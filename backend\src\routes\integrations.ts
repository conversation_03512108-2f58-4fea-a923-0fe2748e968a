import { Router, Request, Response } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { GoogleCalendarService } from '../integrations/calendar/googleCalendar';
import { FitbitService } from '../integrations/fitness/fitbit';
import { prisma } from '../config/database';
import { logger } from '../utils/logger';

const router = Router();
const googleCalendarService = new GoogleCalendarService();
const fitbitService = new FitbitService();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// GET /api/integrations - Get user's integrations
router.get('/', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const integrations = await prisma.integration.findMany({
      where: { userId },
      select: {
        id: true,
        provider: true,
        isActive: true,
        lastSyncAt: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    // Add status information for each integration
    const integrationsWithStatus = await Promise.all(
      integrations.map(async (integration) => {
        let status = 'disconnected';
        let lastSyncStatus = 'never';

        if (integration.isActive) {
          status = 'connected';
          if (integration.lastSyncAt) {
            const hoursSinceSync = (Date.now() - integration.lastSyncAt.getTime()) / (1000 * 60 * 60);
            lastSyncStatus = hoursSinceSync < 24 ? 'recent' : 'stale';
          }
        }

        return {
          ...integration,
          status,
          lastSyncStatus,
        };
      })
    );

    res.json({
      data: integrationsWithStatus,
      count: integrationsWithStatus.length,
    });
  } catch (error) {
    logger.error('Error getting integrations:', error);
    res.status(500).json({
      error: 'Failed to get integrations',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/integrations/available - Get available integrations
router.get('/available', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    // Get user's current integrations
    const userIntegrations = await prisma.integration.findMany({
      where: { userId },
      select: { provider: true, isActive: true },
    });

    const userIntegrationMap = new Map(
      userIntegrations.map(int => [int.provider, int.isActive])
    );

    const availableIntegrations = [
      {
        provider: 'GOOGLE_CALENDAR',
        name: 'Google Calendar',
        description: 'Sync your calendar events and schedule optimization',
        category: 'calendar',
        features: ['Event sync', 'Free/busy checking', 'Automatic scheduling'],
        isConnected: userIntegrationMap.get('GOOGLE_CALENDAR') || false,
        authUrl: googleCalendarService.getAuthUrl(),
      },
      {
        provider: 'FITBIT',
        name: 'Fitbit',
        description: 'Track your activity, sleep, and health metrics',
        category: 'fitness',
        features: ['Activity tracking', 'Sleep analysis', 'Heart rate monitoring'],
        isConnected: userIntegrationMap.get('FITBIT') || false,
        authUrl: fitbitService.getAuthUrl(`fitbit_${userId}_${Date.now()}`),
      },
      {
        provider: 'APPLE_HEALTH',
        name: 'Apple Health',
        description: 'Sync health and fitness data from Apple Health',
        category: 'fitness',
        features: ['Health metrics', 'Activity data', 'Sleep tracking'],
        isConnected: userIntegrationMap.get('APPLE_HEALTH') || false,
        authUrl: null, // Apple Health requires native app integration
      },
      {
        provider: 'SPOTIFY',
        name: 'Spotify',
        description: 'Optimize your music for focus and productivity',
        category: 'music',
        features: ['Focus playlists', 'Mood-based music', 'Productivity tracking'],
        isConnected: userIntegrationMap.get('SPOTIFY') || false,
        authUrl: null, // Would be implemented separately
      },
      {
        provider: 'SLACK',
        name: 'Slack',
        description: 'Manage notifications and focus time',
        category: 'communication',
        features: ['Status management', 'Notification control', 'Focus mode'],
        isConnected: userIntegrationMap.get('SLACK') || false,
        authUrl: null, // Would be implemented separately
      },
    ];

    res.json({
      data: availableIntegrations,
      count: availableIntegrations.length,
    });
  } catch (error) {
    logger.error('Error getting available integrations:', error);
    res.status(500).json({
      error: 'Failed to get available integrations',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// POST /api/integrations/google-calendar/connect - Connect Google Calendar
router.post('/google-calendar/connect',
  [
    body('code').isString().withMessage('Authorization code is required'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { code } = req.body;

      // Exchange code for tokens
      const tokens = await googleCalendarService.exchangeCodeForTokens(code);

      // Store integration
      await googleCalendarService.storeIntegration(userId, tokens);

      res.json({
        message: 'Google Calendar connected successfully',
        provider: 'GOOGLE_CALENDAR',
      });

      logger.info(`Google Calendar connected for user ${userId}`);
    } catch (error) {
      logger.error('Error connecting Google Calendar:', error);
      res.status(500).json({
        error: 'Failed to connect Google Calendar',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// POST /api/integrations/fitbit/connect - Connect Fitbit
router.post('/fitbit/connect',
  [
    body('code').isString().withMessage('Authorization code is required'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { code } = req.body;

      // Exchange code for tokens
      const tokens = await fitbitService.exchangeCodeForTokens(code);

      // Store integration
      await fitbitService.storeIntegration(userId, tokens);

      res.json({
        message: 'Fitbit connected successfully',
        provider: 'FITBIT',
      });

      logger.info(`Fitbit connected for user ${userId}`);
    } catch (error) {
      logger.error('Error connecting Fitbit:', error);
      res.status(500).json({
        error: 'Failed to connect Fitbit',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// DELETE /api/integrations/:provider/disconnect - Disconnect integration
router.delete('/:provider/disconnect',
  [
    param('provider').isIn(['GOOGLE_CALENDAR', 'FITBIT', 'APPLE_HEALTH', 'SPOTIFY', 'SLACK']).withMessage('Invalid provider'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { provider } = req.params;

      // Disconnect based on provider
      switch (provider) {
        case 'GOOGLE_CALENDAR':
          await googleCalendarService.disconnectIntegration(userId);
          break;
        case 'FITBIT':
          await fitbitService.disconnectIntegration(userId);
          break;
        default:
          // For other providers, just deactivate in database
          await prisma.integration.updateMany({
            where: {
              userId,
              provider: provider as any,
            },
            data: {
              isActive: false,
            },
          });
      }

      res.json({
        message: `${provider} disconnected successfully`,
        provider,
      });

      logger.info(`${provider} disconnected for user ${userId}`);
    } catch (error) {
      logger.error(`Error disconnecting ${req.params.provider}:`, error);
      res.status(500).json({
        error: `Failed to disconnect ${req.params.provider}`,
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// POST /api/integrations/:provider/sync - Trigger manual sync
router.post('/:provider/sync',
  [
    param('provider').isIn(['GOOGLE_CALENDAR', 'FITBIT']).withMessage('Invalid provider'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { provider } = req.params;

      let syncResult;

      switch (provider) {
        case 'GOOGLE_CALENDAR':
          await googleCalendarService.syncEvents(userId);
          syncResult = { message: 'Google Calendar events synced successfully' };
          break;
        case 'FITBIT':
          const endDate = new Date();
          const startDate = new Date();
          startDate.setDate(startDate.getDate() - 7); // Sync last 7 days
          await fitbitService.syncData(userId, startDate, endDate);
          syncResult = { message: 'Fitbit data synced successfully' };
          break;
        default:
          return res.status(400).json({
            error: 'Sync not supported',
            message: `Manual sync not supported for ${provider}`,
          });
      }

      res.json({
        ...syncResult,
        provider,
        syncedAt: new Date(),
      });

      logger.info(`${provider} synced for user ${userId}`);
    } catch (error) {
      logger.error(`Error syncing ${req.params.provider}:`, error);
      res.status(500).json({
        error: `Failed to sync ${req.params.provider}`,
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/integrations/google-calendar/events - Get calendar events
router.get('/google-calendar/events',
  [
    query('startDate').isISO8601().withMessage('Start date must be a valid ISO 8601 date'),
    query('endDate').isISO8601().withMessage('End date must be a valid ISO 8601 date'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { startDate, endDate } = req.query;

      const start = new Date(startDate as string);
      const end = new Date(endDate as string);

      const events = await googleCalendarService.getEvents(userId, start, end);

      res.json({
        data: events,
        count: events.length,
        dateRange: {
          startDate: start,
          endDate: end,
        },
      });
    } catch (error) {
      logger.error('Error getting Google Calendar events:', error);
      res.status(500).json({
        error: 'Failed to get calendar events',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/integrations/fitbit/activity - Get Fitbit activity data
router.get('/fitbit/activity',
  [
    query('date').isISO8601().withMessage('Date must be a valid ISO 8601 date'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { date } = req.query;

      const targetDate = new Date(date as string);
      const activityData = await fitbitService.getActivityData(userId, targetDate);

      res.json({
        data: activityData,
      });
    } catch (error) {
      logger.error('Error getting Fitbit activity data:', error);
      res.status(500).json({
        error: 'Failed to get activity data',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/integrations/fitbit/sleep - Get Fitbit sleep data
router.get('/fitbit/sleep',
  [
    query('date').isISO8601().withMessage('Date must be a valid ISO 8601 date'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { date } = req.query;

      const targetDate = new Date(date as string);
      const sleepData = await fitbitService.getSleepData(userId, targetDate);

      if (!sleepData) {
        return res.status(404).json({
          error: 'Sleep data not found',
          message: 'No sleep data available for the specified date',
        });
      }

      res.json({
        data: sleepData,
      });
    } catch (error) {
      logger.error('Error getting Fitbit sleep data:', error);
      res.status(500).json({
        error: 'Failed to get sleep data',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/integrations/status - Get integration status summary
router.get('/status/summary', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const integrations = await prisma.integration.findMany({
      where: { userId },
      select: {
        provider: true,
        isActive: true,
        lastSyncAt: true,
      },
    });

    const statusSummary = {
      totalIntegrations: integrations.length,
      activeIntegrations: integrations.filter(i => i.isActive).length,
      recentlySynced: integrations.filter(i => {
        if (!i.lastSyncAt) return false;
        const hoursSinceSync = (Date.now() - i.lastSyncAt.getTime()) / (1000 * 60 * 60);
        return hoursSinceSync < 24;
      }).length,
      providers: integrations.reduce((acc, integration) => {
        acc[integration.provider] = {
          isActive: integration.isActive,
          lastSyncAt: integration.lastSyncAt,
        };
        return acc;
      }, {} as any),
    };

    res.json({
      data: statusSummary,
    });
  } catch (error) {
    logger.error('Error getting integration status:', error);
    res.status(500).json({
      error: 'Failed to get integration status',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// POST /api/integrations/webhook/:provider - Handle webhooks (for real-time updates)
router.post('/webhook/:provider',
  [
    param('provider').isIn(['GOOGLE_CALENDAR', 'FITBIT']).withMessage('Invalid provider'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { provider } = req.params;
      const webhookData = req.body;

      // Handle webhook based on provider
      switch (provider) {
        case 'GOOGLE_CALENDAR':
          // Handle Google Calendar webhook
          logger.info('Google Calendar webhook received:', webhookData);
          // In a real implementation, you would process the webhook data
          // and trigger appropriate sync operations
          break;
        case 'FITBIT':
          // Handle Fitbit webhook
          logger.info('Fitbit webhook received:', webhookData);
          // Process Fitbit subscription notifications
          break;
      }

      res.status(200).json({
        message: 'Webhook processed successfully',
        provider,
      });
    } catch (error) {
      logger.error(`Error processing ${req.params.provider} webhook:`, error);
      res.status(500).json({
        error: 'Failed to process webhook',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

export default router;
