'use client';

import React from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import { Sun, Moon, Sunrise, Sunset, TrendingUp, TrendingDown } from 'lucide-react';
import { CircadianPattern as CircadianPatternType } from '@/types/api';
import { cn } from '@/lib/utils';

interface CircadianPatternProps {
  pattern: CircadianPatternType;
  height?: number;
}

export function CircadianPattern({ pattern, height = 300 }: CircadianPatternProps) {
  // Transform hourly data for the chart
  const chartData = React.useMemo(() => {
    const data = [];
    for (let hour = 0; hour < 24; hour++) {
      const energy = pattern.averageEnergyByHour[hour] || 0;
      const isPeak = pattern.peakEnergyHours.includes(hour);
      const isLow = pattern.lowEnergyHours.includes(hour);
      
      data.push({
        hour,
        energy,
        isPeak,
        isLow,
        timeLabel: hour === 0 ? '12 AM' : 
                  hour < 12 ? `${hour} AM` : 
                  hour === 12 ? '12 PM' : 
                  `${hour - 12} PM`,
        period: hour < 6 ? 'night' :
                hour < 12 ? 'morning' :
                hour < 18 ? 'afternoon' : 'evening',
      });
    }
    return data;
  }, [pattern]);

  // Get color for each bar based on energy level and peak/low status
  const getBarColor = (entry: any) => {
    if (entry.isPeak) return '#10b981'; // Green for peaks
    if (entry.isLow) return '#ef4444'; // Red for lows
    if (entry.energy >= 4) return '#3b82f6'; // Blue for high energy
    if (entry.energy >= 3) return '#f59e0b'; // Yellow for medium energy
    return '#6b7280'; // Gray for low energy
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            {data.timeLabel}
          </p>
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 dark:text-gray-400">Average Energy:</span>
              <span className="text-xs font-medium text-gray-900 dark:text-white">
                {data.energy.toFixed(1)}/5
              </span>
            </div>
            {data.isPeak && (
              <div className="flex items-center space-x-1">
                <TrendingUp className="w-3 h-3 text-green-500" />
                <span className="text-xs text-green-600 dark:text-green-400">Peak Hour</span>
              </div>
            )}
            {data.isLow && (
              <div className="flex items-center space-x-1">
                <TrendingDown className="w-3 h-3 text-red-500" />
                <span className="text-xs text-red-600 dark:text-red-400">Low Energy Hour</span>
              </div>
            )}
          </div>
        </div>
      );
    }
    return null;
  };

  // Calculate insights
  const peakHour = pattern.peakEnergyHours[0];
  const lowHour = pattern.lowEnergyHours[0];
  const morningEnergy = chartData.slice(6, 12).reduce((sum, d) => sum + d.energy, 0) / 6;
  const afternoonEnergy = chartData.slice(12, 18).reduce((sum, d) => sum + d.energy, 0) / 6;
  const eveningEnergy = chartData.slice(18, 24).reduce((sum, d) => sum + d.energy, 0) / 6;

  const getPeriodIcon = (period: string) => {
    switch (period) {
      case 'morning': return Sunrise;
      case 'afternoon': return Sun;
      case 'evening': return Sunset;
      default: return Moon;
    }
  };

  return (
    <div className="space-y-6">
      {/* Pattern Overview */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <TrendingUp className="w-6 h-6 text-green-600 mx-auto mb-1" />
          <div className="text-sm font-medium text-green-800 dark:text-green-300">
            Peak Hour
          </div>
          <div className="text-xs text-green-600 dark:text-green-400">
            {peakHour !== undefined ? 
              (peakHour === 0 ? '12 AM' : 
               peakHour < 12 ? `${peakHour} AM` : 
               peakHour === 12 ? '12 PM' : 
               `${peakHour - 12} PM`) : 'N/A'}
          </div>
        </div>

        <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
          <TrendingDown className="w-6 h-6 text-red-600 mx-auto mb-1" />
          <div className="text-sm font-medium text-red-800 dark:text-red-300">
            Low Hour
          </div>
          <div className="text-xs text-red-600 dark:text-red-400">
            {lowHour !== undefined ? 
              (lowHour === 0 ? '12 AM' : 
               lowHour < 12 ? `${lowHour} AM` : 
               lowHour === 12 ? '12 PM' : 
               `${lowHour - 12} PM`) : 'N/A'}
          </div>
        </div>

        <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="text-lg font-bold text-blue-800 dark:text-blue-300">
            {pattern.confidence.toFixed(0)}%
          </div>
          <div className="text-xs text-blue-600 dark:text-blue-400">
            Pattern Confidence
          </div>
        </div>

        <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div className="text-lg font-bold text-purple-800 dark:text-purple-300">
            {pattern.dataPoints}
          </div>
          <div className="text-xs text-purple-600 dark:text-purple-400">
            Data Points
          </div>
        </div>
      </div>

      {/* Hourly Energy Chart */}
      <div className="w-full" style={{ height }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="hour"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 10, fill: 'currentColor' }}
              className="text-gray-600 dark:text-gray-400"
              tickFormatter={(hour) => 
                hour === 0 ? '12a' : 
                hour < 12 ? `${hour}a` : 
                hour === 12 ? '12p' : 
                `${hour - 12}p`
              }
            />
            <YAxis 
              domain={[0, 5]}
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 10, fill: 'currentColor' }}
              className="text-gray-600 dark:text-gray-400"
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar dataKey="energy" radius={[2, 2, 0, 0]}>
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={getBarColor(entry)} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Period Averages */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        {[
          { period: 'Morning', value: morningEnergy, icon: Sunrise, color: 'text-yellow-600', bg: 'bg-yellow-50 dark:bg-yellow-900/20' },
          { period: 'Afternoon', value: afternoonEnergy, icon: Sun, color: 'text-orange-600', bg: 'bg-orange-50 dark:bg-orange-900/20' },
          { period: 'Evening', value: eveningEnergy, icon: Sunset, color: 'text-purple-600', bg: 'bg-purple-50 dark:bg-purple-900/20' },
        ].map(({ period, value, icon: Icon, color, bg }) => (
          <div key={period} className={cn('p-4 rounded-lg', bg)}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Icon className={cn('w-5 h-5', color)} />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {period}
                </span>
              </div>
              <span className={cn('text-lg font-bold', color)}>
                {value.toFixed(1)}
              </span>
            </div>
            <div className="mt-2">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className={cn('h-2 rounded-full', color.replace('text-', 'bg-'))}
                  style={{ width: `${(value / 5) * 100}%` }}
                />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Insights */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-3">
          Pattern Insights
        </h4>
        <div className="space-y-2 text-sm text-blue-700 dark:text-blue-400">
          {morningEnergy > afternoonEnergy && morningEnergy > eveningEnergy && (
            <p>• You're a morning person! Your energy peaks in the morning hours.</p>
          )}
          {afternoonEnergy > morningEnergy && afternoonEnergy > eveningEnergy && (
            <p>• You have strong afternoon energy. Consider scheduling important tasks after lunch.</p>
          )}
          {eveningEnergy > morningEnergy && eveningEnergy > afternoonEnergy && (
            <p>• You're a night owl! Your energy is highest in the evening hours.</p>
          )}
          {pattern.confidence > 80 && (
            <p>• Your energy pattern is highly consistent and predictable.</p>
          )}
          {pattern.confidence < 50 && (
            <p>• Your energy pattern varies significantly. Consider tracking more consistently.</p>
          )}
          {pattern.peakEnergyHours.length > 1 && (
            <p>• You have multiple peak energy periods throughout the day.</p>
          )}
        </div>
      </div>

      {/* Legend */}
      <div className="flex flex-wrap items-center justify-center gap-4 text-xs text-gray-600 dark:text-gray-400">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-500 rounded"></div>
          <span>Peak Hours</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-500 rounded"></div>
          <span>Low Energy Hours</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-500 rounded"></div>
          <span>High Energy</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-yellow-500 rounded"></div>
          <span>Medium Energy</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-gray-500 rounded"></div>
          <span>Low Energy</span>
        </div>
      </div>
    </div>
  );
}
