'use client';

import React, { useState } from 'react';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  AreaChart,
  Area,
  ScatterChart,
  Scatter,
  ReferenceLine
} from 'recharts';
import { format, parseISO, startOfDay, endOfDay, eachHourOfInterval } from 'date-fns';
import { BarChart3, TrendingUp, Activity } from 'lucide-react';
import { EnergyData } from '@/types/api';
import { getEnergyLevelLabel, cn } from '@/lib/utils';

interface EnergyChartProps {
  data: EnergyData[];
  period: 'week' | 'month' | 'quarter';
  height?: number;
}

type ChartType = 'line' | 'area' | 'scatter';

export function EnergyChart({ data, period, height = 400 }: EnergyChartProps) {
  const [chartType, setChartType] = useState<ChartType>('line');
  const [showMood, setShowMood] = useState(false);
  const [showStress, setShowStress] = useState(false);

  // Transform data for the chart
  const chartData = React.useMemo(() => {
    if (!data || data.length === 0) return [];

    return data
      .slice()
      .reverse() // Show oldest to newest
      .map((entry, index) => ({
        date: period === 'week' 
          ? format(parseISO(entry.timestamp), 'EEE')
          : period === 'month'
          ? format(parseISO(entry.timestamp), 'MMM dd')
          : format(parseISO(entry.timestamp), 'MMM dd'),
        fullDate: format(parseISO(entry.timestamp), 'MMM dd, yyyy'),
        time: format(parseISO(entry.timestamp), 'h:mm a'),
        energy: entry.energyLevel,
        mood: entry.mood / 2, // Scale mood to 1-5 for better visualization
        stress: entry.stressLevel / 2, // Scale stress to 1-5
        sleepQuality: entry.sleepQuality ? entry.sleepQuality / 2 : null,
        physicalActivity: entry.physicalActivity ? entry.physicalActivity / 2 : null,
        caffeine: entry.caffeine || 0,
        context: entry.context,
        timestamp: entry.timestamp,
        index,
      }));
  }, [data, period]);

  // Calculate average energy for reference line
  const averageEnergy = chartData.length > 0 
    ? chartData.reduce((sum, entry) => sum + entry.energy, 0) / chartData.length 
    : 0;

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 min-w-[200px]">
          <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            {data.fullDate} at {data.time}
          </p>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 dark:text-gray-400">Energy:</span>
              <span className="text-xs font-medium text-blue-600 dark:text-blue-400">
                {getEnergyLevelLabel(data.energy)} ({data.energy}/5)
              </span>
            </div>
            {showMood && (
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600 dark:text-gray-400">Mood:</span>
                <span className="text-xs font-medium text-pink-600 dark:text-pink-400">
                  {(data.mood * 2).toFixed(1)}/10
                </span>
              </div>
            )}
            {showStress && (
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600 dark:text-gray-400">Stress:</span>
                <span className="text-xs font-medium text-red-600 dark:text-red-400">
                  {(data.stress * 2).toFixed(1)}/10
                </span>
              </div>
            )}
            {data.context && (
              <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {data.context}
                </p>
              </div>
            )}
          </div>
        </div>
      );
    }
    return null;
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
        <div className="text-center">
          <Activity className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p className="text-sm">No energy data available</p>
          <p className="text-xs mt-1">Start logging your energy levels to see patterns</p>
        </div>
      </div>
    );
  }

  const renderChart = () => {
    const commonProps = {
      data: chartData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 },
    };

    switch (chartType) {
      case 'area':
        return (
          <AreaChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="date" 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: 'currentColor' }}
              className="text-gray-600 dark:text-gray-400"
            />
            <YAxis 
              domain={[0, 5]}
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: 'currentColor' }}
              className="text-gray-600 dark:text-gray-400"
            />
            <Tooltip content={<CustomTooltip />} />
            <ReferenceLine y={averageEnergy} stroke="#6b7280" strokeDasharray="5 5" />
            <Area
              type="monotone"
              dataKey="energy"
              stroke="#0ea5e9"
              fill="#0ea5e9"
              fillOpacity={0.2}
              strokeWidth={2}
            />
            {showMood && (
              <Area
                type="monotone"
                dataKey="mood"
                stroke="#ec4899"
                fill="#ec4899"
                fillOpacity={0.1}
                strokeWidth={1}
              />
            )}
            {showStress && (
              <Area
                type="monotone"
                dataKey="stress"
                stroke="#ef4444"
                fill="#ef4444"
                fillOpacity={0.1}
                strokeWidth={1}
              />
            )}
          </AreaChart>
        );

      case 'scatter':
        return (
          <ScatterChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="index" 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: 'currentColor' }}
              className="text-gray-600 dark:text-gray-400"
              tickFormatter={(value) => chartData[value]?.date || ''}
            />
            <YAxis 
              domain={[0, 5]}
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: 'currentColor' }}
              className="text-gray-600 dark:text-gray-400"
            />
            <Tooltip content={<CustomTooltip />} />
            <ReferenceLine y={averageEnergy} stroke="#6b7280" strokeDasharray="5 5" />
            <Scatter dataKey="energy" fill="#0ea5e9" />
          </ScatterChart>
        );

      default: // line
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="date" 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: 'currentColor' }}
              className="text-gray-600 dark:text-gray-400"
            />
            <YAxis 
              domain={[0, 5]}
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: 'currentColor' }}
              className="text-gray-600 dark:text-gray-400"
            />
            <Tooltip content={<CustomTooltip />} />
            <ReferenceLine y={averageEnergy} stroke="#6b7280" strokeDasharray="5 5" />
            <Line
              type="monotone"
              dataKey="energy"
              stroke="#0ea5e9"
              strokeWidth={3}
              dot={{ fill: '#0ea5e9', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#0ea5e9', strokeWidth: 2 }}
            />
            {showMood && (
              <Line
                type="monotone"
                dataKey="mood"
                stroke="#ec4899"
                strokeWidth={2}
                dot={{ fill: '#ec4899', strokeWidth: 1, r: 3 }}
                strokeDasharray="5 5"
              />
            )}
            {showStress && (
              <Line
                type="monotone"
                dataKey="stress"
                stroke="#ef4444"
                strokeWidth={2}
                dot={{ fill: '#ef4444', strokeWidth: 1, r: 3 }}
                strokeDasharray="3 3"
              />
            )}
          </LineChart>
        );
    }
  };

  return (
    <div className="space-y-4">
      {/* Chart Controls */}
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600 dark:text-gray-400">Chart type:</span>
          <div className="flex items-center space-x-1">
            {[
              { type: 'line' as ChartType, icon: TrendingUp, label: 'Line' },
              { type: 'area' as ChartType, icon: BarChart3, label: 'Area' },
              { type: 'scatter' as ChartType, icon: Activity, label: 'Scatter' },
            ].map(({ type, icon: Icon, label }) => (
              <button
                key={type}
                onClick={() => setChartType(type)}
                className={cn(
                  'p-2 rounded-md text-xs transition-colors',
                  chartType === type
                    ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400'
                )}
                title={label}
              >
                <Icon className="w-4 h-4" />
              </button>
            ))}
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <label className="flex items-center space-x-2 text-sm">
            <input
              type="checkbox"
              checked={showMood}
              onChange={(e) => setShowMood(e.target.checked)}
              className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
            />
            <span className="text-gray-600 dark:text-gray-400">Show Mood</span>
          </label>
          
          <label className="flex items-center space-x-2 text-sm">
            <input
              type="checkbox"
              checked={showStress}
              onChange={(e) => setShowStress(e.target.checked)}
              className="rounded border-gray-300 text-red-600 focus:ring-red-500"
            />
            <span className="text-gray-600 dark:text-gray-400">Show Stress</span>
          </label>
        </div>
      </div>

      {/* Chart */}
      <div className="w-full" style={{ height }}>
        <ResponsiveContainer width="100%" height="100%">
          {renderChart()}
        </ResponsiveContainer>
      </div>

      {/* Legend */}
      <div className="flex flex-wrap items-center justify-center gap-6 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
          <span className="text-gray-600 dark:text-gray-400">Energy Level</span>
        </div>
        
        {showMood && (
          <div className="flex items-center space-x-2">
            <div className="w-3 h-0.5 bg-pink-500"></div>
            <span className="text-gray-600 dark:text-gray-400">Mood (scaled)</span>
          </div>
        )}
        
        {showStress && (
          <div className="flex items-center space-x-2">
            <div className="w-3 h-0.5 bg-red-500 border-dashed border-t"></div>
            <span className="text-gray-600 dark:text-gray-400">Stress (scaled)</span>
          </div>
        )}
        
        <div className="flex items-center space-x-2">
          <div className="w-3 h-0.5 bg-gray-500 border-dashed border-t"></div>
          <span className="text-gray-600 dark:text-gray-400">Average ({averageEnergy.toFixed(1)})</span>
        </div>
      </div>
    </div>
  );
}
