'use client';

import React, { useState } from 'react';
import { Link, Search, CheckCircle, Calendar, Clock } from 'lucide-react';
import { useTasks } from '@/hooks/useApi';
import { cn } from '@/lib/utils';

interface Task {
  id: string;
  title: string;
  description?: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  dueDate?: string;
  estimatedDuration?: number; // in minutes
}

interface TaskLinkerProps {
  selectedTaskId?: string;
  onTaskSelect: (taskId: string | null) => void;
  className?: string;
  compact?: boolean;
}

export function TaskLinker({ selectedTaskId, onTaskSelect, className, compact = false }: TaskLinkerProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  
  const { data: tasksResponse } = useTasks({ 
    status: 'PENDING,IN_PROGRESS', 
    limit: 20 
  });
  
  const tasks = tasksResponse?.data || [];
  
  const filteredTasks = tasks.filter(task =>
    task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    task.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const selectedTask = tasks.find(task => task.id === selectedTaskId);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20';
      case 'HIGH':
        return 'text-orange-600 dark:text-orange-400 bg-orange-100 dark:bg-orange-900/20';
      case 'MEDIUM':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20';
      case 'LOW':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  if (compact) {
    return (
      <div className={cn('relative', className)}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center space-x-2 px-3 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors w-full text-left"
        >
          <Link className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-700 dark:text-gray-300 truncate">
            {selectedTask ? selectedTask.title : 'Link to task (optional)'}
          </span>
        </button>

        {isOpen && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-64 overflow-hidden">
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search tasks..."
                  className="w-full pl-9 pr-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            
            <div className="max-h-48 overflow-y-auto">
              <button
                onClick={() => {
                  onTaskSelect(null);
                  setIsOpen(false);
                }}
                className="w-full flex items-center space-x-3 px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-left"
              >
                <div className="w-6 h-6 rounded flex items-center justify-center bg-gray-100 dark:bg-gray-600">
                  <Link className="w-3 h-3 text-gray-400" />
                </div>
                <span className="text-sm text-gray-500 dark:text-gray-400">No task linked</span>
              </button>
              
              {filteredTasks.map((task) => (
                <button
                  key={task.id}
                  onClick={() => {
                    onTaskSelect(task.id);
                    setIsOpen(false);
                  }}
                  className="w-full flex items-center space-x-3 px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-left"
                >
                  <div className={cn('w-6 h-6 rounded flex items-center justify-center text-xs font-medium', getPriorityColor(task.priority))}>
                    {task.priority[0]}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {task.title}
                    </div>
                    {task.estimatedDuration && (
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        ~{task.estimatedDuration} min
                      </div>
                    )}
                  </div>
                  {selectedTaskId === task.id && (
                    <CheckCircle className="w-4 h-4 text-blue-500" />
                  )}
                </button>
              ))}
              
              {filteredTasks.length === 0 && searchQuery && (
                <div className="px-3 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                  No tasks found matching "{searchQuery}"
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn('bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6', className)}>
      <div className="flex items-center space-x-3 mb-4">
        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
          <Link className="w-5 h-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Link to Task
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Connect this session to a specific task (optional)
          </p>
        </div>
      </div>

      {/* Search */}
      <div className="mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search tasks..."
            className="w-full pl-9 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Selected Task */}
      {selectedTask && (
        <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={cn('w-6 h-6 rounded flex items-center justify-center text-xs font-medium', getPriorityColor(selectedTask.priority))}>
                {selectedTask.priority[0]}
              </div>
              <div>
                <div className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  {selectedTask.title}
                </div>
                {selectedTask.estimatedDuration && (
                  <div className="text-xs text-blue-700 dark:text-blue-300">
                    Estimated: {selectedTask.estimatedDuration} minutes
                  </div>
                )}
              </div>
            </div>
            <button
              onClick={() => onTaskSelect(null)}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
            >
              Remove
            </button>
          </div>
        </div>
      )}

      {/* Task List */}
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {!selectedTask && (
          <button
            onClick={() => onTaskSelect(null)}
            className={cn(
              'w-full flex items-center space-x-3 p-3 rounded-lg border-2 transition-colors text-left',
              !selectedTaskId
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
            )}
          >
            <div className="w-8 h-8 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
              <Link className="w-4 h-4 text-gray-400" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                No task linked
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Focus session without specific task
              </div>
            </div>
          </button>
        )}

        {filteredTasks.map((task) => (
          <button
            key={task.id}
            onClick={() => onTaskSelect(task.id)}
            className={cn(
              'w-full flex items-center space-x-3 p-3 rounded-lg border-2 transition-colors text-left',
              selectedTaskId === task.id
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
            )}
          >
            <div className={cn('w-8 h-8 rounded-lg flex items-center justify-center text-sm font-medium', getPriorityColor(task.priority))}>
              {task.priority[0]}
            </div>
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                {task.title}
              </div>
              <div className="flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400">
                {task.estimatedDuration && (
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{task.estimatedDuration} min</span>
                  </div>
                )}
                {task.dueDate && (
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-3 h-3" />
                    <span>Due {new Date(task.dueDate).toLocaleDateString()}</span>
                  </div>
                )}
              </div>
            </div>
            {selectedTaskId === task.id && (
              <CheckCircle className="w-5 h-5 text-blue-500" />
            )}
          </button>
        ))}

        {filteredTasks.length === 0 && searchQuery && (
          <div className="text-center py-8">
            <div className="text-gray-500 dark:text-gray-400">
              No tasks found matching "{searchQuery}"
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
