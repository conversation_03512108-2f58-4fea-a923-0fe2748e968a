import { Router, Request, Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { cacheService, CacheKeys } from '../config/redis';
import { logger } from '../utils/logger';

const router = Router();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// GET /api/users/profile - Get user profile
router.get('/profile', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        displayName: true,
        avatar: true,
        timezone: true,
        role: true,
        isActive: true,
        isEmailVerified: true,
        isPremium: true,
        onboardingCompleted: true,
        createdAt: true,
        lastLoginAt: true,
      },
    });

    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User profile not found',
      });
    }

    res.json({
      data: user,
    });
  } catch (error) {
    logger.error('Error getting user profile:', error);
    res.status(500).json({
      error: 'Failed to get user profile',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// PUT /api/users/profile - Update user profile
router.put('/profile',
  [
    body('firstName').optional().isString().trim().isLength({ min: 1, max: 50 }).withMessage('First name must be between 1 and 50 characters'),
    body('lastName').optional().isString().trim().isLength({ min: 1, max: 50 }).withMessage('Last name must be between 1 and 50 characters'),
    body('displayName').optional().isString().trim().isLength({ min: 1, max: 100 }).withMessage('Display name must be between 1 and 100 characters'),
    body('timezone').optional().isString().custom((value) => {
      try {
        Intl.DateTimeFormat(undefined, { timeZone: value });
        return true;
      } catch (error) {
        throw new Error('Invalid timezone');
      }
    }),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const updates = req.body;

      // Generate display name if not provided but first/last name are
      if ((updates.firstName || updates.lastName) && !updates.displayName) {
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { firstName: true, lastName: true },
        });

        const firstName = updates.firstName || user?.firstName || '';
        const lastName = updates.lastName || user?.lastName || '';
        updates.displayName = `${firstName} ${lastName}`.trim();
      }

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: updates,
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          displayName: true,
          avatar: true,
          timezone: true,
          role: true,
          isActive: true,
          isEmailVerified: true,
          isPremium: true,
          onboardingCompleted: true,
          createdAt: true,
          lastLoginAt: true,
        },
      });

      // Clear user cache
      const cacheKey = CacheKeys.user(userId);
      await cacheService.del(cacheKey);

      res.json({
        message: 'Profile updated successfully',
        data: updatedUser,
      });

      logger.info(`Profile updated for user ${userId}`);
    } catch (error) {
      logger.error('Error updating user profile:', error);
      res.status(500).json({
        error: 'Failed to update profile',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/users/preferences - Get user preferences
router.get('/preferences', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const preferences = await prisma.userPreferences.findUnique({
      where: { userId },
    });

    if (!preferences) {
      // Create default preferences if they don't exist
      const defaultPreferences = await prisma.userPreferences.create({
        data: {
          userId,
          emailNotifications: true,
          pushNotifications: true,
          theme: 'light',
          language: 'en',
          workingHours: { start: '09:00', end: '17:00' },
          workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        },
      });

      return res.json({
        data: defaultPreferences,
      });
    }

    res.json({
      data: preferences,
    });
  } catch (error) {
    logger.error('Error getting user preferences:', error);
    res.status(500).json({
      error: 'Failed to get preferences',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// PUT /api/users/preferences - Update user preferences
router.put('/preferences',
  [
    body('emailNotifications').optional().isBoolean().withMessage('Email notifications must be a boolean'),
    body('pushNotifications').optional().isBoolean().withMessage('Push notifications must be a boolean'),
    body('smsNotifications').optional().isBoolean().withMessage('SMS notifications must be a boolean'),
    body('dataSharing').optional().isBoolean().withMessage('Data sharing must be a boolean'),
    body('analyticsOptIn').optional().isBoolean().withMessage('Analytics opt-in must be a boolean'),
    body('theme').optional().isIn(['light', 'dark', 'auto']).withMessage('Theme must be light, dark, or auto'),
    body('language').optional().isString().isLength({ min: 2, max: 5 }).withMessage('Language must be a valid language code'),
    body('workingHours').optional().isObject().withMessage('Working hours must be an object'),
    body('workingDays').optional().isArray().withMessage('Working days must be an array'),
    body('energyReminderFreq').optional().isInt({ min: 1, max: 24 }).withMessage('Energy reminder frequency must be between 1 and 24 hours'),
    body('energyReminderEnabled').optional().isBoolean().withMessage('Energy reminder enabled must be a boolean'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const updates = req.body;

      const updatedPreferences = await prisma.userPreferences.upsert({
        where: { userId },
        update: updates,
        create: {
          userId,
          ...updates,
        },
      });

      res.json({
        message: 'Preferences updated successfully',
        data: updatedPreferences,
      });

      logger.info(`Preferences updated for user ${userId}`);
    } catch (error) {
      logger.error('Error updating user preferences:', error);
      res.status(500).json({
        error: 'Failed to update preferences',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// POST /api/users/onboarding/complete - Complete onboarding
router.post('/onboarding/complete', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { onboardingCompleted: true },
      select: {
        id: true,
        onboardingCompleted: true,
      },
    });

    // Clear user cache
    const cacheKey = CacheKeys.user(userId);
    await cacheService.del(cacheKey);

    res.json({
      message: 'Onboarding completed successfully',
      data: updatedUser,
    });

    logger.info(`Onboarding completed for user ${userId}`);
  } catch (error) {
    logger.error('Error completing onboarding:', error);
    res.status(500).json({
      error: 'Failed to complete onboarding',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/users/dashboard - Get dashboard data
router.get('/dashboard', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    // Get user profile
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        displayName: true,
        onboardingCompleted: true,
        isPremium: true,
      },
    });

    // Get recent activity counts
    const today = new Date();
    const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    const [
      recentEnergyData,
      recentFlowSessions,
      activeHabits,
      pendingTasks,
      completedTasks,
    ] = await Promise.all([
      prisma.energyData.count({
        where: {
          userId,
          timestamp: { gte: sevenDaysAgo },
        },
      }),
      prisma.flowSession.count({
        where: {
          userId,
          startTime: { gte: sevenDaysAgo },
        },
      }),
      prisma.habit.count({
        where: {
          userId,
          isActive: true,
        },
      }),
      prisma.task.count({
        where: {
          userId,
          status: { in: ['PENDING', 'IN_PROGRESS'] },
        },
      }),
      prisma.task.count({
        where: {
          userId,
          status: 'COMPLETED',
          completedAt: { gte: sevenDaysAgo },
        },
      }),
    ]);

    // Get recent coaching sessions
    const recentCoachingSessions = await prisma.coachingSession.count({
      where: {
        userId,
        createdAt: { gte: sevenDaysAgo },
      },
    });

    // Get active integrations
    const activeIntegrations = await prisma.integration.count({
      where: {
        userId,
        isActive: true,
      },
    });

    const dashboardData = {
      user,
      stats: {
        recentEnergyLogs: recentEnergyData,
        recentFlowSessions,
        activeHabits,
        pendingTasks,
        completedTasksThisWeek: completedTasks,
        recentCoachingSessions,
        activeIntegrations,
      },
      needsAttention: {
        onboardingIncomplete: !user?.onboardingCompleted,
        noRecentEnergyData: recentEnergyData === 0,
        noActiveHabits: activeHabits === 0,
        manyPendingTasks: pendingTasks > 10,
      },
    };

    res.json({
      data: dashboardData,
    });
  } catch (error) {
    logger.error('Error getting dashboard data:', error);
    res.status(500).json({
      error: 'Failed to get dashboard data',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/users/stats - Get user statistics
router.get('/stats',
  [
    query('period').optional().isIn(['week', 'month', 'quarter', 'year']).withMessage('Period must be week, month, quarter, or year'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const period = (req.query.period as string) || 'month';

      const endDate = new Date();
      const startDate = new Date();

      switch (period) {
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(startDate.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
      }

      const [
        energyDataCount,
        flowSessionsCount,
        totalFlowTime,
        habitsCount,
        habitCompletions,
        tasksCreated,
        tasksCompleted,
        coachingSessions,
      ] = await Promise.all([
        prisma.energyData.count({
          where: {
            userId,
            timestamp: { gte: startDate, lte: endDate },
          },
        }),
        prisma.flowSession.count({
          where: {
            userId,
            startTime: { gte: startDate, lte: endDate },
            endTime: { not: null },
          },
        }),
        prisma.flowSession.aggregate({
          where: {
            userId,
            startTime: { gte: startDate, lte: endDate },
            endTime: { not: null },
          },
          _sum: { duration: true },
        }),
        prisma.habit.count({
          where: {
            userId,
            createdAt: { gte: startDate, lte: endDate },
          },
        }),
        prisma.habitCompletion.count({
          where: {
            habit: { userId },
            completedAt: { gte: startDate, lte: endDate },
          },
        }),
        prisma.task.count({
          where: {
            userId,
            createdAt: { gte: startDate, lte: endDate },
          },
        }),
        prisma.task.count({
          where: {
            userId,
            status: 'COMPLETED',
            completedAt: { gte: startDate, lte: endDate },
          },
        }),
        prisma.coachingSession.count({
          where: {
            userId,
            createdAt: { gte: startDate, lte: endDate },
          },
        }),
      ]);

      const stats = {
        period,
        dateRange: { startDate, endDate },
        energy: {
          dataPoints: energyDataCount,
          averagePerDay: Math.round((energyDataCount / Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))) * 100) / 100,
        },
        flow: {
          sessions: flowSessionsCount,
          totalMinutes: totalFlowTime._sum.duration || 0,
          averageSessionLength: flowSessionsCount > 0 ? Math.round((totalFlowTime._sum.duration || 0) / flowSessionsCount) : 0,
        },
        habits: {
          created: habitsCount,
          completions: habitCompletions,
          averageCompletionsPerDay: Math.round((habitCompletions / Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))) * 100) / 100,
        },
        tasks: {
          created: tasksCreated,
          completed: tasksCompleted,
          completionRate: tasksCreated > 0 ? Math.round((tasksCompleted / tasksCreated) * 100) : 0,
        },
        coaching: {
          sessions: coachingSessions,
        },
      };

      res.json({
        data: stats,
      });
    } catch (error) {
      logger.error('Error getting user statistics:', error);
      res.status(500).json({
        error: 'Failed to get user statistics',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// DELETE /api/users/account - Delete user account
router.delete('/account', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    // In a production environment, you might want to:
    // 1. Soft delete instead of hard delete
    // 2. Anonymize data instead of deleting
    // 3. Send confirmation email
    // 4. Have a grace period for account recovery

    // For now, we'll just deactivate the account
    await prisma.user.update({
      where: { id: userId },
      data: {
        isActive: false,
        email: `deleted_${userId}@deleted.com`, // Anonymize email
      },
    });

    // Clear user cache
    const cacheKey = CacheKeys.user(userId);
    await cacheService.del(cacheKey);

    res.json({
      message: 'Account deactivated successfully',
    });

    logger.info(`Account deactivated for user ${userId}`);
  } catch (error) {
    logger.error('Error deactivating account:', error);
    res.status(500).json({
      error: 'Failed to deactivate account',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/users/export - Export user data
router.get('/export', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    // Get all user data
    const [
      user,
      preferences,
      energyData,
      flowSessions,
      habits,
      tasks,
      integrations,
    ] = await Promise.all([
      prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          displayName: true,
          timezone: true,
          createdAt: true,
        },
      }),
      prisma.userPreferences.findUnique({
        where: { userId },
      }),
      prisma.energyData.findMany({
        where: { userId },
        orderBy: { timestamp: 'desc' },
      }),
      prisma.flowSession.findMany({
        where: { userId },
        orderBy: { startTime: 'desc' },
      }),
      prisma.habit.findMany({
        where: { userId },
        include: { completions: true },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.task.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.integration.findMany({
        where: { userId },
        select: {
          provider: true,
          isActive: true,
          createdAt: true,
          lastSyncAt: true,
        },
      }),
    ]);

    const exportData = {
      exportedAt: new Date(),
      user,
      preferences,
      energyData,
      flowSessions,
      habits,
      tasks,
      integrations,
    };

    res.json({
      data: exportData,
    });

    logger.info(`Data exported for user ${userId}`);
  } catch (error) {
    logger.error('Error exporting user data:', error);
    res.status(500).json({
      error: 'Failed to export data',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export default router;
