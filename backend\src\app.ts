import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';

// Import middleware
import { errorHand<PERSON>, notFoundHandler } from './middleware/errorHandler';
import { authenticateToken } from './middleware/auth';
import { createLoggingMiddleware } from './middleware/logging';

// Import routes
import authRoutes from './routes/auth';
import usersRoutes from './routes/users';
import energyRoutes from './routes/energy';
import flowRoutes from './routes/flow';
import tasksRoutes from './routes/tasks';
import habitsRoutes from './routes/habits';
import coachingRoutes from './routes/coaching';
import integrationsRoutes from './routes/integrations';

// Import configuration
import { config } from './config/database';
import { cacheService } from './config/redis';

class App {
  public app: express.Application;
  public server: any;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);

    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // Enhanced CORS configuration
    const corsOptions = {
      origin: function (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) {
        // Allow requests with no origin (mobile apps, Postman, etc.)
        if (!origin) return callback(null, true);

        const allowedOrigins = [
          process.env.FRONTEND_URL || 'http://localhost:3000',
          'http://localhost:3001', // Development frontend
          'https://rhythmai.app', // Production domain
          'https://www.rhythmai.app',
        ];

        if (allowedOrigins.includes(origin)) {
          callback(null, true);
        } else {
          callback(new Error('Not allowed by CORS'), false);
        }
      },
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'X-Request-ID',
        'Accept',
        'Origin',
      ],
      exposedHeaders: ['X-Request-ID'],
      maxAge: 86400, // 24 hours
    };

    this.app.use(cors(corsOptions));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: process.env.NODE_ENV === 'production' ? 100 : 1000, // requests per window
      message: {
        error: 'Too many requests from this IP, please try again later.',
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api/', limiter);

    // Stricter rate limiting for auth endpoints
    const authLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // 5 attempts per window
      message: {
        error: 'Too many authentication attempts, please try again later.',
      },
    });
    this.app.use('/api/auth/login', authLimiter);
    this.app.use('/api/auth/register', authLimiter);

    // Body parsing and compression
    this.app.use(compression());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Logging middleware
    this.app.use(createLoggingMiddleware());

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV,
        version: process.env.npm_package_version || '1.0.0',
      });
    });

    // API documentation endpoint
    this.app.get('/api', (req, res) => {
      res.json({
        name: 'RhythmAI API',
        version: '1.0.0',
        description: 'Rhythm-aware productivity system API',
        documentation: '/api/docs',
        endpoints: {
          auth: '/api/auth',
          users: '/api/users',
          energy: '/api/energy',
          flow: '/api/flow',
          tasks: '/api/tasks',
          habits: '/api/habits',
          coaching: '/api/coaching',
          integrations: '/api/integrations',
        },
      });
    });
  }

  private initializeRoutes(): void {
    // Public routes (no authentication required)
    this.app.use('/api/auth', authRoutes);

    // Protected routes (authentication required)
    this.app.use('/api/users', authenticateToken, usersRoutes);
    this.app.use('/api/energy', authenticateToken, energyRoutes);
    this.app.use('/api/flow', authenticateToken, flowRoutes);
    this.app.use('/api/tasks', authenticateToken, tasksRoutes);
    this.app.use('/api/habits', authenticateToken, habitsRoutes);
    this.app.use('/api/coaching', authenticateToken, coachingRoutes);
    this.app.use('/api/integrations', authenticateToken, integrationsRoutes);

    // 404 handler for API routes
    this.app.use('/api/*', notFoundHandler);
  }

  private initializeErrorHandling(): void {
    // 404 handler for non-API routes
    this.app.use(notFoundHandler);

    // Error handling middleware (must be last)
    this.app.use(errorHandler);

    // Graceful shutdown
    process.on('SIGTERM', this.gracefulShutdown.bind(this));
    process.on('SIGINT', this.gracefulShutdown.bind(this));
  }

  private async gracefulShutdown(signal: string): Promise<void> {
    console.log(`Received ${signal}. Starting graceful shutdown...`);

    // Close server
    this.server.close(() => {
      console.log('HTTP server closed.');
    });

    // Close WebSocket connections
    this.io.close(() => {
      console.log('WebSocket server closed.');
    });

    // Close database connections
    try {
      await redisClient.quit();
      console.log('Redis connection closed.');
    } catch (error) {
      console.error('Error closing Redis connection:', error);
    }

    // Exit process
    process.exit(0);
  }

  public listen(port: number): void {
    this.server.listen(port, () => {
      console.log(`🚀 RhythmAI API server running on port ${port}`);
      console.log(`📊 Health check: http://localhost:${port}/health`);
      console.log(`📖 API docs: http://localhost:${port}/api`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
    });
  }
}

export default App;
