import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { prisma } from '../config/database';
import { cacheService, CacheKeys } from '../config/redis';
import { logger } from '../utils/logger';
import { config } from '../config/database';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: string;
        isActive: boolean;
      };
    }
  }
}

// JWT payload interface
interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  type: 'access' | 'refresh';
  iat: number;
  exp: number;
}

// Authentication middleware
export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        error: 'Access denied',
        message: 'No token provided or invalid format',
      });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify JWT token
    let decoded: JWTPayload;
    try {
      decoded = jwt.verify(token, config.jwt.secret) as JWTPayload;
    } catch (jwtError) {
      if (jwtError instanceof jwt.TokenExpiredError) {
        res.status(401).json({
          error: 'Token expired',
          message: 'Please refresh your token',
        });
        return;
      } else if (jwtError instanceof jwt.JsonWebTokenError) {
        res.status(401).json({
          error: 'Invalid token',
          message: 'Token is malformed or invalid',
        });
        return;
      }
      throw jwtError;
    }

    // Check if token type is access token
    if (decoded.type !== 'access') {
      res.status(401).json({
        error: 'Invalid token type',
        message: 'Access token required',
      });
      return;
    }

    // Check cache for user data first
    const cacheKey = CacheKeys.user(decoded.userId);
    let user = await cacheService.get<any>(cacheKey);

    if (!user) {
      // Fetch user from database if not in cache
      user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          email: true,
          role: true,
          isActive: true,
          lastLoginAt: true,
        },
      });

      if (!user) {
        res.status(401).json({
          error: 'User not found',
          message: 'User associated with token does not exist',
        });
        return;
      }

      // Cache user data for 30 minutes
      await cacheService.set(cacheKey, user, config.cache.userDataTTL);
    }

    // Check if user is active
    if (!user.isActive) {
      res.status(401).json({
        error: 'Account deactivated',
        message: 'Your account has been deactivated',
      });
      return;
    }

    // Attach user to request object
    req.user = {
      id: user.id,
      email: user.email,
      role: user.role,
      isActive: user.isActive,
    };

    next();
  } catch (error) {
    logger.error('Authentication middleware error:', error);
    res.status(500).json({
      error: 'Authentication error',
      message: 'Internal server error during authentication',
    });
  }
};

// Optional authentication middleware (doesn't fail if no token)
export const optionalAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      next();
      return;
    }

    // Use the main auth middleware logic but don't fail
    await authMiddleware(req, res, next);
  } catch (error) {
    // Continue without authentication
    next();
  }
};

// Role-based authorization middleware
export const requireRole = (roles: string | string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        error: 'Authentication required',
        message: 'Please authenticate to access this resource',
      });
      return;
    }

    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    if (!allowedRoles.includes(req.user.role)) {
      res.status(403).json({
        error: 'Insufficient permissions',
        message: `Required role: ${allowedRoles.join(' or ')}`,
      });
      return;
    }

    next();
  };
};

// Admin role middleware
export const requireAdmin = requireRole('admin');

// Premium user middleware
export const requirePremium = requireRole(['premium', 'admin']);

// Token generation utilities
export class TokenService {
  static generateAccessToken(user: { id: string; email: string; role: string }): string {
    return jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role,
        type: 'access',
      },
      config.jwt.secret,
      {
        expiresIn: config.jwt.expiresIn,
        issuer: 'rhythmai-api',
        audience: 'rhythmai-app',
      }
    );
  }

  static generateRefreshToken(user: { id: string; email: string; role: string }): string {
    return jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role,
        type: 'refresh',
      },
      config.jwt.refreshSecret,
      {
        expiresIn: config.jwt.refreshExpiresIn,
        issuer: 'rhythmai-api',
        audience: 'rhythmai-app',
      }
    );
  }

  static verifyRefreshToken(token: string): JWTPayload {
    return jwt.verify(token, config.jwt.refreshSecret) as JWTPayload;
  }

  static async revokeUserTokens(userId: string): Promise<void> {
    try {
      // Clear user cache to force re-authentication
      const cacheKey = CacheKeys.user(userId);
      await cacheService.del(cacheKey);

      // In a production environment, you might want to maintain a blacklist
      // of revoked tokens or use a different token strategy
      logger.info(`Tokens revoked for user: ${userId}`);
    } catch (error) {
      logger.error('Error revoking user tokens:', error);
      throw error;
    }
  }

  static async isTokenRevoked(token: string): Promise<boolean> {
    try {
      // Decode token without verification to get user ID
      const decoded = jwt.decode(token) as JWTPayload;
      if (!decoded || !decoded.userId) return true;

      // Check if user still exists and is active
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: { isActive: true },
      });

      return !user || !user.isActive;
    } catch (error) {
      logger.error('Error checking token revocation:', error);
      return true; // Assume revoked on error
    }
  }
}

// Session management
export class SessionService {
  static async createSession(userId: string, deviceInfo?: any): Promise<string> {
    const sessionId = require('uuid').v4();
    const sessionData = {
      userId,
      deviceInfo,
      createdAt: new Date(),
      lastAccessAt: new Date(),
    };

    const cacheKey = CacheKeys.userSession(userId, sessionId);
    await cacheService.set(cacheKey, sessionData, 7 * 24 * 3600); // 7 days

    return sessionId;
  }

  static async getSession(userId: string, sessionId: string): Promise<any> {
    const cacheKey = CacheKeys.userSession(userId, sessionId);
    return await cacheService.get(cacheKey);
  }

  static async updateSessionAccess(userId: string, sessionId: string): Promise<void> {
    const cacheKey = CacheKeys.userSession(userId, sessionId);
    const session = await cacheService.get(cacheKey);
    
    if (session) {
      session.lastAccessAt = new Date();
      await cacheService.set(cacheKey, session, 7 * 24 * 3600);
    }
  }

  static async revokeSession(userId: string, sessionId: string): Promise<void> {
    const cacheKey = CacheKeys.userSession(userId, sessionId);
    await cacheService.del(cacheKey);
  }

  static async revokeAllSessions(userId: string): Promise<void> {
    // This would require a pattern-based deletion
    // Implementation depends on your session storage strategy
    logger.info(`All sessions revoked for user: ${userId}`);
  }
}
