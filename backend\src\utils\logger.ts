import winston from 'winston';
import path from 'path';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define which level to log based on environment
const level = () => {
  const env = process.env.NODE_ENV || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'warn';
};

// Define format for logs
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`,
  ),
);

// Define transports
const transports = [
  // Console transport
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    ),
  }),
  
  // File transport for errors
  new winston.transports.File({
    filename: path.join(process.cwd(), 'logs', 'error.log'),
    level: 'error',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    ),
  }),
  
  // File transport for all logs
  new winston.transports.File({
    filename: path.join(process.cwd(), 'logs', 'combined.log'),
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    ),
  }),
];

// Create the logger
const logger = winston.createLogger({
  level: level(),
  levels,
  format,
  transports,
  // Handle uncaught exceptions
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'exceptions.log'),
    }),
  ],
  // Handle unhandled promise rejections
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'rejections.log'),
    }),
  ],
});

// Create logs directory if it doesn't exist
import fs from 'fs';
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Export logger
export { logger };

// Export a stream for Morgan HTTP logging
export const morganStream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

// Utility functions for structured logging
export const logError = (message: string, error: any, context?: any) => {
  logger.error(message, {
    error: {
      message: error.message,
      stack: error.stack,
      name: error.name,
    },
    context,
  });
};

export const logInfo = (message: string, context?: any) => {
  logger.info(message, { context });
};

export const logWarn = (message: string, context?: any) => {
  logger.warn(message, { context });
};

export const logDebug = (message: string, context?: any) => {
  logger.debug(message, { context });
};

// Performance logging utility
export const logPerformance = (operation: string, startTime: number, context?: any) => {
  const duration = Date.now() - startTime;
  logger.info(`Performance: ${operation} completed in ${duration}ms`, {
    operation,
    duration,
    context,
  });
};

// API request logging utility
export const logApiRequest = (req: any, res: any, duration: number) => {
  logger.http(`${req.method} ${req.originalUrl}`, {
    method: req.method,
    url: req.originalUrl,
    statusCode: res.statusCode,
    duration,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: req.user?.id,
  });
};

// Security event logging
export const logSecurityEvent = (event: string, details: any, severity: 'low' | 'medium' | 'high' = 'medium') => {
  const logLevel = severity === 'high' ? 'error' : severity === 'medium' ? 'warn' : 'info';
  
  logger[logLevel](`Security Event: ${event}`, {
    event,
    severity,
    timestamp: new Date().toISOString(),
    ...details,
  });
};

// Business logic logging
export const logBusinessEvent = (event: string, userId: string, details?: any) => {
  logger.info(`Business Event: ${event}`, {
    event,
    userId,
    timestamp: new Date().toISOString(),
    ...details,
  });
};

// Database operation logging
export const logDatabaseOperation = (operation: string, table: string, duration: number, success: boolean) => {
  const level = success ? 'debug' : 'error';
  logger[level](`Database ${operation} on ${table}`, {
    operation,
    table,
    duration,
    success,
    timestamp: new Date().toISOString(),
  });
};

// External API logging
export const logExternalApiCall = (service: string, endpoint: string, method: string, statusCode: number, duration: number) => {
  const level = statusCode >= 400 ? 'error' : 'debug';
  logger[level](`External API call to ${service}`, {
    service,
    endpoint,
    method,
    statusCode,
    duration,
    timestamp: new Date().toISOString(),
  });
};

// ML/AI operation logging
export const logMLOperation = (operation: string, model: string, duration: number, success: boolean, accuracy?: number) => {
  const level = success ? 'info' : 'error';
  logger[level](`ML Operation: ${operation}`, {
    operation,
    model,
    duration,
    success,
    accuracy,
    timestamp: new Date().toISOString(),
  });
};

// User activity logging (for analytics)
export const logUserActivity = (userId: string, activity: string, details?: any) => {
  logger.info(`User Activity: ${activity}`, {
    userId,
    activity,
    timestamp: new Date().toISOString(),
    ...details,
  });
};

// System health logging
export const logSystemHealth = (component: string, status: 'healthy' | 'degraded' | 'unhealthy', metrics?: any) => {
  const level = status === 'healthy' ? 'info' : status === 'degraded' ? 'warn' : 'error';
  logger[level](`System Health: ${component} is ${status}`, {
    component,
    status,
    metrics,
    timestamp: new Date().toISOString(),
  });
};

// Cache operation logging
export const logCacheOperation = (operation: 'hit' | 'miss' | 'set' | 'delete', key: string, duration?: number) => {
  logger.debug(`Cache ${operation}: ${key}`, {
    operation,
    key,
    duration,
    timestamp: new Date().toISOString(),
  });
};

// Integration logging
export const logIntegration = (provider: string, operation: string, success: boolean, details?: any) => {
  const level = success ? 'info' : 'error';
  logger[level](`Integration ${provider}: ${operation}`, {
    provider,
    operation,
    success,
    timestamp: new Date().toISOString(),
    ...details,
  });
};

export default logger;
