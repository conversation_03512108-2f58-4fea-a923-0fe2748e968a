import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env') });

import App from './app';
import { logger } from './utils/logger';
import { connectDatabase } from './config/database';
import { connectRedis } from './config/redis';

// Validate required environment variables
const requiredEnvVars = [
  'DATABASE_URL',
  'REDIS_URL',
  'JWT_SECRET',
  'JWT_REFRESH_SECRET',
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  logger.error('Missing required environment variables:', missingEnvVars);
  process.exit(1);
}

// Set default port
const PORT = parseInt(process.env.PORT || '8000', 10);

async function startServer(): Promise<void> {
  try {
    // Initialize database connection
    logger.info('Connecting to database...');
    await connectDatabase();
    logger.info('✅ Database connected successfully');

    // Initialize Redis connection
    logger.info('Connecting to Redis...');
    await connectRedis();
    logger.info('✅ Redis connected successfully');

    // Initialize and start the application
    const app = new App();
    app.listen(PORT);

    logger.info(`🎵 RhythmAI Backend Server started successfully`);
    logger.info(`🔗 Server URL: http://localhost:${PORT}`);
    logger.info(`📊 Health Check: http://localhost:${PORT}/health`);
    logger.info(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error: Error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
startServer();
