import Redis from 'ioredis';
import { logger } from '../utils/logger';

// Redis client configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0', 10),
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  lazyConnect: true,
  keepAlive: 30000,
  connectTimeout: 10000,
  commandTimeout: 5000,
};

// Create Redis client
export const redisClient = new Redis(redisConfig);

// Redis connection event handlers
redisClient.on('connect', () => {
  logger.info('Redis client connected');
});

redisClient.on('ready', () => {
  logger.info('Redis client ready');
});

redisClient.on('error', (error) => {
  logger.error('Redis client error:', error);
});

redisClient.on('close', () => {
  logger.info('Redis client connection closed');
});

redisClient.on('reconnecting', () => {
  logger.info('Redis client reconnecting...');
});

// Connect to Redis
export async function connectRedis(): Promise<void> {
  try {
    await redisClient.connect();
    logger.info('Redis connection established successfully');
    
    // Test the connection
    await redisClient.ping();
    logger.info('Redis connection test passed');
    
  } catch (error) {
    logger.error('Failed to connect to Redis:', error);
    throw error;
  }
}

// Disconnect from Redis
export async function disconnectRedis(): Promise<void> {
  try {
    await redisClient.quit();
    logger.info('Redis connection closed successfully');
  } catch (error) {
    logger.error('Error closing Redis connection:', error);
    throw error;
  }
}

// Redis health check
export async function checkRedisHealth(): Promise<boolean> {
  try {
    const result = await redisClient.ping();
    return result === 'PONG';
  } catch (error) {
    logger.error('Redis health check failed:', error);
    return false;
  }
}

// Cache utility functions
export class CacheService {
  private client: Redis;

  constructor(client: Redis) {
    this.client = client;
  }

  // Set cache with TTL
  async set(key: string, value: any, ttl: number = 3600): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      await this.client.setex(key, ttl, serializedValue);
    } catch (error) {
      logger.error(`Cache set error for key ${key}:`, error);
      throw error;
    }
  }

  // Get cache value
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(key);
      if (!value) return null;
      return JSON.parse(value) as T;
    } catch (error) {
      logger.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  }

  // Delete cache key
  async del(key: string): Promise<void> {
    try {
      await this.client.del(key);
    } catch (error) {
      logger.error(`Cache delete error for key ${key}:`, error);
      throw error;
    }
  }

  // Check if key exists
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error(`Cache exists check error for key ${key}:`, error);
      return false;
    }
  }

  // Set cache with pattern-based expiration
  async setWithPattern(pattern: string, key: string, value: any, ttl: number = 3600): Promise<void> {
    const fullKey = `${pattern}:${key}`;
    await this.set(fullKey, value, ttl);
  }

  // Get cache with pattern
  async getWithPattern<T>(pattern: string, key: string): Promise<T | null> {
    const fullKey = `${pattern}:${key}`;
    return this.get<T>(fullKey);
  }

  // Delete all keys matching pattern
  async delPattern(pattern: string): Promise<void> {
    try {
      const keys = await this.client.keys(`${pattern}:*`);
      if (keys.length > 0) {
        await this.client.del(...keys);
      }
    } catch (error) {
      logger.error(`Cache pattern delete error for pattern ${pattern}:`, error);
      throw error;
    }
  }

  // Increment counter
  async incr(key: string, ttl?: number): Promise<number> {
    try {
      const result = await this.client.incr(key);
      if (ttl && result === 1) {
        await this.client.expire(key, ttl);
      }
      return result;
    } catch (error) {
      logger.error(`Cache increment error for key ${key}:`, error);
      throw error;
    }
  }

  // Set hash field
  async hset(key: string, field: string, value: any): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      await this.client.hset(key, field, serializedValue);
    } catch (error) {
      logger.error(`Cache hset error for key ${key}, field ${field}:`, error);
      throw error;
    }
  }

  // Get hash field
  async hget<T>(key: string, field: string): Promise<T | null> {
    try {
      const value = await this.client.hget(key, field);
      if (!value) return null;
      return JSON.parse(value) as T;
    } catch (error) {
      logger.error(`Cache hget error for key ${key}, field ${field}:`, error);
      return null;
    }
  }

  // Get all hash fields
  async hgetall<T>(key: string): Promise<Record<string, T> | null> {
    try {
      const hash = await this.client.hgetall(key);
      if (!hash || Object.keys(hash).length === 0) return null;
      
      const result: Record<string, T> = {};
      for (const [field, value] of Object.entries(hash)) {
        result[field] = JSON.parse(value) as T;
      }
      return result;
    } catch (error) {
      logger.error(`Cache hgetall error for key ${key}:`, error);
      return null;
    }
  }

  // Add to set
  async sadd(key: string, ...members: string[]): Promise<number> {
    try {
      return await this.client.sadd(key, ...members);
    } catch (error) {
      logger.error(`Cache sadd error for key ${key}:`, error);
      throw error;
    }
  }

  // Get set members
  async smembers(key: string): Promise<string[]> {
    try {
      return await this.client.smembers(key);
    } catch (error) {
      logger.error(`Cache smembers error for key ${key}:`, error);
      return [];
    }
  }

  // Check if member exists in set
  async sismember(key: string, member: string): Promise<boolean> {
    try {
      const result = await this.client.sismember(key, member);
      return result === 1;
    } catch (error) {
      logger.error(`Cache sismember error for key ${key}, member ${member}:`, error);
      return false;
    }
  }
}

// Export cache service instance
export const cacheService = new CacheService(redisClient);

// Cache key generators
export const CacheKeys = {
  user: (userId: string) => `user:${userId}`,
  userSession: (userId: string, sessionId: string) => `session:${userId}:${sessionId}`,
  energyData: (userId: string, date: string) => `energy:${userId}:${date}`,
  taskData: (userId: string) => `tasks:${userId}`,
  habitData: (userId: string) => `habits:${userId}`,
  analyticsData: (userId: string, type: string) => `analytics:${userId}:${type}`,
  mlPrediction: (userId: string, type: string) => `ml:${userId}:${type}`,
  integrationToken: (userId: string, provider: string) => `token:${userId}:${provider}`,
  rateLimit: (ip: string, endpoint: string) => `ratelimit:${ip}:${endpoint}`,
  notification: (userId: string) => `notifications:${userId}`,
};
