'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { X, Zap, <PERSON>, Brain, Coffee, Dumbbell, Bed } from 'lucide-react';
import { useRecordEnergy } from '@/hooks/useApi';
import { EnergyDataRequest, EnergyLevel } from '@/types/api';
import { cn } from '@/lib/utils';

const energyLogSchema = z.object({
  energyLevel: z.number().min(1).max(5),
  mood: z.number().min(1).max(10),
  stressLevel: z.number().min(1).max(10),
  sleepQuality: z.number().min(1).max(10).optional(),
  physicalActivity: z.number().min(1).max(10).optional(),
  caffeine: z.number().min(0).max(10).optional(),
  context: z.string().optional(),
});

type EnergyLogFormData = z.infer<typeof energyLogSchema>;

interface EnergyLogFormProps {
  onClose: () => void;
  onSuccess: () => void;
  initialData?: Partial<EnergyLogFormData>;
}

const energyLevels = [
  { value: 1, label: 'Very Low', emoji: '😴', color: 'bg-red-100 border-red-300 text-red-700 dark:bg-red-900/20 dark:border-red-700 dark:text-red-400' },
  { value: 2, label: 'Low', emoji: '😐', color: 'bg-orange-100 border-orange-300 text-orange-700 dark:bg-orange-900/20 dark:border-orange-700 dark:text-orange-400' },
  { value: 3, label: 'Medium', emoji: '🙂', color: 'bg-yellow-100 border-yellow-300 text-yellow-700 dark:bg-yellow-900/20 dark:border-yellow-700 dark:text-yellow-400' },
  { value: 4, label: 'High', emoji: '😊', color: 'bg-green-100 border-green-300 text-green-700 dark:bg-green-900/20 dark:border-green-700 dark:text-green-400' },
  { value: 5, label: 'Very High', emoji: '⚡', color: 'bg-blue-100 border-blue-300 text-blue-700 dark:bg-blue-900/20 dark:border-blue-700 dark:text-blue-400' },
];

export function EnergyLogForm({ onClose, onSuccess, initialData }: EnergyLogFormProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const recordEnergyMutation = useRecordEnergy();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<EnergyLogFormData>({
    resolver: zodResolver(energyLogSchema),
    defaultValues: {
      energyLevel: 3,
      mood: 5,
      stressLevel: 5,
      sleepQuality: 7,
      physicalActivity: 5,
      caffeine: 0,
      context: '',
      ...initialData,
    },
  });

  const watchedValues = watch();

  const onSubmit = async (data: EnergyLogFormData) => {
    try {
      await recordEnergyMutation.mutateAsync(data as EnergyDataRequest);
      onSuccess();
    } catch (error) {
      console.error('Failed to record energy:', error);
    }
  };

  const SliderInput = ({ 
    name, 
    label, 
    min = 1, 
    max = 10, 
    icon: Icon,
    color = 'text-blue-600',
    description 
  }: {
    name: keyof EnergyLogFormData;
    label: string;
    min?: number;
    max?: number;
    icon: React.ComponentType<{ className?: string }>;
    color?: string;
    description?: string;
  }) => {
    const value = watchedValues[name] as number || min;
    
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Icon className={cn('w-5 h-5', color)} />
            <label className="text-sm font-medium text-gray-900 dark:text-white">
              {label}
            </label>
          </div>
          <span className="text-sm font-bold text-gray-900 dark:text-white">
            {value}/{max}
          </span>
        </div>
        
        <div className="relative">
          <input
            type="range"
            min={min}
            max={max}
            step={1}
            value={value}
            onChange={(e) => setValue(name, parseInt(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider"
          />
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
            <span>{min}</span>
            <span>{max}</span>
          </div>
        </div>
        
        {description && (
          <p className="text-xs text-gray-600 dark:text-gray-400">{description}</p>
        )}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2">
            <Zap className="w-6 h-6 text-yellow-500" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Log Energy Level
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
          {/* Step 1: Energy Level */}
          {currentStep === 1 && (
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  How is your energy level right now?
                </h3>
                <div className="grid grid-cols-1 gap-2">
                  {energyLevels.map((level) => (
                    <button
                      key={level.value}
                      type="button"
                      onClick={() => setValue('energyLevel', level.value as EnergyLevel)}
                      className={cn(
                        'p-4 rounded-lg border-2 transition-all text-left',
                        watchedValues.energyLevel === level.value
                          ? level.color + ' border-current'
                          : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                      )}
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{level.emoji}</span>
                        <div>
                          <div className="font-medium">{level.label}</div>
                          <div className="text-sm opacity-75">Level {level.value}</div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
              
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setCurrentStep(2)}
                  className="btn-primary btn-md"
                  disabled={!watchedValues.energyLevel}
                >
                  Next
                </button>
              </div>
            </div>
          )}

          {/* Step 2: Additional Metrics */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Additional details (optional but helpful)
              </h3>

              <SliderInput
                name="mood"
                label="Mood"
                icon={Heart}
                color="text-pink-600"
                description="How are you feeling emotionally?"
              />

              <SliderInput
                name="stressLevel"
                label="Stress Level"
                icon={Brain}
                color="text-red-600"
                description="How stressed or anxious do you feel?"
              />

              <SliderInput
                name="sleepQuality"
                label="Sleep Quality"
                icon={Bed}
                color="text-indigo-600"
                description="How well did you sleep last night?"
              />

              <SliderInput
                name="physicalActivity"
                label="Physical Activity"
                icon={Dumbbell}
                color="text-green-600"
                description="How active have you been today?"
              />

              <SliderInput
                name="caffeine"
                label="Caffeine Intake"
                min={0}
                icon={Coffee}
                color="text-amber-600"
                description="Cups of coffee/tea or energy drinks today"
              />

              <div className="flex justify-between">
                <button
                  type="button"
                  onClick={() => setCurrentStep(1)}
                  className="btn-secondary btn-md"
                >
                  Back
                </button>
                <button
                  type="button"
                  onClick={() => setCurrentStep(3)}
                  className="btn-primary btn-md"
                >
                  Next
                </button>
              </div>
            </div>
          )}

          {/* Step 3: Context */}
          {currentStep === 3 && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Context (Optional)
                </label>
                <textarea
                  {...register('context')}
                  rows={4}
                  className="textarea"
                  placeholder="What's happening right now? Any specific activities, environment, or factors affecting your energy?"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  This helps us understand patterns and provide better insights
                </p>
              </div>

              <div className="flex justify-between">
                <button
                  type="button"
                  onClick={() => setCurrentStep(2)}
                  className="btn-secondary btn-md"
                >
                  Back
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="btn-primary btn-md"
                >
                  {isSubmitting ? (
                    <div className="flex items-center space-x-2">
                      <div className="spinner w-4 h-4"></div>
                      <span>Saving...</span>
                    </div>
                  ) : (
                    'Save Energy Log'
                  )}
                </button>
              </div>
            </div>
          )}
        </form>

        {/* Progress Indicator */}
        <div className="px-6 pb-4">
          <div className="flex items-center space-x-2">
            {[1, 2, 3].map((step) => (
              <div
                key={step}
                className={cn(
                  'flex-1 h-2 rounded-full',
                  step <= currentStep ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-700'
                )}
              />
            ))}
          </div>
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
            <span>Energy Level</span>
            <span>Details</span>
            <span>Context</span>
          </div>
        </div>
      </div>
    </div>
  );
}
