import { useState, useEffect, useCallback, useRef } from 'react';
import { useStartFlowSession, useEndFlowSession, useActiveFlowSession } from './useApi';
import {
  FlowSessionUI,
  SessionState,
  TimerState,
  SessionProgress,
  SessionType,
  BreakTimer,
  AmbientSound,
  SessionPreferences,
  SessionEvent,
  SessionEventType,
  STORAGE_KEYS,
  SESSION_TYPES,
  AMBIENT_SOUNDS,
  TimerConfig
} from '@/types/flow-session';
import { FlowSession, FlowSessionRequest } from '@/types/api';

// Hook for managing session preferences
export function useSessionPreferences() {
  const [preferences, setPreferences] = useState<SessionPreferences>(() => {
    const stored = localStorage.getItem(STORAGE_KEYS.SESSION_PREFERENCES);
    if (stored) {
      return JSON.parse(stored);
    }

    // Default preferences
    return {
      defaultSessionType: 'deep-focus',
      timerConfig: {
        duration: 60,
        breakInterval: 25,
        shortBreakDuration: 5,
        longBreakDuration: 15,
        longBreakInterval: 4,
        autoStartBreaks: false,
        autoStartSessions: false,
        soundEnabled: true,
        notificationsEnabled: true
      },
      ambientSoundEnabled: false,
      defaultAmbientSound: 'rain',
      breakRemindersEnabled: true,
      focusModeEnabled: false,
      sessionGoals: []
    };
  });

  const updatePreferences = useCallback((updates: Partial<SessionPreferences>) => {
    const newPreferences = { ...preferences, ...updates };
    setPreferences(newPreferences);
    localStorage.setItem(STORAGE_KEYS.SESSION_PREFERENCES, JSON.stringify(newPreferences));
  }, [preferences]);

  const updateTimerConfig = useCallback((updates: Partial<TimerConfig>) => {
    const newTimerConfig = { ...preferences.timerConfig, ...updates };
    updatePreferences({ timerConfig: newTimerConfig });
  }, [preferences.timerConfig, updatePreferences]);

  return {
    preferences,
    updatePreferences,
    updateTimerConfig
  };
}

// Hook for managing active flow session state
export function useFlowSessionManager() {
  const [sessionState, setSessionState] = useState<SessionState>('idle');
  const [timerState, setTimerState] = useState<TimerState>('stopped');
  const [progress, setProgress] = useState<SessionProgress>({
    elapsedTime: 0,
    remainingTime: 0,
    totalTime: 0,
    percentage: 0,
    milestones: []
  });
  const [breakTimer, setBreakTimer] = useState<BreakTimer | null>(null);
  const [currentSession, setCurrentSession] = useState<FlowSessionUI | null>(null);
  
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<Date | null>(null);
  const pausedTimeRef = useRef<number>(0);

  const { data: activeSession } = useActiveFlowSession();
  const startSessionMutation = useStartFlowSession();
  const endSessionMutation = useEndFlowSession();

  // Initialize session from API if exists
  useEffect(() => {
    if (activeSession?.data) {
      const session = activeSession.data;
      const sessionType = SESSION_TYPES.find(type => type.name.toLowerCase().includes(session.taskType.toLowerCase())) || SESSION_TYPES[0];
      
      setCurrentSession({
        ...session,
        state: 'active',
        timerState: 'running',
        progress: calculateProgress(session),
        sessionType
      });
      setSessionState('active');
      setTimerState('running');
      startTimeRef.current = new Date(session.startTime);
    }
  }, [activeSession]);

  // Timer effect
  useEffect(() => {
    if (timerState === 'running' && currentSession) {
      timerRef.current = setInterval(() => {
        updateProgress();
      }, 1000);
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [timerState, currentSession]);

  const calculateProgress = useCallback((session: FlowSession): SessionProgress => {
    const startTime = new Date(session.startTime);
    const now = new Date();
    const elapsedMs = now.getTime() - startTime.getTime();
    const elapsedSeconds = Math.floor(elapsedMs / 1000);
    
    // Default duration if not set
    const totalSeconds = (session.duration || 60) * 60;
    const remainingSeconds = Math.max(0, totalSeconds - elapsedSeconds);
    const percentage = Math.min(100, (elapsedSeconds / totalSeconds) * 100);

    const milestones = [
      { id: '25', name: '25% Complete', percentage: 25, reached: percentage >= 25 },
      { id: '50', name: 'Halfway Point', percentage: 50, reached: percentage >= 50 },
      { id: '75', name: '75% Complete', percentage: 75, reached: percentage >= 75 },
      { id: '90', name: 'Almost Done', percentage: 90, reached: percentage >= 90 }
    ];

    return {
      elapsedTime: elapsedSeconds,
      remainingTime: remainingSeconds,
      totalTime: totalSeconds,
      percentage,
      milestones,
      currentMilestone: milestones.find(m => m.reached && !m.timestamp)
    };
  }, []);

  const updateProgress = useCallback(() => {
    if (!currentSession || !startTimeRef.current) return;

    const now = new Date();
    const elapsedMs = now.getTime() - startTimeRef.current.getTime() - pausedTimeRef.current;
    const elapsedSeconds = Math.floor(elapsedMs / 1000);
    
    const totalSeconds = currentSession.sessionType.defaultDuration * 60;
    const remainingSeconds = Math.max(0, totalSeconds - elapsedSeconds);
    const percentage = Math.min(100, (elapsedSeconds / totalSeconds) * 100);

    const newProgress: SessionProgress = {
      elapsedTime: elapsedSeconds,
      remainingTime: remainingSeconds,
      totalTime: totalSeconds,
      percentage,
      milestones: progress.milestones.map(m => ({
        ...m,
        reached: percentage >= m.percentage,
        timestamp: percentage >= m.percentage && !m.reached ? now : m.timestamp
      }))
    };

    setProgress(newProgress);
    
    if (currentSession) {
      setCurrentSession(prev => prev ? { ...prev, progress: newProgress } : null);
    }

    // Auto-complete session when time is up
    if (remainingSeconds === 0 && sessionState === 'active') {
      completeSession();
    }
  }, [currentSession, progress.milestones, sessionState]);

  const startSession = useCallback(async (sessionType: SessionType, environment: any, linkedTaskId?: string) => {
    try {
      const sessionRequest: FlowSessionRequest = {
        taskType: sessionType.name,
        environment
      };

      const response = await startSessionMutation.mutateAsync(sessionRequest);
      
      if (response.data) {
        const newSession: FlowSessionUI = {
          ...response.data,
          state: 'active',
          timerState: 'running',
          progress: {
            elapsedTime: 0,
            remainingTime: sessionType.defaultDuration * 60,
            totalTime: sessionType.defaultDuration * 60,
            percentage: 0,
            milestones: [
              { id: '25', name: '25% Complete', percentage: 25, reached: false },
              { id: '50', name: 'Halfway Point', percentage: 50, reached: false },
              { id: '75', name: '75% Complete', percentage: 75, reached: false },
              { id: '90', name: 'Almost Done', percentage: 90, reached: false }
            ]
          },
          sessionType,
          linkedTaskId
        };

        setCurrentSession(newSession);
        setSessionState('active');
        setTimerState('running');
        startTimeRef.current = new Date();
        pausedTimeRef.current = 0;

        // Save to local storage
        localStorage.setItem(STORAGE_KEYS.ACTIVE_SESSION, JSON.stringify(newSession));
        
        logSessionEvent('session_started', { sessionType: sessionType.name });
      }
    } catch (error) {
      console.error('Failed to start session:', error);
      throw error;
    }
  }, [startSessionMutation]);

  const pauseSession = useCallback(() => {
    if (sessionState === 'active' && timerState === 'running') {
      setTimerState('paused');
      setSessionState('paused');
      
      if (currentSession) {
        const updatedSession = { ...currentSession, state: 'paused' as SessionState, timerState: 'paused' as TimerState };
        setCurrentSession(updatedSession);
        localStorage.setItem(STORAGE_KEYS.ACTIVE_SESSION, JSON.stringify(updatedSession));
      }
      
      logSessionEvent('session_paused');
    }
  }, [sessionState, timerState, currentSession]);

  const resumeSession = useCallback(() => {
    if (sessionState === 'paused' && timerState === 'paused') {
      setTimerState('running');
      setSessionState('active');
      
      if (currentSession) {
        const updatedSession = { ...currentSession, state: 'active' as SessionState, timerState: 'running' as TimerState };
        setCurrentSession(updatedSession);
        localStorage.setItem(STORAGE_KEYS.ACTIVE_SESSION, JSON.stringify(updatedSession));
      }
      
      logSessionEvent('session_resumed');
    }
  }, [sessionState, timerState, currentSession]);

  const completeSession = useCallback(async () => {
    if (!currentSession) return;

    try {
      await endSessionMutation.mutateAsync({
        id: currentSession.id,
        data: {
          productivity: currentSession.productivity,
          satisfaction: currentSession.satisfaction,
          interruptions: currentSession.interruptions,
          notes: currentSession.notes
        }
      });

      setSessionState('completed');
      setTimerState('stopped');
      
      // Clear active session from storage
      localStorage.removeItem(STORAGE_KEYS.ACTIVE_SESSION);
      
      logSessionEvent('session_completed', { 
        duration: progress.elapsedTime,
        completionRate: progress.percentage 
      });

      // Reset after a delay to show completion state
      setTimeout(() => {
        setCurrentSession(null);
        setSessionState('idle');
        setProgress({
          elapsedTime: 0,
          remainingTime: 0,
          totalTime: 0,
          percentage: 0,
          milestones: []
        });
      }, 3000);

    } catch (error) {
      console.error('Failed to complete session:', error);
    }
  }, [currentSession, endSessionMutation, progress]);

  const cancelSession = useCallback(async () => {
    if (!currentSession) return;

    try {
      await endSessionMutation.mutateAsync({
        id: currentSession.id,
        data: {
          notes: 'Session cancelled by user'
        }
      });

      setSessionState('idle');
      setTimerState('stopped');
      setCurrentSession(null);
      
      localStorage.removeItem(STORAGE_KEYS.ACTIVE_SESSION);
      logSessionEvent('session_cancelled');

      setProgress({
        elapsedTime: 0,
        remainingTime: 0,
        totalTime: 0,
        percentage: 0,
        milestones: []
      });

    } catch (error) {
      console.error('Failed to cancel session:', error);
    }
  }, [currentSession, endSessionMutation]);

  const logSessionEvent = useCallback((type: SessionEventType, data?: any) => {
    const event: SessionEvent = {
      id: Date.now().toString(),
      sessionId: currentSession?.id || '',
      type,
      timestamp: new Date(),
      data
    };

    // Store events in local storage for analytics
    const existingEvents = JSON.parse(localStorage.getItem('session_events') || '[]');
    existingEvents.push(event);
    localStorage.setItem('session_events', JSON.stringify(existingEvents.slice(-100))); // Keep last 100 events
  }, [currentSession]);

  return {
    // State
    sessionState,
    timerState,
    progress,
    breakTimer,
    currentSession,
    
    // Actions
    startSession,
    pauseSession,
    resumeSession,
    completeSession,
    cancelSession,
    
    // Loading states
    isStarting: startSessionMutation.isLoading,
    isEnding: endSessionMutation.isLoading
  };
}
