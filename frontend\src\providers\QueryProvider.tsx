'use client';

import React, { ReactNode, useState } from 'react';
import { QueryClient, QueryClientProvider, QueryCache, MutationCache } from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';
import { toast } from 'sonner';

interface QueryProviderProps {
  children: ReactNode;
}

export function QueryProvider({ children }: QueryProviderProps) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Global query defaults
            staleTime: 5 * 60 * 1000, // 5 minutes
            cacheTime: 10 * 60 * 1000, // 10 minutes
            retry: (failureCount, error: any) => {
              // Don't retry on 4xx errors except 408, 429
              if (error?.statusCode >= 400 && error?.statusCode < 500) {
                if (error?.statusCode === 408 || error?.statusCode === 429) {
                  return failureCount < 2;
                }
                return false;
              }
              // Retry on 5xx errors and network errors
              return failureCount < 3;
            },
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
            refetchOnWindowFocus: false,
            refetchOnReconnect: true,
            refetchOnMount: true,
          },
          mutations: {
            // Global mutation defaults
            retry: (failureCount, error: any) => {
              // Don't retry mutations on client errors
              if (error?.statusCode >= 400 && error?.statusCode < 500) {
                return false;
              }
              // Retry on server errors
              return failureCount < 2;
            },
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
          },
        },
        queryCache: new QueryCache({
          onError: (error: any, query) => {
            // Global error handling for queries
            console.error('Query error:', error, query);
            
            // Don't show toast for background refetches
            if (query.state.data !== undefined) {
              return;
            }

            // Show error toast for failed queries
            if (error?.message && !error?.message.includes('Network Error')) {
              toast.error(error.message);
            }
          },
        }),
        mutationCache: new MutationCache({
          onError: (error: any, variables, context, mutation) => {
            // Global error handling for mutations
            console.error('Mutation error:', error, variables, mutation);
            
            // Show error toast for failed mutations
            if (error?.message) {
              toast.error(error.message);
            }
          },
          onSuccess: (data: any, variables, context, mutation) => {
            // Global success handling for mutations
            console.log('Mutation success:', data, mutation);
            
            // Show success toast if mutation has a success message
            if (data?.message) {
              toast.success(data.message);
            }
          },
        }),
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools
          initialIsOpen={false}
          position="bottom-right"
          toggleButtonProps={{
            style: {
              marginLeft: '5px',
              transform: 'scale(0.8)',
              transformOrigin: 'bottom right',
            },
          }}
        />
      )}
    </QueryClientProvider>
  );
}
