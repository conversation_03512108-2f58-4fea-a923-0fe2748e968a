'use client';

import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  Eye, 
  EyeOff, 
  Monitor, 
  Volume2, 
  VolumeX, 
  Smartphone, 
  Wifi, 
  WifiOff,
  Lightbulb,
  Coffee,
  Thermometer,
  CheckCircle,
  X,
  Settings,
  AlertTriangle
} from 'lucide-react';
import { useSessionPreferences } from '@/hooks/useFlowSession';
import { cn } from '@/lib/utils';

interface FocusModeProps {
  className?: string;
  isActive?: boolean;
  onToggle?: (enabled: boolean) => void;
}

interface DistractionSource {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  description: string;
  category: 'digital' | 'physical' | 'environmental';
  severity: 'low' | 'medium' | 'high';
  suggestions: string[];
  isBlocked?: boolean;
}

const DISTRACTION_SOURCES: DistractionSource[] = [
  {
    id: 'social-media',
    name: 'Social Media',
    icon: Smartphone,
    description: 'Facebook, Twitter, Instagram, etc.',
    category: 'digital',
    severity: 'high',
    suggestions: [
      'Use website blockers like Cold Turkey or Freedom',
      'Log out of all social media accounts',
      'Put phone in another room',
      'Use app timers to limit usage'
    ]
  },
  {
    id: 'notifications',
    name: 'Notifications',
    icon: Shield,
    description: 'Email, messages, app notifications',
    category: 'digital',
    severity: 'high',
    suggestions: [
      'Turn on Do Not Disturb mode',
      'Disable non-essential notifications',
      'Use notification scheduling',
      'Close email and messaging apps'
    ]
  },
  {
    id: 'noise',
    name: 'Background Noise',
    icon: Volume2,
    description: 'Traffic, conversations, construction',
    category: 'environmental',
    severity: 'medium',
    suggestions: [
      'Use noise-canceling headphones',
      'Play white noise or ambient sounds',
      'Find a quieter workspace',
      'Use earplugs if necessary'
    ]
  },
  {
    id: 'lighting',
    name: 'Poor Lighting',
    icon: Lightbulb,
    description: 'Too bright, too dim, or flickering lights',
    category: 'environmental',
    severity: 'medium',
    suggestions: [
      'Adjust screen brightness to match surroundings',
      'Use a desk lamp for task lighting',
      'Position screen to avoid glare',
      'Take breaks to rest your eyes'
    ]
  },
  {
    id: 'temperature',
    name: 'Temperature',
    icon: Thermometer,
    description: 'Too hot or too cold workspace',
    category: 'environmental',
    severity: 'low',
    suggestions: [
      'Adjust thermostat to 68-72°F (20-22°C)',
      'Use a fan or space heater',
      'Dress appropriately for temperature',
      'Take breaks to regulate body temperature'
    ]
  },
  {
    id: 'clutter',
    name: 'Visual Clutter',
    icon: Eye,
    description: 'Messy desk, too many open windows',
    category: 'physical',
    severity: 'medium',
    suggestions: [
      'Clear your desk of unnecessary items',
      'Close unused browser tabs and applications',
      'Use a clean, minimal desktop wallpaper',
      'Organize files and folders regularly'
    ]
  }
];

export function FocusMode({ className, isActive = false, onToggle }: FocusModeProps) {
  const [selectedDistractions, setSelectedDistractions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const { preferences, updatePreferences } = useSessionPreferences();

  useEffect(() => {
    // Load focus mode state from preferences
    if (preferences.focusModeEnabled !== isActive) {
      onToggle?.(preferences.focusModeEnabled);
    }
  }, [preferences.focusModeEnabled, isActive, onToggle]);

  const handleToggleFocusMode = () => {
    const newState = !isActive;
    updatePreferences({ focusModeEnabled: newState });
    onToggle?.(newState);
  };

  const handleDistractionToggle = (distractionId: string) => {
    setSelectedDistractions(prev => 
      prev.includes(distractionId)
        ? prev.filter(id => id !== distractionId)
        : [...prev, distractionId]
    );
  };

  const getDistractionsByCategory = (category: string) => {
    return DISTRACTION_SOURCES.filter(d => d.category === category);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20';
      case 'medium':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20';
      case 'low':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  return (
    <div className={cn('bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className={cn('w-10 h-10 rounded-full flex items-center justify-center', 
            isActive 
              ? 'bg-green-100 dark:bg-green-900/20' 
              : 'bg-gray-100 dark:bg-gray-800'
          )}>
            <Shield className={cn('w-5 h-5', 
              isActive 
                ? 'text-green-600 dark:text-green-400' 
                : 'text-gray-600 dark:text-gray-400'
            )} />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Focus Mode
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {isActive ? 'Active - Distractions minimized' : 'Optimize your environment for deep work'}
            </p>
          </div>
        </div>

        <button
          onClick={handleToggleFocusMode}
          className={cn(
            'px-4 py-2 rounded-lg font-medium transition-colors',
            isActive
              ? 'bg-red-500 hover:bg-red-600 text-white'
              : 'bg-green-500 hover:bg-green-600 text-white'
          )}
        >
          {isActive ? 'Disable' : 'Enable'} Focus Mode
        </button>
      </div>

      {/* Focus Mode Status */}
      {isActive && (
        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
            <span className="font-medium text-green-800 dark:text-green-200">Focus Mode Active</span>
          </div>
          <p className="text-sm text-green-700 dark:text-green-300">
            Your environment is optimized for focused work. Minimize distractions and stay in the flow!
          </p>
        </div>
      )}

      {/* Distraction Assessment */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-md font-medium text-gray-900 dark:text-white">
            Distraction Assessment
          </h4>
          <button
            onClick={() => setShowSuggestions(!showSuggestions)}
            className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
          >
            {showSuggestions ? 'Hide' : 'Show'} Suggestions
          </button>
        </div>

        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
          Select the distractions currently affecting your workspace:
        </p>

        {/* Categories */}
        <div className="space-y-4">
          {['digital', 'environmental', 'physical'].map((category) => (
            <div key={category}>
              <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 capitalize">
                {category} Distractions
              </h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {getDistractionsByCategory(category).map((distraction) => {
                  const Icon = distraction.icon;
                  const isSelected = selectedDistractions.includes(distraction.id);
                  
                  return (
                    <button
                      key={distraction.id}
                      onClick={() => handleDistractionToggle(distraction.id)}
                      className={cn(
                        'flex items-center space-x-3 p-3 rounded-lg border-2 transition-all text-left',
                        isSelected
                          ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                      )}
                    >
                      <Icon className={cn('w-4 h-4', isSelected ? 'text-red-600 dark:text-red-400' : 'text-gray-600 dark:text-gray-400')} />
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className={cn('text-sm font-medium', isSelected ? 'text-red-900 dark:text-red-100' : 'text-gray-900 dark:text-white')}>
                            {distraction.name}
                          </span>
                          <span className={cn('px-2 py-0.5 text-xs font-medium rounded-full', getSeverityColor(distraction.severity))}>
                            {distraction.severity}
                          </span>
                        </div>
                        <p className={cn('text-xs', isSelected ? 'text-red-700 dark:text-red-300' : 'text-gray-500 dark:text-gray-400')}>
                          {distraction.description}
                        </p>
                      </div>
                      {isSelected && <X className="w-4 h-4 text-red-600 dark:text-red-400" />}
                    </button>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Suggestions */}
      {showSuggestions && selectedDistractions.length > 0 && (
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            Recommended Actions
          </h4>
          <div className="space-y-4">
            {selectedDistractions.map((distractionId) => {
              const distraction = DISTRACTION_SOURCES.find(d => d.id === distractionId);
              if (!distraction) return null;

              const Icon = distraction.icon;
              
              return (
                <div key={distractionId} className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <Icon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    <span className="font-medium text-blue-900 dark:text-blue-100">
                      {distraction.name}
                    </span>
                  </div>
                  <ul className="space-y-1">
                    {distraction.suggestions.map((suggestion, index) => (
                      <li key={index} className="text-sm text-blue-800 dark:text-blue-200 flex items-start space-x-2">
                        <span className="text-blue-600 dark:text-blue-400 mt-1">•</span>
                        <span>{suggestion}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Focus Tips */}
      <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div className="flex items-start space-x-2">
          <Lightbulb className="w-4 h-4 text-yellow-600 dark:text-yellow-400 mt-0.5" />
          <div>
            <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
              Pro Focus Tips
            </h5>
            <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Use the Pomodoro Technique: 25 minutes focused work, 5 minute break</li>
              <li>• Keep a notepad nearby to jot down distracting thoughts</li>
              <li>• Set specific goals for each focus session</li>
              <li>• Hydrate regularly but avoid excessive caffeine</li>
              <li>• Take short walks during breaks to reset your mind</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

// Compact focus mode indicator for headers
export function FocusModeIndicator({ className }: { className?: string }) {
  const { preferences } = useSessionPreferences();
  
  if (!preferences.focusModeEnabled) return null;

  return (
    <div className={cn('flex items-center space-x-2 px-2 py-1 bg-green-100 dark:bg-green-900/20 rounded-full', className)}>
      <Shield className="w-3 h-3 text-green-600 dark:text-green-400" />
      <span className="text-xs font-medium text-green-700 dark:text-green-300">
        Focus Mode
      </span>
    </div>
  );
}
