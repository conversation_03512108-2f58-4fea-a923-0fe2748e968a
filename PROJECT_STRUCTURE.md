# RhythmAI Project Structure

## Complete Directory Organization

```
RhythmAI/
├── README.md                           # Project overview and setup instructions
├── DOMAIN_RESEARCH.md                  # Domain availability analysis
├── PROJECT_STRUCTURE.md                # This file - complete project structure
├── LICENSE                             # MIT License
├── .gitignore                          # Git ignore patterns
├── docker-compose.yml                  # Multi-service Docker setup
├── .env.example                        # Environment variables template
├── package.json                        # Root package.json for workspace
├── tsconfig.json                       # TypeScript configuration
├── .eslintrc.js                        # ESLint configuration
├── .prettierrc                         # Prettier configuration
├── jest.config.js                      # Jest testing configuration
├── CONTRIBUTING.md                     # Contribution guidelines
├── CHANGELOG.md                        # Version history
├── SECURITY.md                         # Security policy
│
├── frontend/                           # React/Next.js Frontend Application
│   ├── package.json                    # Frontend dependencies
│   ├── next.config.js                  # Next.js configuration
│   ├── tailwind.config.js              # Tailwind CSS configuration
│   ├── tsconfig.json                   # Frontend TypeScript config
│   ├── .env.local.example              # Frontend environment variables
│   ├── public/                         # Static assets
│   │   ├── favicon.ico
│   │   ├── logo.svg
│   │   ├── manifest.json               # PWA manifest
│   │   ├── sw.js                       # Service worker
│   │   └── icons/                      # App icons for PWA
│   ├── src/                            # Source code
│   │   ├── app/                        # Next.js 13+ app directory
│   │   │   ├── layout.tsx              # Root layout
│   │   │   ├── page.tsx                # Home page
│   │   │   ├── globals.css             # Global styles
│   │   │   ├── auth/                   # Authentication pages
│   │   │   ├── dashboard/              # Dashboard pages
│   │   │   ├── onboarding/             # User onboarding flow
│   │   │   ├── settings/               # User settings
│   │   │   ├── analytics/              # Analytics and insights
│   │   │   └── api/                    # API routes (Next.js API)
│   │   ├── components/                 # Reusable React components
│   │   │   ├── ui/                     # Basic UI components
│   │   │   │   ├── Button.tsx
│   │   │   │   ├── Input.tsx
│   │   │   │   ├── Modal.tsx
│   │   │   │   ├── Card.tsx
│   │   │   │   ├── Chart.tsx
│   │   │   │   └── index.ts
│   │   │   ├── layout/                 # Layout components
│   │   │   │   ├── Header.tsx
│   │   │   │   ├── Sidebar.tsx
│   │   │   │   ├── Footer.tsx
│   │   │   │   └── Navigation.tsx
│   │   │   ├── dashboard/              # Dashboard-specific components
│   │   │   │   ├── EnergyChart.tsx
│   │   │   │   ├── TaskList.tsx
│   │   │   │   ├── FlowStateIndicator.tsx
│   │   │   │   ├── HabitTracker.tsx
│   │   │   │   └── CoachingPanel.tsx
│   │   │   ├── onboarding/             # Onboarding components
│   │   │   │   ├── WelcomeStep.tsx
│   │   │   │   ├── DataConnectionStep.tsx
│   │   │   │   ├── PreferencesStep.tsx
│   │   │   │   └── BaselineStep.tsx
│   │   │   └── analytics/              # Analytics components
│   │   │       ├── CircadianChart.tsx
│   │   │       ├── ProductivityTrends.tsx
│   │   │       ├── HabitProgress.tsx
│   │   │       └── InsightsPanel.tsx
│   │   ├── hooks/                      # Custom React hooks
│   │   │   ├── useAuth.ts
│   │   │   ├── useEnergyData.ts
│   │   │   ├── useFlowState.ts
│   │   │   ├── useHabits.ts
│   │   │   ├── useNotifications.ts
│   │   │   └── useWebSocket.ts
│   │   ├── lib/                        # Utility libraries
│   │   │   ├── api.ts                  # API client
│   │   │   ├── auth.ts                 # Authentication utilities
│   │   │   ├── storage.ts              # Local storage utilities
│   │   │   ├── utils.ts                # General utilities
│   │   │   ├── constants.ts            # Application constants
│   │   │   ├── validations.ts          # Form validations
│   │   │   └── ml-client.ts            # ML model client
│   │   ├── store/                      # State management (Zustand/Redux)
│   │   │   ├── authStore.ts
│   │   │   ├── userStore.ts
│   │   │   ├── energyStore.ts
│   │   │   ├── taskStore.ts
│   │   │   ├── habitStore.ts
│   │   │   └── notificationStore.ts
│   │   ├── types/                      # TypeScript type definitions
│   │   │   ├── auth.ts
│   │   │   ├── user.ts
│   │   │   ├── energy.ts
│   │   │   ├── tasks.ts
│   │   │   ├── habits.ts
│   │   │   ├── analytics.ts
│   │   │   └── api.ts
│   │   └── styles/                     # Additional styles
│   │       ├── components.css
│   │       └── utilities.css
│   └── __tests__/                      # Frontend tests
│       ├── components/
│       ├── hooks/
│       ├── pages/
│       └── utils/
│
├── backend/                            # Node.js/Express Backend API
│   ├── package.json                    # Backend dependencies
│   ├── tsconfig.json                   # Backend TypeScript config
│   ├── .env.example                    # Backend environment variables
│   ├── Dockerfile                      # Docker configuration
│   ├── src/                            # Source code
│   │   ├── app.ts                      # Express app setup
│   │   ├── server.ts                   # Server entry point
│   │   ├── config/                     # Configuration files
│   │   │   ├── database.ts             # Database configuration
│   │   │   ├── redis.ts                # Redis configuration
│   │   │   ├── auth.ts                 # Authentication config
│   │   │   ├── ml.ts                   # ML model configuration
│   │   │   └── integrations.ts         # External API configurations
│   │   ├── controllers/                # Route controllers
│   │   │   ├── authController.ts
│   │   │   ├── userController.ts
│   │   │   ├── energyController.ts
│   │   │   ├── taskController.ts
│   │   │   ├── habitController.ts
│   │   │   ├── analyticsController.ts
│   │   │   ├── coachingController.ts
│   │   │   └── integrationController.ts
│   │   ├── middleware/                 # Express middleware
│   │   │   ├── auth.ts                 # Authentication middleware
│   │   │   ├── validation.ts           # Request validation
│   │   │   ├── rateLimit.ts            # Rate limiting
│   │   │   ├── cors.ts                 # CORS configuration
│   │   │   ├── logging.ts              # Request logging
│   │   │   └── errorHandler.ts         # Error handling
│   │   ├── models/                     # Database models (Prisma/TypeORM)
│   │   │   ├── User.ts
│   │   │   ├── EnergyData.ts
│   │   │   ├── Task.ts
│   │   │   ├── Habit.ts
│   │   │   ├── FlowSession.ts
│   │   │   ├── BiometricData.ts
│   │   │   └── Notification.ts
│   │   ├── routes/                     # API routes
│   │   │   ├── auth.ts
│   │   │   ├── users.ts
│   │   │   ├── energy.ts
│   │   │   ├── tasks.ts
│   │   │   ├── habits.ts
│   │   │   ├── analytics.ts
│   │   │   ├── coaching.ts
│   │   │   ├── integrations.ts
│   │   │   └── webhooks.ts
│   │   ├── services/                   # Business logic services
│   │   │   ├── authService.ts
│   │   │   ├── userService.ts
│   │   │   ├── energyAnalysisService.ts
│   │   │   ├── flowDetectionService.ts
│   │   │   ├── schedulingService.ts
│   │   │   ├── habitFormationService.ts
│   │   │   ├── coachingService.ts
│   │   │   ├── notificationService.ts
│   │   │   └── integrationService.ts
│   │   ├── ml/                         # Machine Learning modules
│   │   │   ├── circadianAnalysis.ts
│   │   │   ├── flowStateDetection.ts
│   │   │   ├── stressAnalysis.ts
│   │   │   ├── predictiveModels.ts
│   │   │   ├── behaviorAnalytics.ts
│   │   │   └── patternRecognition.ts
│   │   ├── integrations/               # External API integrations
│   │   │   ├── calendar/
│   │   │   │   ├── googleCalendar.ts
│   │   │   │   ├── outlookCalendar.ts
│   │   │   │   └── appleCalendar.ts
│   │   │   ├── fitness/
│   │   │   │   ├── fitbit.ts
│   │   │   │   ├── appleHealth.ts
│   │   │   │   ├── googleFit.ts
│   │   │   │   └── garmin.ts
│   │   │   ├── music/
│   │   │   │   ├── spotify.ts
│   │   │   │   └── appleMusic.ts
│   │   │   ├── weather/
│   │   │   │   └── openWeather.ts
│   │   │   └── notifications/
│   │   │       ├── email.ts
│   │   │       ├── push.ts
│   │   │       └── sms.ts
│   │   ├── utils/                      # Utility functions
│   │   │   ├── encryption.ts
│   │   │   ├── validation.ts
│   │   │   ├── dateTime.ts
│   │   │   ├── calculations.ts
│   │   │   ├── logger.ts
│   │   │   └── helpers.ts
│   │   ├── types/                      # TypeScript types
│   │   │   ├── api.ts
│   │   │   ├── database.ts
│   │   │   ├── integrations.ts
│   │   │   ├── ml.ts
│   │   │   └── common.ts
│   │   └── websocket/                  # WebSocket handlers
│   │       ├── socketServer.ts
│   │       ├── energyUpdates.ts
│   │       ├── notifications.ts
│   │       └── realTimeSync.ts
│   ├── prisma/                         # Database schema and migrations
│   │   ├── schema.prisma
│   │   ├── migrations/
│   │   └── seed.ts
│   └── __tests__/                      # Backend tests
│       ├── controllers/
│       ├── services/
│       ├── models/
│       ├── integrations/
│       └── utils/
│
├── shared/                             # Shared code between frontend and backend
│   ├── package.json
│   ├── src/
│   │   ├── types/                      # Shared TypeScript types
│   │   │   ├── user.ts
│   │   │   ├── energy.ts
│   │   │   ├── tasks.ts
│   │   │   ├── habits.ts
│   │   │   ├── analytics.ts
│   │   │   └── api.ts
│   │   ├── constants/                  # Shared constants
│   │   │   ├── energyLevels.ts
│   │   │   ├── taskTypes.ts
│   │   │   ├── habitCategories.ts
│   │   │   └── notifications.ts
│   │   ├── utils/                      # Shared utilities
│   │   │   ├── dateTime.ts
│   │   │   ├── calculations.ts
│   │   │   ├── validations.ts
│   │   │   └── formatters.ts
│   │   └── schemas/                    # Validation schemas (Zod)
│   │       ├── user.ts
│   │       ├── energy.ts
│   │       ├── tasks.ts
│   │       └── habits.ts
│   └── __tests__/
│
├── ml-models/                          # Machine Learning models and training
│   ├── package.json
│   ├── requirements.txt                # Python dependencies
│   ├── src/
│   │   ├── training/                   # Model training scripts
│   │   │   ├── circadian_model.py
│   │   │   ├── flow_detection_model.py
│   │   │   ├── stress_prediction_model.py
│   │   │   └── behavior_analysis_model.py
│   │   ├── models/                     # Trained model files
│   │   │   ├── circadian_rhythm.pkl
│   │   │   ├── flow_state_detector.pkl
│   │   │   ├── stress_predictor.pkl
│   │   │   └── behavior_classifier.pkl
│   │   ├── preprocessing/              # Data preprocessing
│   │   │   ├── energy_data_processor.py
│   │   │   ├── biometric_processor.py
│   │   │   └── behavioral_processor.py
│   │   ├── evaluation/                 # Model evaluation
│   │   │   ├── metrics.py
│   │   │   ├── validation.py
│   │   │   └── performance_analysis.py
│   │   └── deployment/                 # Model deployment utilities
│   │       ├── model_server.py
│   │       ├── api_wrapper.py
│   │       └── batch_processor.py
│   ├── data/                           # Training data and datasets
│   │   ├── raw/
│   │   ├── processed/
│   │   └── synthetic/
│   ├── notebooks/                      # Jupyter notebooks for research
│   │   ├── exploratory_analysis.ipynb
│   │   ├── model_development.ipynb
│   │   └── performance_evaluation.ipynb
│   └── __tests__/
│
├── mobile/                             # React Native mobile app (future)
│   ├── package.json
│   ├── metro.config.js
│   ├── babel.config.js
│   ├── android/
│   ├── ios/
│   └── src/
│
├── docs/                               # Documentation
│   ├── api/                            # API documentation
│   │   ├── openapi.yaml
│   │   ├── authentication.md
│   │   ├── endpoints.md
│   │   └── webhooks.md
│   ├── architecture/                   # Architecture documentation
│   │   ├── system-overview.md
│   │   ├── data-flow.md
│   │   ├── security.md
│   │   └── scalability.md
│   ├── deployment/                     # Deployment guides
│   │   ├── docker.md
│   │   ├── kubernetes.md
│   │   ├── aws.md
│   │   └── monitoring.md
│   ├── development/                    # Development guides
│   │   ├── setup.md
│   │   ├── testing.md
│   │   ├── contributing.md
│   │   └── code-style.md
│   └── user/                           # User documentation
│       ├── getting-started.md
│       ├── features.md
│       ├── integrations.md
│       └── troubleshooting.md
│
├── scripts/                            # Build and deployment scripts
│   ├── build.sh                       # Build script
│   ├── deploy.sh                      # Deployment script
│   ├── test.sh                        # Test runner
│   ├── migrate.sh                     # Database migration
│   ├── seed.sh                        # Database seeding
│   ├── backup.sh                      # Backup script
│   └── setup-dev.sh                   # Development environment setup
│
├── infrastructure/                     # Infrastructure as Code
│   ├── docker/                         # Docker configurations
│   │   ├── Dockerfile.frontend
│   │   ├── Dockerfile.backend
│   │   ├── Dockerfile.ml
│   │   └── docker-compose.prod.yml
│   ├── kubernetes/                     # Kubernetes manifests
│   │   ├── namespace.yaml
│   │   ├── configmap.yaml
│   │   ├── secrets.yaml
│   │   ├── deployments/
│   │   ├── services/
│   │   └── ingress/
│   ├── terraform/                      # Terraform configurations
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   ├── outputs.tf
│   │   └── modules/
│   └── monitoring/                     # Monitoring configurations
│       ├── prometheus.yml
│       ├── grafana/
│       └── alertmanager.yml
│
├── tests/                              # Integration and E2E tests
│   ├── e2e/                            # End-to-end tests
│   │   ├── cypress/
│   │   ├── playwright/
│   │   └── fixtures/
│   ├── integration/                    # Integration tests
│   │   ├── api/
│   │   ├── database/
│   │   └── external-services/
│   ├── performance/                    # Performance tests
│   │   ├── load-testing/
│   │   └── stress-testing/
│   └── security/                       # Security tests
│       ├── penetration/
│       └── vulnerability/
│
└── .github/                            # GitHub workflows and templates
    ├── workflows/                      # GitHub Actions
    │   ├── ci.yml                      # Continuous Integration
    │   ├── cd.yml                      # Continuous Deployment
    │   ├── security.yml                # Security scanning
    │   ├── performance.yml             # Performance testing
    │   └── release.yml                 # Release automation
    ├── ISSUE_TEMPLATE/                 # Issue templates
    │   ├── bug_report.md
    │   ├── feature_request.md
    │   └── security_report.md
    ├── PULL_REQUEST_TEMPLATE.md        # PR template
    └── CODEOWNERS                      # Code ownership
```

## Key Architecture Decisions

### 1. **Monorepo Structure**
- Single repository with multiple packages
- Shared code between frontend and backend
- Consistent tooling and dependencies

### 2. **Technology Stack**
- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Node.js, Express, TypeScript, Prisma ORM
- **Database**: PostgreSQL (main), Redis (cache), InfluxDB (time-series)
- **ML**: Python for training, TensorFlow.js for inference
- **Mobile**: React Native (future expansion)

### 3. **Microservices Architecture**
- Modular service design
- Independent scaling capabilities
- Clear separation of concerns
- API-first approach

### 4. **Security & Privacy**
- End-to-end encryption
- Privacy-first data handling
- GDPR compliance ready
- Secure authentication (JWT + OAuth2)

### 5. **Development Workflow**
- TypeScript throughout
- Comprehensive testing strategy
- CI/CD with GitHub Actions
- Docker containerization
- Kubernetes orchestration

This structure provides a solid foundation for building a scalable, maintainable, and secure RhythmAI application.
