'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Zap, Target, CheckSquare, Calendar } from 'lucide-react';
import { Menu, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { cn } from '@/lib/utils';

const quickActions = [
  {
    name: 'Log Energy',
    description: 'Record your current energy level',
    icon: Zap,
    href: '/app/energy/log',
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
  },
  {
    name: 'Start Flow Session',
    description: 'Begin a focused work session',
    icon: Target,
    href: '/app/flow/start',
    color: 'text-purple-600',
    bgColor: 'bg-purple-100 dark:bg-purple-900/20',
  },
  {
    name: 'Complete Habit',
    description: 'Mark a habit as completed',
    icon: CheckSquare,
    href: '/app/habits',
    color: 'text-green-600',
    bgColor: 'bg-green-100 dark:bg-green-900/20',
  },
  {
    name: 'Add Task',
    description: 'Create a new task',
    icon: Calendar,
    href: '/app/tasks/new',
    color: 'text-blue-600',
    bgColor: 'bg-blue-100 dark:bg-blue-900/20',
  },
];

export function QuickActions() {
  const router = useRouter();

  const handleAction = (href: string) => {
    router.push(href);
  };

  return (
    <Menu as="div" className="relative">
      <Menu.Button className="btn-primary btn-md flex items-center space-x-2">
        <Plus className="w-4 h-4" />
        <span>Quick Action</span>
      </Menu.Button>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute right-0 mt-2 w-72 origin-top-right bg-white dark:bg-gray-800 rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
          <div className="p-2">
            <div className="px-3 py-2 border-b border-gray-100 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Quick Actions
              </h3>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Jump to common tasks
              </p>
            </div>
            <div className="py-2 space-y-1">
              {quickActions.map((action) => {
                const Icon = action.icon;
                return (
                  <Menu.Item key={action.name}>
                    {({ active }) => (
                      <button
                        onClick={() => handleAction(action.href)}
                        className={cn(
                          'flex items-center w-full px-3 py-3 text-left rounded-md transition-colors',
                          active ? 'bg-gray-50 dark:bg-gray-700' : ''
                        )}
                      >
                        <div className={cn('p-2 rounded-lg mr-3', action.bgColor)}>
                          <Icon className={cn('w-4 h-4', action.color)} />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {action.name}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {action.description}
                          </p>
                        </div>
                      </button>
                    )}
                  </Menu.Item>
                );
              })}
            </div>
          </div>
        </Menu.Items>
      </Transition>
    </Menu>
  );
}
