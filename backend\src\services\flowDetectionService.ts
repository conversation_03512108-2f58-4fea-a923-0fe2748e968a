import { prisma } from '../config/database';
import { cacheService, CacheKeys } from '../config/redis';
import { logger } from '../utils/logger';

// Flow state interfaces
export interface FlowSession {
  id: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  duration?: number; // in minutes
  intensity: FlowIntensity;
  taskType: string;
  environment: FlowEnvironment;
  interruptions: number;
  productivity: number; // 1-10 scale
  satisfaction: number; // 1-10 scale
  triggers: string[];
  notes?: string;
}

export enum FlowIntensity {
  LIGHT = 'light',
  MODERATE = 'moderate',
  DEEP = 'deep',
  PEAK = 'peak',
}

export interface FlowEnvironment {
  location: string;
  noiseLevel: number; // 1-10 scale
  lighting: string;
  temperature: number;
  musicType?: string;
  distractions: string[];
}

export interface FlowPattern {
  userId: string;
  optimalConditions: {
    timeOfDay: number[];
    duration: number;
    environment: Partial<FlowEnvironment>;
    taskTypes: string[];
  };
  averageIntensity: FlowIntensity;
  successRate: number;
  totalFlowTime: number; // in minutes
  lastUpdated: Date;
}

export interface FlowPrediction {
  timestamp: Date;
  probability: number;
  optimalDuration: number;
  recommendedEnvironment: Partial<FlowEnvironment>;
  confidence: number;
}

export class FlowDetectionService {
  private static instance: FlowDetectionService;

  private constructor() {}

  public static getInstance(): FlowDetectionService {
    if (!FlowDetectionService.instance) {
      FlowDetectionService.instance = new FlowDetectionService();
    }
    return FlowDetectionService.instance;
  }

  // Start a new flow session
  async startFlowSession(
    userId: string,
    taskType: string,
    environment: FlowEnvironment
  ): Promise<FlowSession> {
    try {
      const session = await prisma.flowSession.create({
        data: {
          userId,
          startTime: new Date(),
          taskType,
          environment: JSON.stringify(environment),
          interruptions: 0,
          intensity: FlowIntensity.LIGHT,
          productivity: 5,
          satisfaction: 5,
          triggers: [],
        },
      });

      const flowSession: FlowSession = {
        id: session.id,
        userId: session.userId,
        startTime: session.startTime,
        intensity: session.intensity as FlowIntensity,
        taskType: session.taskType,
        environment: JSON.parse(session.environment),
        interruptions: session.interruptions,
        productivity: session.productivity,
        satisfaction: session.satisfaction,
        triggers: session.triggers,
        notes: session.notes || undefined,
      };

      // Cache active session
      const cacheKey = CacheKeys.analyticsData(userId, 'active-flow-session');
      await cacheService.set(cacheKey, flowSession, 3600); // 1 hour

      logger.info(`Flow session started for user ${userId}`);
      return flowSession;
    } catch (error) {
      logger.error('Error starting flow session:', error);
      throw error;
    }
  }

  // Update flow session with real-time data
  async updateFlowSession(
    sessionId: string,
    updates: {
      intensity?: FlowIntensity;
      interruptions?: number;
      productivity?: number;
      satisfaction?: number;
      triggers?: string[];
      notes?: string;
    }
  ): Promise<FlowSession> {
    try {
      const session = await prisma.flowSession.update({
        where: { id: sessionId },
        data: {
          ...updates,
          intensity: updates.intensity,
          interruptions: updates.interruptions,
          productivity: updates.productivity,
          satisfaction: updates.satisfaction,
          triggers: updates.triggers,
          notes: updates.notes,
        },
      });

      const flowSession: FlowSession = {
        id: session.id,
        userId: session.userId,
        startTime: session.startTime,
        endTime: session.endTime || undefined,
        duration: session.duration || undefined,
        intensity: session.intensity as FlowIntensity,
        taskType: session.taskType,
        environment: JSON.parse(session.environment),
        interruptions: session.interruptions,
        productivity: session.productivity,
        satisfaction: session.satisfaction,
        triggers: session.triggers,
        notes: session.notes || undefined,
      };

      // Update cache
      const cacheKey = CacheKeys.analyticsData(session.userId, 'active-flow-session');
      await cacheService.set(cacheKey, flowSession, 3600);

      return flowSession;
    } catch (error) {
      logger.error('Error updating flow session:', error);
      throw error;
    }
  }

  // End a flow session
  async endFlowSession(
    sessionId: string,
    finalData: {
      productivity: number;
      satisfaction: number;
      notes?: string;
    }
  ): Promise<FlowSession> {
    try {
      const endTime = new Date();
      const session = await prisma.flowSession.findUnique({
        where: { id: sessionId },
      });

      if (!session) {
        throw new Error('Flow session not found');
      }

      const duration = Math.round((endTime.getTime() - session.startTime.getTime()) / (1000 * 60));

      const updatedSession = await prisma.flowSession.update({
        where: { id: sessionId },
        data: {
          endTime,
          duration,
          productivity: finalData.productivity,
          satisfaction: finalData.satisfaction,
          notes: finalData.notes,
        },
      });

      const flowSession: FlowSession = {
        id: updatedSession.id,
        userId: updatedSession.userId,
        startTime: updatedSession.startTime,
        endTime: updatedSession.endTime!,
        duration: updatedSession.duration!,
        intensity: updatedSession.intensity as FlowIntensity,
        taskType: updatedSession.taskType,
        environment: JSON.parse(updatedSession.environment),
        interruptions: updatedSession.interruptions,
        productivity: updatedSession.productivity,
        satisfaction: updatedSession.satisfaction,
        triggers: updatedSession.triggers,
        notes: updatedSession.notes || undefined,
      };

      // Clear active session cache
      const cacheKey = CacheKeys.analyticsData(session.userId, 'active-flow-session');
      await cacheService.del(cacheKey);

      // Update flow pattern
      await this.updateFlowPattern(session.userId);

      logger.info(`Flow session ended for user ${session.userId}, duration: ${duration} minutes`);
      return flowSession;
    } catch (error) {
      logger.error('Error ending flow session:', error);
      throw error;
    }
  }

  // Get active flow session
  async getActiveFlowSession(userId: string): Promise<FlowSession | null> {
    try {
      const cacheKey = CacheKeys.analyticsData(userId, 'active-flow-session');
      let session = await cacheService.get<FlowSession>(cacheKey);

      if (!session) {
        const dbSession = await prisma.flowSession.findFirst({
          where: {
            userId,
            endTime: null,
          },
          orderBy: { startTime: 'desc' },
        });

        if (dbSession) {
          session = {
            id: dbSession.id,
            userId: dbSession.userId,
            startTime: dbSession.startTime,
            intensity: dbSession.intensity as FlowIntensity,
            taskType: dbSession.taskType,
            environment: JSON.parse(dbSession.environment),
            interruptions: dbSession.interruptions,
            productivity: dbSession.productivity,
            satisfaction: dbSession.satisfaction,
            triggers: dbSession.triggers,
            notes: dbSession.notes || undefined,
          };

          await cacheService.set(cacheKey, session, 3600);
        }
      }

      return session;
    } catch (error) {
      logger.error('Error getting active flow session:', error);
      return null;
    }
  }

  // Get flow sessions history
  async getFlowSessions(
    userId: string,
    startDate: Date,
    endDate: Date,
    limit: number = 50
  ): Promise<FlowSession[]> {
    try {
      const sessions = await prisma.flowSession.findMany({
        where: {
          userId,
          startTime: {
            gte: startDate,
            lte: endDate,
          },
          endTime: { not: null },
        },
        orderBy: { startTime: 'desc' },
        take: limit,
      });

      return sessions.map(session => ({
        id: session.id,
        userId: session.userId,
        startTime: session.startTime,
        endTime: session.endTime!,
        duration: session.duration!,
        intensity: session.intensity as FlowIntensity,
        taskType: session.taskType,
        environment: JSON.parse(session.environment),
        interruptions: session.interruptions,
        productivity: session.productivity,
        satisfaction: session.satisfaction,
        triggers: session.triggers,
        notes: session.notes || undefined,
      }));
    } catch (error) {
      logger.error('Error getting flow sessions:', error);
      throw error;
    }
  }

  // Analyze and update flow pattern
  async updateFlowPattern(userId: string): Promise<FlowPattern> {
    try {
      // Get last 30 days of flow sessions
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
      
      const sessions = await this.getFlowSessions(userId, startDate, endDate, 100);

      if (sessions.length < 3) {
        throw new Error('Insufficient flow sessions for pattern analysis');
      }

      // Analyze optimal conditions
      const optimalConditions = this.analyzeOptimalConditions(sessions);
      
      // Calculate average intensity
      const intensityScores = sessions.map(s => this.getIntensityScore(s.intensity));
      const avgIntensityScore = intensityScores.reduce((sum, score) => sum + score, 0) / intensityScores.length;
      const averageIntensity = this.getIntensityFromScore(avgIntensityScore);

      // Calculate success rate (sessions with productivity >= 7)
      const successfulSessions = sessions.filter(s => s.productivity >= 7);
      const successRate = successfulSessions.length / sessions.length;

      // Calculate total flow time
      const totalFlowTime = sessions.reduce((sum, s) => sum + (s.duration || 0), 0);

      const pattern: FlowPattern = {
        userId,
        optimalConditions,
        averageIntensity,
        successRate,
        totalFlowTime,
        lastUpdated: new Date(),
      };

      // Store pattern in database
      await prisma.flowPattern.upsert({
        where: { userId },
        update: {
          optimalConditions: JSON.stringify(pattern.optimalConditions),
          averageIntensity: pattern.averageIntensity,
          successRate: pattern.successRate,
          totalFlowTime: pattern.totalFlowTime,
          lastUpdated: pattern.lastUpdated,
        },
        create: {
          userId,
          optimalConditions: JSON.stringify(pattern.optimalConditions),
          averageIntensity: pattern.averageIntensity,
          successRate: pattern.successRate,
          totalFlowTime: pattern.totalFlowTime,
          lastUpdated: pattern.lastUpdated,
        },
      });

      // Cache the pattern
      const cacheKey = CacheKeys.analyticsData(userId, 'flow-pattern');
      await cacheService.set(cacheKey, pattern, 3600);

      logger.info(`Flow pattern updated for user ${userId}`);
      return pattern;
    } catch (error) {
      logger.error('Error updating flow pattern:', error);
      throw error;
    }
  }

  // Get user's flow pattern
  async getFlowPattern(userId: string): Promise<FlowPattern | null> {
    try {
      const cacheKey = CacheKeys.analyticsData(userId, 'flow-pattern');
      let pattern = await cacheService.get<FlowPattern>(cacheKey);

      if (!pattern) {
        const dbPattern = await prisma.flowPattern.findUnique({
          where: { userId },
        });

        if (dbPattern) {
          pattern = {
            userId: dbPattern.userId,
            optimalConditions: JSON.parse(dbPattern.optimalConditions),
            averageIntensity: dbPattern.averageIntensity as FlowIntensity,
            successRate: dbPattern.successRate,
            totalFlowTime: dbPattern.totalFlowTime,
            lastUpdated: dbPattern.lastUpdated,
          };

          await cacheService.set(cacheKey, pattern, 3600);
        }
      }

      return pattern;
    } catch (error) {
      logger.error('Error getting flow pattern:', error);
      return null;
    }
  }

  // Predict optimal flow time
  async predictOptimalFlowTime(userId: string, targetDate: Date): Promise<FlowPrediction> {
    try {
      const pattern = await this.getFlowPattern(userId);
      if (!pattern) {
        throw new Error('No flow pattern available for prediction');
      }

      const hour = targetDate.getHours();
      const isOptimalTime = pattern.optimalConditions.timeOfDay.includes(hour);
      
      // Base probability on historical success rate and time of day
      let probability = pattern.successRate;
      if (isOptimalTime) {
        probability *= 1.3; // Boost for optimal time
      } else {
        probability *= 0.7; // Reduce for non-optimal time
      }

      probability = Math.min(1, Math.max(0, probability));

      const prediction: FlowPrediction = {
        timestamp: targetDate,
        probability,
        optimalDuration: pattern.optimalConditions.duration,
        recommendedEnvironment: pattern.optimalConditions.environment,
        confidence: pattern.successRate,
      };

      return prediction;
    } catch (error) {
      logger.error('Error predicting optimal flow time:', error);
      throw error;
    }
  }

  // Analyze optimal conditions from sessions
  private analyzeOptimalConditions(sessions: FlowSession[]): any {
    const successfulSessions = sessions.filter(s => s.productivity >= 7);
    
    if (successfulSessions.length === 0) {
      return {
        timeOfDay: [],
        duration: 60,
        environment: {},
        taskTypes: [],
      };
    }

    // Analyze time of day
    const timeOfDay = successfulSessions.map(s => s.startTime.getHours());
    const timeFrequency: { [hour: number]: number } = {};
    timeOfDay.forEach(hour => {
      timeFrequency[hour] = (timeFrequency[hour] || 0) + 1;
    });

    const optimalHours = Object.entries(timeFrequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 6)
      .map(([hour]) => parseInt(hour));

    // Analyze duration
    const durations = successfulSessions.map(s => s.duration || 60);
    const avgDuration = Math.round(durations.reduce((sum, d) => sum + d, 0) / durations.length);

    // Analyze environment
    const environments = successfulSessions.map(s => s.environment);
    const environmentAnalysis = this.analyzeEnvironments(environments);

    // Analyze task types
    const taskTypes = successfulSessions.map(s => s.taskType);
    const taskFrequency: { [task: string]: number } = {};
    taskTypes.forEach(task => {
      taskFrequency[task] = (taskFrequency[task] || 0) + 1;
    });

    const optimalTaskTypes = Object.entries(taskFrequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([task]) => task);

    return {
      timeOfDay: optimalHours,
      duration: avgDuration,
      environment: environmentAnalysis,
      taskTypes: optimalTaskTypes,
    };
  }

  private analyzeEnvironments(environments: FlowEnvironment[]): Partial<FlowEnvironment> {
    if (environments.length === 0) return {};

    const avgNoiseLevel = Math.round(
      environments.reduce((sum, env) => sum + env.noiseLevel, 0) / environments.length
    );

    const locationFreq: { [loc: string]: number } = {};
    const lightingFreq: { [light: string]: number } = {};
    const musicFreq: { [music: string]: number } = {};

    environments.forEach(env => {
      locationFreq[env.location] = (locationFreq[env.location] || 0) + 1;
      lightingFreq[env.lighting] = (lightingFreq[env.lighting] || 0) + 1;
      if (env.musicType) {
        musicFreq[env.musicType] = (musicFreq[env.musicType] || 0) + 1;
      }
    });

    const optimalLocation = Object.entries(locationFreq).sort(([,a], [,b]) => b - a)[0]?.[0];
    const optimalLighting = Object.entries(lightingFreq).sort(([,a], [,b]) => b - a)[0]?.[0];
    const optimalMusic = Object.entries(musicFreq).sort(([,a], [,b]) => b - a)[0]?.[0];

    return {
      location: optimalLocation,
      noiseLevel: avgNoiseLevel,
      lighting: optimalLighting,
      musicType: optimalMusic,
    };
  }

  private getIntensityScore(intensity: FlowIntensity): number {
    switch (intensity) {
      case FlowIntensity.LIGHT: return 1;
      case FlowIntensity.MODERATE: return 2;
      case FlowIntensity.DEEP: return 3;
      case FlowIntensity.PEAK: return 4;
      default: return 2;
    }
  }

  private getIntensityFromScore(score: number): FlowIntensity {
    if (score <= 1.5) return FlowIntensity.LIGHT;
    if (score <= 2.5) return FlowIntensity.MODERATE;
    if (score <= 3.5) return FlowIntensity.DEEP;
    return FlowIntensity.PEAK;
  }
}
