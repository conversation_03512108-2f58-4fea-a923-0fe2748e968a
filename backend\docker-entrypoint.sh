#!/bin/sh
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "${GREEN}🚀 Starting RhythmAI Backend...${NC}"

# Function to wait for database
wait_for_db() {
    echo "${YELLOW}⏳ Waiting for database to be ready...${NC}"
    
    # Extract database connection details from DATABASE_URL
    DB_HOST=$(echo $DATABASE_URL | sed -n 's/.*@\([^:]*\):.*/\1/p')
    DB_PORT=$(echo $DATABASE_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
    
    if [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ]; then
        echo "${RED}❌ Could not parse DATABASE_URL${NC}"
        exit 1
    fi
    
    # Wait for database to be ready
    until pg_isready -h "$DB_HOST" -p "$DB_PORT" -q; do
        echo "${YELLOW}⏳ Database is unavailable - sleeping...${NC}"
        sleep 2
    done
    
    echo "${GREEN}✅ Database is ready!${NC}"
}

# Function to wait for Redis
wait_for_redis() {
    echo "${YELLOW}⏳ Waiting for Redis to be ready...${NC}"
    
    # Extract Redis connection details from REDIS_URL
    REDIS_HOST=$(echo $REDIS_URL | sed -n 's/redis:\/\/\([^:]*\):.*/\1/p')
    REDIS_PORT=$(echo $REDIS_URL | sed -n 's/.*:\([0-9]*\).*/\1/p')
    
    if [ -z "$REDIS_HOST" ]; then
        REDIS_HOST="localhost"
    fi
    
    if [ -z "$REDIS_PORT" ]; then
        REDIS_PORT="6379"
    fi
    
    # Simple Redis connectivity check
    until nc -z "$REDIS_HOST" "$REDIS_PORT"; do
        echo "${YELLOW}⏳ Redis is unavailable - sleeping...${NC}"
        sleep 2
    done
    
    echo "${GREEN}✅ Redis is ready!${NC}"
}

# Function to run database migrations
run_migrations() {
    echo "${YELLOW}🔄 Running database migrations...${NC}"
    
    if npx prisma migrate deploy; then
        echo "${GREEN}✅ Database migrations completed successfully!${NC}"
    else
        echo "${RED}❌ Database migrations failed!${NC}"
        exit 1
    fi
}

# Function to generate Prisma client
generate_prisma_client() {
    echo "${YELLOW}🔄 Generating Prisma client...${NC}"
    
    if npx prisma generate; then
        echo "${GREEN}✅ Prisma client generated successfully!${NC}"
    else
        echo "${RED}❌ Prisma client generation failed!${NC}"
        exit 1
    fi
}

# Function to seed database (optional)
seed_database() {
    if [ "$SEED_DATABASE" = "true" ]; then
        echo "${YELLOW}🌱 Seeding database...${NC}"
        
        if npx prisma db seed; then
            echo "${GREEN}✅ Database seeded successfully!${NC}"
        else
            echo "${YELLOW}⚠️ Database seeding failed or no seed script found${NC}"
        fi
    fi
}

# Function to validate environment variables
validate_env() {
    echo "${YELLOW}🔍 Validating environment variables...${NC}"
    
    required_vars="DATABASE_URL JWT_SECRET"
    
    for var in $required_vars; do
        if [ -z "$(eval echo \$$var)" ]; then
            echo "${RED}❌ Required environment variable $var is not set${NC}"
            exit 1
        fi
    done
    
    echo "${GREEN}✅ Environment variables validated!${NC}"
}

# Function to create necessary directories
create_directories() {
    echo "${YELLOW}📁 Creating necessary directories...${NC}"
    
    mkdir -p logs uploads temp
    
    echo "${GREEN}✅ Directories created!${NC}"
}

# Function to check disk space
check_disk_space() {
    echo "${YELLOW}💾 Checking disk space...${NC}"
    
    available_space=$(df /app | awk 'NR==2 {print $4}')
    
    if [ "$available_space" -lt 1048576 ]; then # Less than 1GB
        echo "${YELLOW}⚠️ Warning: Low disk space available${NC}"
    else
        echo "${GREEN}✅ Sufficient disk space available${NC}"
    fi
}

# Function to start the application
start_app() {
    echo "${GREEN}🎯 Starting RhythmAI Backend Server...${NC}"
    echo "${GREEN}📊 Environment: $NODE_ENV${NC}"
    echo "${GREEN}🌐 Port: ${PORT:-8000}${NC}"
    
    # Start the Node.js application
    exec node dist/server.js
}

# Main execution flow
main() {
    echo "${GREEN}🔧 RhythmAI Backend Initialization${NC}"
    echo "${GREEN}===================================${NC}"
    
    # Validate environment
    validate_env
    
    # Create directories
    create_directories
    
    # Check disk space
    check_disk_space
    
    # Wait for dependencies if not in development
    if [ "$NODE_ENV" != "development" ]; then
        wait_for_db
        
        if [ -n "$REDIS_URL" ]; then
            wait_for_redis
        fi
    fi
    
    # Generate Prisma client
    generate_prisma_client
    
    # Run migrations in production
    if [ "$NODE_ENV" = "production" ]; then
        run_migrations
        seed_database
    fi
    
    # Start the application
    start_app
}

# Handle signals for graceful shutdown
trap 'echo "${YELLOW}🛑 Received shutdown signal, stopping gracefully...${NC}"; exit 0' TERM INT

# Run main function
main "$@"
