// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model with authentication and profile data
model User {
  id                String    @id @default(cuid())
  email             String    @unique
  password          String?   // Optional for OAuth users
  firstName         String?
  lastName          String?
  displayName       String?
  avatar            String?
  timezone          String    @default("UTC")
  role              UserRole  @default(USER)
  isActive          Boolean   @default(true)
  isEmailVerified   Boolean   @default(false)
  isPremium         Boolean   @default(false)
  onboardingCompleted Boolean @default(false)
  
  // Timestamps
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  lastLoginAt       DateTime?
  emailVerifiedAt   DateTime?
  
  // User preferences
  preferences       UserPreferences?
  
  // Related data
  energyData        EnergyData[]
  flowSessions      FlowSession[]
  tasks             Task[]
  habits            Habit[]
  notifications     Notification[]
  integrations      Integration[]
  coachingSessions  CoachingSession[]
  
  // Patterns and analytics
  circadianPattern  CircadianPattern?
  flowPattern       FlowPattern?
  habitPatterns     HabitPattern[]
  
  @@map("users")
}

enum UserRole {
  USER
  PREMIUM
  ADMIN
}

// User preferences and settings
model UserPreferences {
  id                    String   @id @default(cuid())
  userId                String   @unique
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Notification preferences
  emailNotifications    Boolean  @default(true)
  pushNotifications     Boolean  @default(true)
  smsNotifications      Boolean  @default(false)
  
  // Privacy settings
  dataSharing           Boolean  @default(false)
  analyticsOptIn        Boolean  @default(true)
  
  // App preferences
  theme                 String   @default("light")
  language              String   @default("en")
  workingHours          Json     // { start: "09:00", end: "17:00" }
  workingDays           String[] @default(["monday", "tuesday", "wednesday", "thursday", "friday"])
  
  // Energy tracking preferences
  energyReminderFreq    Int      @default(4) // hours
  energyReminderEnabled Boolean  @default(true)
  
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  
  @@map("user_preferences")
}

// Energy data points for circadian rhythm analysis
model EnergyData {
  id               String   @id @default(cuid())
  userId           String
  user             User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  timestamp        DateTime
  energyLevel      Int      // 1-5 scale
  mood             Int      // 1-10 scale
  stressLevel      Int      // 1-10 scale
  sleepQuality     Int?     // 1-10 scale
  physicalActivity Int?     // minutes
  caffeine         Int?     // mg
  context          String?  // free text context
  
  // Environmental factors
  weather          String?
  temperature      Float?
  
  createdAt        DateTime @default(now())
  
  @@map("energy_data")
  @@index([userId, timestamp])
}

// Circadian rhythm patterns
model CircadianPattern {
  id               String   @id @default(cuid())
  userId           String   @unique
  user             User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  peakEnergyHours  Int[]    // Array of hours (0-23)
  lowEnergyHours   Int[]    // Array of hours (0-23)
  averagePattern   Float[]  // 24-hour average energy pattern
  confidence       Float    // 0-1 confidence score
  
  lastUpdated      DateTime @updatedAt
  createdAt        DateTime @default(now())
  
  @@map("circadian_patterns")
}

// Flow state sessions
model FlowSession {
  id            String        @id @default(cuid())
  userId        String
  user          User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  startTime     DateTime
  endTime       DateTime?
  duration      Int?          // minutes
  intensity     FlowIntensity @default(LIGHT)
  taskType      String
  environment   String        // JSON string of environment data
  interruptions Int           @default(0)
  productivity  Int           @default(5) // 1-10 scale
  satisfaction  Int           @default(5) // 1-10 scale
  triggers      String[]      @default([])
  notes         String?
  
  createdAt     DateTime      @default(now())
  
  @@map("flow_sessions")
  @@index([userId, startTime])
}

enum FlowIntensity {
  LIGHT
  MODERATE
  DEEP
  PEAK
}

// Flow state patterns
model FlowPattern {
  id                String   @id @default(cuid())
  userId            String   @unique
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  optimalConditions String   // JSON string of optimal conditions
  averageIntensity  FlowIntensity @default(MODERATE)
  successRate       Float    // 0-1 success rate
  totalFlowTime     Int      // total minutes in flow
  
  lastUpdated       DateTime @updatedAt
  createdAt         DateTime @default(now())
  
  @@map("flow_patterns")
}

// Tasks and scheduling
model Task {
  id              String      @id @default(cuid())
  userId          String
  user            User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  title           String
  description     String?
  taskType        TaskType    @default(WORK)
  priority        Priority    @default(MEDIUM)
  status          TaskStatus  @default(PENDING)
  
  // Scheduling
  scheduledAt     DateTime?
  dueDate         DateTime?
  estimatedDuration Int?      // minutes
  actualDuration  Int?        // minutes
  
  // Energy requirements
  energyRequired  Int         @default(3) // 1-5 scale
  focusRequired   Boolean     @default(false)
  
  // Completion tracking
  completedAt     DateTime?
  productivity    Int?        // 1-10 scale
  satisfaction    Int?        // 1-10 scale
  
  // Relationships
  habitId         String?
  habit           Habit?      @relation(fields: [habitId], references: [id])
  
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  
  @@map("tasks")
  @@index([userId, scheduledAt])
  @@index([userId, status])
}

enum TaskType {
  WORK
  PERSONAL
  HEALTH
  LEARNING
  CREATIVE
  ADMINISTRATIVE
  SOCIAL
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
  RESCHEDULED
}

// Habits and habit formation
model Habit {
  id              String       @id @default(cuid())
  userId          String
  user            User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  name            String
  description     String?
  category        HabitCategory @default(PRODUCTIVITY)
  
  // Habit configuration
  frequency       HabitFrequency @default(DAILY)
  targetValue     Int          @default(1) // target repetitions/duration
  unit            String       @default("times") // times, minutes, hours, etc.
  
  // Scheduling
  preferredTime   String?      // HH:MM format
  reminderEnabled Boolean      @default(true)
  
  // Tracking
  isActive        Boolean      @default(true)
  streak          Int          @default(0)
  longestStreak   Int          @default(0)
  totalCompletions Int         @default(0)
  
  // Related data
  tasks           Task[]
  completions     HabitCompletion[]
  patterns        HabitPattern[]
  
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  
  @@map("habits")
  @@index([userId, isActive])
}

enum HabitCategory {
  PRODUCTIVITY
  HEALTH
  LEARNING
  MINDFULNESS
  SOCIAL
  CREATIVE
  FINANCIAL
}

enum HabitFrequency {
  DAILY
  WEEKLY
  MONTHLY
  CUSTOM
}

// Habit completion tracking
model HabitCompletion {
  id          String   @id @default(cuid())
  habitId     String
  habit       Habit    @relation(fields: [habitId], references: [id], onDelete: Cascade)
  
  completedAt DateTime
  value       Int      @default(1) // actual repetitions/duration
  notes       String?
  mood        Int?     // 1-10 scale
  difficulty  Int?     // 1-10 scale
  
  createdAt   DateTime @default(now())
  
  @@map("habit_completions")
  @@index([habitId, completedAt])
}

// Habit patterns and analytics
model HabitPattern {
  id              String   @id @default(cuid())
  userId          String
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  habitId         String
  habit           Habit    @relation(fields: [habitId], references: [id], onDelete: Cascade)
  
  optimalTime     String?  // HH:MM format
  successRate     Float    // 0-1 success rate
  averageMood     Float?   // average mood when completing
  streakPattern   Json     // streak analysis data
  
  lastUpdated     DateTime @updatedAt
  createdAt       DateTime @default(now())
  
  @@map("habit_patterns")
  @@unique([userId, habitId])
}

// AI coaching sessions and insights
model CoachingSession {
  id              String         @id @default(cuid())
  userId          String
  user            User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  sessionType     CoachingType   @default(WEEKLY_REVIEW)
  insights        String         // JSON string of insights
  recommendations String         // JSON string of recommendations
  goals           String?        // JSON string of goals
  
  // Metrics
  productivityScore Float?       // 0-10 score
  wellnessScore   Float?         // 0-10 score
  progressScore   Float?         // 0-10 score
  
  createdAt       DateTime       @default(now())
  
  @@map("coaching_sessions")
  @@index([userId, createdAt])
}

enum CoachingType {
  DAILY_CHECKIN
  WEEKLY_REVIEW
  MONTHLY_ANALYSIS
  GOAL_SETTING
  HABIT_COACHING
  ENERGY_OPTIMIZATION
}

// External integrations
model Integration {
  id            String           @id @default(cuid())
  userId        String
  user          User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  provider      IntegrationType
  isActive      Boolean          @default(true)
  accessToken   String?          // Encrypted
  refreshToken  String?          // Encrypted
  expiresAt     DateTime?
  
  // Provider-specific data
  providerUserId String?
  providerData  String?          // JSON string of provider-specific data
  
  lastSyncAt    DateTime?
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  
  @@map("integrations")
  @@unique([userId, provider])
}

enum IntegrationType {
  GOOGLE_CALENDAR
  OUTLOOK_CALENDAR
  APPLE_CALENDAR
  FITBIT
  APPLE_HEALTH
  GOOGLE_FIT
  GARMIN
  SPOTIFY
  APPLE_MUSIC
  SLACK
  NOTION
}

// Notifications
model Notification {
  id          String             @id @default(cuid())
  userId      String
  user        User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  type        NotificationType
  title       String
  message     String
  data        String?            // JSON string of additional data
  
  isRead      Boolean            @default(false)
  isSent      Boolean            @default(false)
  
  scheduledAt DateTime?
  sentAt      DateTime?
  readAt      DateTime?
  
  createdAt   DateTime           @default(now())
  
  @@map("notifications")
  @@index([userId, isRead])
  @@index([scheduledAt])
}

enum NotificationType {
  ENERGY_REMINDER
  FLOW_SUGGESTION
  HABIT_REMINDER
  TASK_REMINDER
  BREAK_REMINDER
  COACHING_INSIGHT
  WEEKLY_SUMMARY
  ACHIEVEMENT
  SYSTEM
}
