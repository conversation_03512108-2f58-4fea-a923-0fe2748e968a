'use client';

import React, { useState, useEffect } from 'react';
import { 
  CheckCircle, 
  Star, 
  RotateCcw, 
  Plus, 
  Clock, 
  Target,
  TrendingUp,
  Award,
  Zap,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Meh
} from 'lucide-react';
import { useFlowSessionManager } from '@/hooks/useFlowSession';
import { FlowIntensity } from '@/types/api';
import { cn, formatDuration } from '@/lib/utils';

interface SessionCompletionProps {
  className?: string;
  onRestart?: () => void;
  onExtend?: (minutes: number) => void;
  onNewSession?: () => void;
}

export function SessionCompletion({ 
  className, 
  onRestart, 
  onExtend, 
  onNewSession 
}: SessionCompletionProps) {
  const { currentSession, progress, sessionState } = useFlowSessionManager();
  const [showFeedback, setShowFeedback] = useState(false);
  const [productivity, setProductivity] = useState(5);
  const [satisfaction, setSatisfaction] = useState(5);
  const [intensity, setIntensity] = useState<FlowIntensity>('moderate');
  const [notes, setNotes] = useState('');
  const [showCelebration, setShowCelebration] = useState(false);

  useEffect(() => {
    if (sessionState === 'completed') {
      setShowCelebration(true);
      const timer = setTimeout(() => setShowCelebration(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [sessionState]);

  if (!currentSession || sessionState !== 'completed') {
    return null;
  }

  const handleSubmitFeedback = () => {
    // Here you would typically update the session with feedback
    console.log('Session feedback:', {
      productivity,
      satisfaction,
      intensity,
      notes
    });
    setShowFeedback(false);
  };

  const getProductivityIcon = (rating: number) => {
    if (rating >= 8) return <ThumbsUp className="w-5 h-5 text-green-500" />;
    if (rating >= 5) return <Meh className="w-5 h-5 text-yellow-500" />;
    return <ThumbsDown className="w-5 h-5 text-red-500" />;
  };

  const getCompletionMessage = () => {
    const completionRate = progress.percentage;
    if (completionRate >= 100) return "Perfect! You completed your full session!";
    if (completionRate >= 75) return "Great work! You made excellent progress!";
    if (completionRate >= 50) return "Good job! You're building momentum!";
    return "Every step counts! Keep building your focus habit!";
  };

  const getAchievements = () => {
    const achievements = [];
    if (progress.percentage >= 100) achievements.push("Session Completed");
    if (progress.elapsedTime >= 3600) achievements.push("Hour of Focus"); // 1 hour
    if (progress.milestones.filter(m => m.reached).length >= 3) achievements.push("Milestone Master");
    return achievements;
  };

  return (
    <div className={cn('bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6', className)}>
      {/* Celebration Animation */}
      {showCelebration && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/90 dark:bg-gray-800/90 rounded-lg z-10">
          <div className="text-center animate-bounce">
            <div className="w-20 h-20 mx-auto mb-4 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
              <CheckCircle className="w-10 h-10 text-green-500" />
            </div>
            <h3 className="text-xl font-bold text-green-600 dark:text-green-400">
              Session Complete!
            </h3>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="text-center mb-6">
        <div className="w-16 h-16 mx-auto mb-4 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
          <CheckCircle className="w-8 h-8 text-green-500" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Session Complete!
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {getCompletionMessage()}
        </p>
      </div>

      {/* Session Summary */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <Clock className="w-6 h-6 mx-auto mb-2 text-blue-600 dark:text-blue-400" />
          <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
            {formatDuration(progress.elapsedTime * 1000)}
          </div>
          <div className="text-xs text-blue-600/70 dark:text-blue-400/70">Duration</div>
        </div>

        <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <Target className="w-6 h-6 mx-auto mb-2 text-green-600 dark:text-green-400" />
          <div className="text-lg font-bold text-green-600 dark:text-green-400">
            {Math.round(progress.percentage)}%
          </div>
          <div className="text-xs text-green-600/70 dark:text-green-400/70">Complete</div>
        </div>

        <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <Zap className="w-6 h-6 mx-auto mb-2 text-purple-600 dark:text-purple-400" />
          <div className="text-lg font-bold text-purple-600 dark:text-purple-400 capitalize">
            {currentSession.intensity}
          </div>
          <div className="text-xs text-purple-600/70 dark:text-purple-400/70">Intensity</div>
        </div>

        <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
          <Award className="w-6 h-6 mx-auto mb-2 text-orange-600 dark:text-orange-400" />
          <div className="text-lg font-bold text-orange-600 dark:text-orange-400">
            {progress.milestones.filter(m => m.reached).length}
          </div>
          <div className="text-xs text-orange-600/70 dark:text-orange-400/70">Milestones</div>
        </div>
      </div>

      {/* Achievements */}
      {getAchievements().length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Achievements Unlocked
          </h3>
          <div className="flex flex-wrap gap-2">
            {getAchievements().map((achievement, index) => (
              <div
                key={index}
                className="flex items-center space-x-2 px-3 py-2 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 rounded-full text-sm font-medium"
              >
                <Award className="w-4 h-4" />
                <span>{achievement}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-6">
        <button
          onClick={onRestart}
          className="flex items-center justify-center space-x-2 px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
        >
          <RotateCcw className="w-4 h-4" />
          <span>Restart Session</span>
        </button>

        <button
          onClick={() => onExtend?.(15)}
          className="flex items-center justify-center space-x-2 px-4 py-3 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Extend 15min</span>
        </button>

        <button
          onClick={onNewSession}
          className="flex items-center justify-center space-x-2 px-4 py-3 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors"
        >
          <Target className="w-4 h-4" />
          <span>New Session</span>
        </button>
      </div>

      {/* Feedback Section */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <button
          onClick={() => setShowFeedback(!showFeedback)}
          className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
        >
          <MessageSquare className="w-4 h-4" />
          <span>Rate this session</span>
        </button>

        {showFeedback && (
          <div className="mt-4 space-y-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            {/* Productivity Rating */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                How productive was this session?
              </label>
              <div className="flex items-center space-x-2">
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (
                  <button
                    key={rating}
                    onClick={() => setProductivity(rating)}
                    className={cn(
                      'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors',
                      productivity >= rating
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-500'
                    )}
                  >
                    {rating}
                  </button>
                ))}
                <div className="ml-2">
                  {getProductivityIcon(productivity)}
                </div>
              </div>
            </div>

            {/* Satisfaction Rating */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                How satisfied are you with this session?
              </label>
              <div className="flex items-center space-x-1">
                {[1, 2, 3, 4, 5].map((rating) => (
                  <button
                    key={rating}
                    onClick={() => setSatisfaction(rating)}
                    className={cn(
                      'p-1 transition-colors',
                      satisfaction >= rating ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'
                    )}
                  >
                    <Star className="w-6 h-6 fill-current" />
                  </button>
                ))}
              </div>
            </div>

            {/* Intensity */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                How intense was your focus?
              </label>
              <div className="flex space-x-2">
                {(['light', 'moderate', 'deep', 'peak'] as FlowIntensity[]).map((level) => (
                  <button
                    key={level}
                    onClick={() => setIntensity(level)}
                    className={cn(
                      'px-3 py-2 rounded-lg text-sm font-medium transition-colors capitalize',
                      intensity === level
                        ? 'bg-purple-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'
                    )}
                  >
                    {level}
                  </button>
                ))}
              </div>
            </div>

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Notes (optional)
              </label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="How did this session go? Any insights or observations?"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                rows={3}
              />
            </div>

            {/* Submit Button */}
            <button
              onClick={handleSubmitFeedback}
              className="w-full px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors font-medium"
            >
              Submit Feedback
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
