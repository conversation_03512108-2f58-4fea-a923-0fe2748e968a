'use client';

import React from 'react';
import { LucideIcon, TrendingUp, TrendingDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface DashboardCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: LucideIcon;
  color?: string;
  bgColor?: string;
  trend?: 'up' | 'down';
  trendValue?: string;
  onClick?: () => void;
  className?: string;
}

export function DashboardCard({
  title,
  value,
  subtitle,
  icon: Icon,
  color = 'text-gray-600',
  bgColor = 'bg-gray-100 dark:bg-gray-800',
  trend,
  trendValue,
  onClick,
  className,
}: DashboardCardProps) {
  return (
    <div
      className={cn(
        'card cursor-pointer transition-all duration-200 hover:shadow-md',
        onClick && 'hover:scale-105',
        className
      )}
      onClick={onClick}
    >
      <div className="card-content p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
              {title}
            </p>
            <div className="flex items-baseline space-x-2">
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {typeof value === 'number' ? value.toLocaleString() : value}
              </p>
              {trend && (
                <div className={cn(
                  'flex items-center text-xs font-medium',
                  trend === 'up' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                )}>
                  {trend === 'up' ? (
                    <TrendingUp className="w-3 h-3 mr-1" />
                  ) : (
                    <TrendingDown className="w-3 h-3 mr-1" />
                  )}
                  {trendValue}
                </div>
              )}
            </div>
            {subtitle && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {subtitle}
              </p>
            )}
          </div>
          <div className={cn('p-3 rounded-lg', bgColor)}>
            <Icon className={cn('w-6 h-6', color)} />
          </div>
        </div>
      </div>
    </div>
  );
}
