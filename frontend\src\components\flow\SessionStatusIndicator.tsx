'use client';

import React from 'react';
import { 
  Play, 
  Pause, 
  CheckCircle, 
  Clock, 
  Coffee,
  Target,
  Zap,
  AlertCircle
} from 'lucide-react';
import { useFlowSessionManager } from '@/hooks/useFlowSession';
import { useBreakTimer } from '@/hooks/useBreakTimer';
import { SessionState } from '@/types/flow-session';
import { cn, formatDuration } from '@/lib/utils';

interface SessionStatusIndicatorProps {
  className?: string;
  showDetails?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function SessionStatusIndicator({ 
  className, 
  showDetails = true, 
  size = 'md' 
}: SessionStatusIndicatorProps) {
  const {
    sessionState,
    timerState,
    progress,
    currentSession
  } = useFlowSessionManager();
  
  const { isActive: isBreakActive, timeRemaining: breakTimeRemaining } = useBreakTimer();

  const getStatusConfig = () => {
    if (isBreakActive) {
      return {
        icon: Coffee,
        label: 'On Break',
        description: `${Math.floor(breakTimeRemaining / 60)}:${(breakTimeRemaining % 60).toString().padStart(2, '0')} remaining`,
        color: 'text-orange-600 dark:text-orange-400',
        bgColor: 'bg-orange-100 dark:bg-orange-900/20',
        borderColor: 'border-orange-200 dark:border-orange-800',
        pulseColor: 'bg-orange-500',
        animate: true
      };
    }

    switch (sessionState) {
      case 'active':
        return {
          icon: timerState === 'running' ? Play : Pause,
          label: timerState === 'running' ? 'Active Session' : 'Session Paused',
          description: currentSession ? `${currentSession.sessionType.name} • ${formatDuration(progress.elapsedTime * 1000)}` : '',
          color: timerState === 'running' ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400',
          bgColor: timerState === 'running' ? 'bg-green-100 dark:bg-green-900/20' : 'bg-yellow-100 dark:bg-yellow-900/20',
          borderColor: timerState === 'running' ? 'border-green-200 dark:border-green-800' : 'border-yellow-200 dark:border-yellow-800',
          pulseColor: timerState === 'running' ? 'bg-green-500' : 'bg-yellow-500',
          animate: timerState === 'running'
        };
      
      case 'paused':
        return {
          icon: Pause,
          label: 'Session Paused',
          description: currentSession ? `${currentSession.sessionType.name} • Paused at ${formatDuration(progress.elapsedTime * 1000)}` : '',
          color: 'text-yellow-600 dark:text-yellow-400',
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
          borderColor: 'border-yellow-200 dark:border-yellow-800',
          pulseColor: 'bg-yellow-500',
          animate: false
        };
      
      case 'completed':
        return {
          icon: CheckCircle,
          label: 'Session Completed',
          description: currentSession ? `${currentSession.sessionType.name} • ${formatDuration(progress.elapsedTime * 1000)}` : '',
          color: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-100 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800',
          pulseColor: 'bg-blue-500',
          animate: false
        };
      
      case 'break':
        return {
          icon: Coffee,
          label: 'Break Time',
          description: 'Take a moment to rest and recharge',
          color: 'text-orange-600 dark:text-orange-400',
          bgColor: 'bg-orange-100 dark:bg-orange-900/20',
          borderColor: 'border-orange-200 dark:border-orange-800',
          pulseColor: 'bg-orange-500',
          animate: true
        };
      
      default:
        return {
          icon: Clock,
          label: 'Ready to Focus',
          description: 'Start a new flow session',
          color: 'text-gray-600 dark:text-gray-400',
          bgColor: 'bg-gray-100 dark:bg-gray-900/20',
          borderColor: 'border-gray-200 dark:border-gray-800',
          pulseColor: 'bg-gray-500',
          animate: false
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  const sizeClasses = {
    sm: {
      container: 'p-3',
      icon: 'w-4 h-4',
      pulse: 'w-2 h-2',
      title: 'text-sm',
      description: 'text-xs'
    },
    md: {
      container: 'p-4',
      icon: 'w-5 h-5',
      pulse: 'w-3 h-3',
      title: 'text-base',
      description: 'text-sm'
    },
    lg: {
      container: 'p-6',
      icon: 'w-6 h-6',
      pulse: 'w-4 h-4',
      title: 'text-lg',
      description: 'text-base'
    }
  };

  const currentSize = sizeClasses[size];

  return (
    <div className={cn(
      'rounded-lg border transition-all duration-300',
      config.bgColor,
      config.borderColor,
      currentSize.container,
      className
    )}>
      <div className="flex items-center space-x-3">
        {/* Status Indicator */}
        <div className="relative flex items-center justify-center">
          <Icon className={cn(currentSize.icon, config.color)} />
          {config.animate && (
            <div className={cn(
              'absolute rounded-full animate-ping',
              config.pulseColor,
              currentSize.pulse
            )} />
          )}
        </div>

        {/* Status Content */}
        <div className="flex-1 min-w-0">
          <div className={cn('font-semibold', config.color, currentSize.title)}>
            {config.label}
          </div>
          
          {showDetails && config.description && (
            <div className={cn('text-gray-600 dark:text-gray-400 truncate', currentSize.description)}>
              {config.description}
            </div>
          )}
        </div>

        {/* Progress Indicator */}
        {sessionState === 'active' && progress.percentage > 0 && (
          <div className="flex items-center space-x-2">
            <div className="w-12 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <div
                className={cn('h-full transition-all duration-300', {
                  'bg-green-500': progress.percentage < 75,
                  'bg-yellow-500': progress.percentage >= 75 && progress.percentage < 90,
                  'bg-red-500': progress.percentage >= 90
                })}
                style={{ width: `${progress.percentage}%` }}
              />
            </div>
            <span className={cn('font-mono text-xs', config.color)}>
              {Math.round(progress.percentage)}%
            </span>
          </div>
        )}
      </div>

      {/* Session Intensity Indicator */}
      {currentSession && sessionState === 'active' && size !== 'sm' && (
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Zap className="w-4 h-4 text-gray-400" />
              <span className="text-xs text-gray-600 dark:text-gray-400">Intensity</span>
            </div>
            <div className={cn('px-2 py-1 rounded text-xs font-medium', {
              'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300': currentSession.intensity === 'light',
              'bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300': currentSession.intensity === 'moderate',
              'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300': currentSession.intensity === 'deep',
              'bg-indigo-100 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300': currentSession.intensity === 'peak'
            })}>
              {currentSession.intensity}
            </div>
          </div>
        </div>
      )}

      {/* Milestones */}
      {sessionState === 'active' && progress.milestones.length > 0 && size === 'lg' && (
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-1">
            {progress.milestones.map((milestone) => (
              <div
                key={milestone.id}
                className={cn('w-2 h-2 rounded-full transition-colors', {
                  'bg-green-500': milestone.reached,
                  'bg-gray-300 dark:bg-gray-600': !milestone.reached
                })}
                title={milestone.name}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Compact version for headers/navbars
export function CompactSessionStatus({ className }: { className?: string }) {
  const { sessionState, currentSession } = useFlowSessionManager();
  const { isActive: isBreakActive } = useBreakTimer();

  if (sessionState === 'idle' && !isBreakActive) {
    return null;
  }

  const getStatusDot = () => {
    if (isBreakActive) {
      return 'bg-orange-500 animate-pulse';
    }
    
    switch (sessionState) {
      case 'active':
        return 'bg-green-500 animate-pulse';
      case 'paused':
        return 'bg-yellow-500';
      case 'completed':
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = () => {
    if (isBreakActive) return 'Break';
    if (sessionState === 'active') return currentSession?.sessionType.name || 'Active';
    if (sessionState === 'paused') return 'Paused';
    if (sessionState === 'completed') return 'Done';
    return 'Ready';
  };

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <div className={cn('w-2 h-2 rounded-full', getStatusDot())} />
      <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
        {getStatusText()}
      </span>
    </div>
  );
}
