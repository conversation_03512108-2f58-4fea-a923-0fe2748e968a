import { useCallback, useEffect, useState } from 'react';
import { useSessionPreferences } from './useFlowSession';

export type NotificationType = 
  | 'session_started'
  | 'session_completed'
  | 'break_reminder'
  | 'break_started'
  | 'break_ended'
  | 'milestone_reached'
  | 'session_extended';

interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  tag?: string;
  requireInteraction?: boolean;
  silent?: boolean;
  actions?: NotificationAction[];
}

export function useNotifications() {
  const [permission, setPermission] = useState<NotificationPermission>('default');
  const [isSupported, setIsSupported] = useState(false);
  const { preferences } = useSessionPreferences();

  useEffect(() => {
    // Check if notifications are supported
    setIsSupported('Notification' in window);
    
    if ('Notification' in window) {
      setPermission(Notification.permission);
    }
  }, []);

  const requestPermission = useCallback(async (): Promise<NotificationPermission> => {
    if (!isSupported) {
      return 'denied';
    }

    if (permission === 'granted') {
      return 'granted';
    }

    try {
      const result = await Notification.requestPermission();
      setPermission(result);
      return result;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return 'denied';
    }
  }, [isSupported, permission]);

  const showNotification = useCallback(async (
    type: NotificationType,
    options: NotificationOptions
  ): Promise<Notification | null> => {
    // Check if notifications are enabled in preferences
    if (!preferences.timerConfig.notificationsEnabled) {
      return null;
    }

    // Check if notifications are supported and permitted
    if (!isSupported || permission !== 'granted') {
      return null;
    }

    try {
      const notification = new Notification(options.title, {
        body: options.body,
        icon: options.icon || '/icons/rhythmai-icon-192.png',
        tag: options.tag || type,
        requireInteraction: options.requireInteraction || false,
        silent: options.silent || false,
        badge: '/icons/rhythmai-badge-72.png',
        timestamp: Date.now(),
        ...options
      });

      // Auto-close notification after 5 seconds unless requireInteraction is true
      if (!options.requireInteraction) {
        setTimeout(() => {
          notification.close();
        }, 5000);
      }

      return notification;
    } catch (error) {
      console.error('Error showing notification:', error);
      return null;
    }
  }, [isSupported, permission, preferences.timerConfig.notificationsEnabled]);

  // Predefined notification templates
  const showSessionStarted = useCallback((sessionType: string) => {
    return showNotification('session_started', {
      title: 'Flow Session Started',
      body: `Your ${sessionType} session has begun. Time to focus!`,
      tag: 'session_started',
      silent: false
    });
  }, [showNotification]);

  const showSessionCompleted = useCallback((sessionType: string, duration: number) => {
    const minutes = Math.round(duration / 60);
    return showNotification('session_completed', {
      title: 'Session Completed! 🎉',
      body: `Great job! You completed a ${minutes}-minute ${sessionType} session.`,
      tag: 'session_completed',
      requireInteraction: true,
      actions: [
        { action: 'start_break', title: 'Take a Break' },
        { action: 'start_new', title: 'Start New Session' }
      ]
    });
  }, [showNotification]);

  const showBreakReminder = useCallback((sessionType: string, elapsedMinutes: number) => {
    return showNotification('break_reminder', {
      title: 'Break Reminder',
      body: `You've been working for ${elapsedMinutes} minutes. Consider taking a short break!`,
      tag: 'break_reminder',
      actions: [
        { action: 'start_break', title: 'Start Break' },
        { action: 'dismiss', title: 'Continue Working' }
      ]
    });
  }, [showNotification]);

  const showBreakStarted = useCallback ((breakType: string, duration: number) => {
    return showNotification('break_started', {
      title: 'Break Time! ☕',
      body: `Your ${duration}-minute ${breakType} break has started. Time to recharge!`,
      tag: 'break_started',
      silent: false
    });
  }, [showNotification]);

  const showBreakEnded = useCallback(() => {
    return showNotification('break_ended', {
      title: 'Break Complete',
      body: 'Your break is over. Ready to get back to work?',
      tag: 'break_ended',
      requireInteraction: true,
      actions: [
        { action: 'resume_session', title: 'Resume Session' },
        { action: 'start_new', title: 'Start New Session' }
      ]
    });
  }, [showNotification]);

  const showMilestoneReached = useCallback((milestone: string, percentage: number) => {
    return showNotification('milestone_reached', {
      title: `Milestone Reached! 🎯`,
      body: `${milestone} - You're ${percentage}% through your session!`,
      tag: 'milestone_reached',
      silent: true
    });
  }, [showNotification]);

  const showSessionExtended = useCallback((additionalMinutes: number) => {
    return showNotification('session_extended', {
      title: 'Session Extended',
      body: `Added ${additionalMinutes} more minutes to your session. Keep going!`,
      tag: 'session_extended',
      silent: true
    });
  }, [showNotification]);

  // Clear all notifications with a specific tag
  const clearNotifications = useCallback((tag?: string) => {
    if (!isSupported) return;

    // Note: There's no direct way to clear notifications by tag in the browser API
    // This is a limitation of the Notification API
    console.log(`Clearing notifications${tag ? ` with tag: ${tag}` : ''}`);
  }, [isSupported]);

  return {
    // State
    permission,
    isSupported,
    isEnabled: preferences.timerConfig.notificationsEnabled,
    
    // Actions
    requestPermission,
    showNotification,
    clearNotifications,
    
    // Predefined notifications
    showSessionStarted,
    showSessionCompleted,
    showBreakReminder,
    showBreakStarted,
    showBreakEnded,
    showMilestoneReached,
    showSessionExtended
  };
}

// Hook for managing notification preferences
export function useNotificationPreferences() {
  const { preferences, updatePreferences } = useSessionPreferences();
  const { permission, requestPermission, isSupported } = useNotifications();

  const enableNotifications = useCallback(async () => {
    if (!isSupported) {
      throw new Error('Notifications are not supported in this browser');
    }

    const result = await requestPermission();
    
    if (result === 'granted') {
      updatePreferences({
        timerConfig: {
          ...preferences.timerConfig,
          notificationsEnabled: true
        }
      });
      return true;
    } else {
      throw new Error('Notification permission was denied');
    }
  }, [isSupported, requestPermission, updatePreferences, preferences.timerConfig]);

  const disableNotifications = useCallback(() => {
    updatePreferences({
      timerConfig: {
        ...preferences.timerConfig,
        notificationsEnabled: false
      }
    });
  }, [updatePreferences, preferences.timerConfig]);

  const toggleNotifications = useCallback(async () => {
    if (preferences.timerConfig.notificationsEnabled) {
      disableNotifications();
      return false;
    } else {
      try {
        await enableNotifications();
        return true;
      } catch (error) {
        console.error('Failed to enable notifications:', error);
        return false;
      }
    }
  }, [preferences.timerConfig.notificationsEnabled, enableNotifications, disableNotifications]);

  return {
    isEnabled: preferences.timerConfig.notificationsEnabled,
    permission,
    isSupported,
    canEnable: isSupported && permission !== 'denied',
    enableNotifications,
    disableNotifications,
    toggleNotifications
  };
}

// Utility function to check if notifications should be shown based on user preferences and browser state
export function shouldShowNotification(
  type: NotificationType,
  preferences: any,
  permission: NotificationPermission
): boolean {
  // Check if notifications are enabled in preferences
  if (!preferences.timerConfig.notificationsEnabled) {
    return false;
  }

  // Check if browser permission is granted
  if (permission !== 'granted') {
    return false;
  }

  // Check if the document is hidden (user is on another tab)
  if (document.hidden) {
    return true; // Show notifications when user is away
  }

  // For certain types, always show regardless of document visibility
  const alwaysShowTypes: NotificationType[] = ['session_completed', 'break_ended'];
  if (alwaysShowTypes.includes(type)) {
    return true;
  }

  return false;
}
