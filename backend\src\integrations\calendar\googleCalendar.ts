import { google } from 'googleapis';
import { prisma } from '../../config/database';
import { logger } from '../../utils/logger';
import { config } from '../../config/database';

// Google Calendar interfaces
export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  location?: string;
  attendees?: string[];
  isAllDay: boolean;
  status: 'confirmed' | 'tentative' | 'cancelled';
  source: 'google' | 'outlook' | 'apple';
}

export interface CalendarIntegration {
  userId: string;
  provider: 'google';
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
}

export class GoogleCalendarService {
  private oauth2Client: any;

  constructor() {
    this.oauth2Client = new google.auth.OAuth2(
      config.integrations.google.clientId,
      config.integrations.google.clientSecret,
      config.integrations.google.redirectUri
    );
  }

  // Get authorization URL for OAuth flow
  getAuthUrl(): string {
    const scopes = [
      'https://www.googleapis.com/auth/calendar.readonly',
      'https://www.googleapis.com/auth/calendar.events',
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent',
    });
  }

  // Exchange authorization code for tokens
  async exchangeCodeForTokens(code: string): Promise<any> {
    try {
      const { tokens } = await this.oauth2Client.getToken(code);
      return tokens;
    } catch (error) {
      logger.error('Error exchanging code for tokens:', error);
      throw new Error('Failed to exchange authorization code');
    }
  }

  // Store integration credentials
  async storeIntegration(userId: string, tokens: any): Promise<void> {
    try {
      const expiresAt = new Date(Date.now() + tokens.expiry_date);

      await prisma.integration.upsert({
        where: {
          userId_provider: {
            userId,
            provider: 'GOOGLE_CALENDAR',
          },
        },
        update: {
          accessToken: this.encryptToken(tokens.access_token),
          refreshToken: this.encryptToken(tokens.refresh_token),
          expiresAt,
          isActive: true,
          lastSyncAt: new Date(),
        },
        create: {
          userId,
          provider: 'GOOGLE_CALENDAR',
          accessToken: this.encryptToken(tokens.access_token),
          refreshToken: this.encryptToken(tokens.refresh_token),
          expiresAt,
          isActive: true,
          lastSyncAt: new Date(),
        },
      });

      logger.info(`Google Calendar integration stored for user ${userId}`);
    } catch (error) {
      logger.error('Error storing Google Calendar integration:', error);
      throw error;
    }
  }

  // Get user's calendar integration
  async getIntegration(userId: string): Promise<CalendarIntegration | null> {
    try {
      const integration = await prisma.integration.findUnique({
        where: {
          userId_provider: {
            userId,
            provider: 'GOOGLE_CALENDAR',
          },
        },
      });

      if (!integration || !integration.isActive) {
        return null;
      }

      return {
        userId: integration.userId,
        provider: 'google',
        accessToken: this.decryptToken(integration.accessToken!),
        refreshToken: this.decryptToken(integration.refreshToken!),
        expiresAt: integration.expiresAt!,
      };
    } catch (error) {
      logger.error('Error getting Google Calendar integration:', error);
      return null;
    }
  }

  // Refresh access token if needed
  async refreshTokenIfNeeded(integration: CalendarIntegration): Promise<string> {
    try {
      if (new Date() < integration.expiresAt) {
        return integration.accessToken;
      }

      this.oauth2Client.setCredentials({
        refresh_token: integration.refreshToken,
      });

      const { credentials } = await this.oauth2Client.refreshAccessToken();
      
      // Update stored tokens
      await this.storeIntegration(integration.userId, credentials);

      return credentials.access_token;
    } catch (error) {
      logger.error('Error refreshing Google Calendar token:', error);
      throw new Error('Failed to refresh access token');
    }
  }

  // Get calendar events for a date range
  async getEvents(userId: string, startDate: Date, endDate: Date): Promise<CalendarEvent[]> {
    try {
      const integration = await this.getIntegration(userId);
      if (!integration) {
        throw new Error('Google Calendar integration not found');
      }

      const accessToken = await this.refreshTokenIfNeeded(integration);
      this.oauth2Client.setCredentials({ access_token: accessToken });

      const calendar = google.calendar({ version: 'v3', auth: this.oauth2Client });

      const response = await calendar.events.list({
        calendarId: 'primary',
        timeMin: startDate.toISOString(),
        timeMax: endDate.toISOString(),
        singleEvents: true,
        orderBy: 'startTime',
        maxResults: 250,
      });

      const events = response.data.items || [];

      return events.map(event => this.transformGoogleEvent(event));
    } catch (error) {
      logger.error('Error getting Google Calendar events:', error);
      throw error;
    }
  }

  // Create a calendar event
  async createEvent(userId: string, eventData: Partial<CalendarEvent>): Promise<CalendarEvent> {
    try {
      const integration = await this.getIntegration(userId);
      if (!integration) {
        throw new Error('Google Calendar integration not found');
      }

      const accessToken = await this.refreshTokenIfNeeded(integration);
      this.oauth2Client.setCredentials({ access_token: accessToken });

      const calendar = google.calendar({ version: 'v3', auth: this.oauth2Client });

      const event = {
        summary: eventData.title,
        description: eventData.description,
        location: eventData.location,
        start: {
          dateTime: eventData.startTime?.toISOString(),
          timeZone: 'UTC',
        },
        end: {
          dateTime: eventData.endTime?.toISOString(),
          timeZone: 'UTC',
        },
        attendees: eventData.attendees?.map(email => ({ email })),
      };

      const response = await calendar.events.insert({
        calendarId: 'primary',
        requestBody: event,
      });

      return this.transformGoogleEvent(response.data);
    } catch (error) {
      logger.error('Error creating Google Calendar event:', error);
      throw error;
    }
  }

  // Update a calendar event
  async updateEvent(userId: string, eventId: string, eventData: Partial<CalendarEvent>): Promise<CalendarEvent> {
    try {
      const integration = await this.getIntegration(userId);
      if (!integration) {
        throw new Error('Google Calendar integration not found');
      }

      const accessToken = await this.refreshTokenIfNeeded(integration);
      this.oauth2Client.setCredentials({ access_token: accessToken });

      const calendar = google.calendar({ version: 'v3', auth: this.oauth2Client });

      const event = {
        summary: eventData.title,
        description: eventData.description,
        location: eventData.location,
        start: {
          dateTime: eventData.startTime?.toISOString(),
          timeZone: 'UTC',
        },
        end: {
          dateTime: eventData.endTime?.toISOString(),
          timeZone: 'UTC',
        },
      };

      const response = await calendar.events.update({
        calendarId: 'primary',
        eventId,
        requestBody: event,
      });

      return this.transformGoogleEvent(response.data);
    } catch (error) {
      logger.error('Error updating Google Calendar event:', error);
      throw error;
    }
  }

  // Delete a calendar event
  async deleteEvent(userId: string, eventId: string): Promise<void> {
    try {
      const integration = await this.getIntegration(userId);
      if (!integration) {
        throw new Error('Google Calendar integration not found');
      }

      const accessToken = await this.refreshTokenIfNeeded(integration);
      this.oauth2Client.setCredentials({ access_token: accessToken });

      const calendar = google.calendar({ version: 'v3', auth: this.oauth2Client });

      await calendar.events.delete({
        calendarId: 'primary',
        eventId,
      });

      logger.info(`Google Calendar event deleted: ${eventId}`);
    } catch (error) {
      logger.error('Error deleting Google Calendar event:', error);
      throw error;
    }
  }

  // Get free/busy information
  async getFreeBusy(userId: string, startDate: Date, endDate: Date): Promise<any> {
    try {
      const integration = await this.getIntegration(userId);
      if (!integration) {
        throw new Error('Google Calendar integration not found');
      }

      const accessToken = await this.refreshTokenIfNeeded(integration);
      this.oauth2Client.setCredentials({ access_token: accessToken });

      const calendar = google.calendar({ version: 'v3', auth: this.oauth2Client });

      const response = await calendar.freebusy.query({
        requestBody: {
          timeMin: startDate.toISOString(),
          timeMax: endDate.toISOString(),
          items: [{ id: 'primary' }],
        },
      });

      return response.data.calendars?.primary?.busy || [];
    } catch (error) {
      logger.error('Error getting Google Calendar free/busy:', error);
      throw error;
    }
  }

  // Sync calendar events (for background sync)
  async syncEvents(userId: string): Promise<void> {
    try {
      const integration = await this.getIntegration(userId);
      if (!integration) {
        return;
      }

      // Sync events for the next 30 days
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + 30);

      const events = await this.getEvents(userId, startDate, endDate);

      // Store events in database for offline access
      for (const event of events) {
        await this.storeEventInDatabase(userId, event);
      }

      // Update last sync time
      await prisma.integration.update({
        where: {
          userId_provider: {
            userId,
            provider: 'GOOGLE_CALENDAR',
          },
        },
        data: {
          lastSyncAt: new Date(),
        },
      });

      logger.info(`Synced ${events.length} Google Calendar events for user ${userId}`);
    } catch (error) {
      logger.error('Error syncing Google Calendar events:', error);
      throw error;
    }
  }

  // Transform Google Calendar event to our format
  private transformGoogleEvent(googleEvent: any): CalendarEvent {
    const start = googleEvent.start?.dateTime || googleEvent.start?.date;
    const end = googleEvent.end?.dateTime || googleEvent.end?.date;

    return {
      id: googleEvent.id,
      title: googleEvent.summary || 'Untitled Event',
      description: googleEvent.description,
      startTime: new Date(start),
      endTime: new Date(end),
      location: googleEvent.location,
      attendees: googleEvent.attendees?.map((attendee: any) => attendee.email) || [],
      isAllDay: !googleEvent.start?.dateTime,
      status: googleEvent.status === 'confirmed' ? 'confirmed' : 
              googleEvent.status === 'tentative' ? 'tentative' : 'cancelled',
      source: 'google',
    };
  }

  // Store event in database for offline access
  private async storeEventInDatabase(userId: string, event: CalendarEvent): Promise<void> {
    try {
      // In a real implementation, you might have a CalendarEvent model
      // For now, we'll store it as JSON in the integration data
      logger.debug(`Storing calendar event ${event.id} for user ${userId}`);
    } catch (error) {
      logger.error('Error storing calendar event in database:', error);
    }
  }

  // Encrypt token (placeholder - use proper encryption in production)
  private encryptToken(token: string): string {
    // In production, use proper encryption like AES-256
    return Buffer.from(token).toString('base64');
  }

  // Decrypt token (placeholder - use proper decryption in production)
  private decryptToken(encryptedToken: string): string {
    // In production, use proper decryption
    return Buffer.from(encryptedToken, 'base64').toString();
  }

  // Disconnect integration
  async disconnectIntegration(userId: string): Promise<void> {
    try {
      await prisma.integration.update({
        where: {
          userId_provider: {
            userId,
            provider: 'GOOGLE_CALENDAR',
          },
        },
        data: {
          isActive: false,
        },
      });

      logger.info(`Google Calendar integration disconnected for user ${userId}`);
    } catch (error) {
      logger.error('Error disconnecting Google Calendar integration:', error);
      throw error;
    }
  }

  // Check if user has active integration
  async hasActiveIntegration(userId: string): Promise<boolean> {
    try {
      const integration = await this.getIntegration(userId);
      return integration !== null;
    } catch (error) {
      logger.error('Error checking Google Calendar integration status:', error);
      return false;
    }
  }
}
