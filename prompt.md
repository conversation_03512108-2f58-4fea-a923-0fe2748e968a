Create a comprehensive RhythmAI productivity application with the following specifications:

**Project Overview:**
- Name: RhythmAI
- Tagline: "Find your productive flow"
- Purpose: A rhythm-aware productivity system that learns individual energy patterns and optimizes scheduling

**Core Features to Implement:**
1. Circadian Rhythm Analysis - Track natural energy patterns for optimal task scheduling
2. Flow State Detection - Monitor work patterns to identify and protect deep work periods
3. Adaptive Workload Management - Adjust daily capacity based on stress levels and life events
4. Habit Formation Assistant - Use behavioral psychology for sustainable productivity routines
5. Wellness Integration - Connect with fitness trackers, sleep data, and calendar events
6. Personalized Productivity Coaching - Provide tailored advice based on individual patterns

**Technical Implementation Requirements:**
- Biometric data integration (heart rate, sleep, activity data)
- Behavioral analytics with machine learning algorithms
- Calendar and scheduling API integrations
- Smart push notification system
- Data visualization for pattern recognition
- Privacy-focused data handling with encryption
- Use free APIs and custom algorithms where possible

**Deliverables Required:**
1. Check GitHub profile (https://github.com/HectorTa1989) and create a comprehensive README.md
2. Verify domain availability for "RhythmAI" and suggest alternative domain names that are:
   - Available at low cost
   - High trustability
   - Good viral potential
3. Create system architecture diagram in Mermaid syntax
4. Create workflow diagram in Mermaid syntax
5. Design complete project structure for the application
6. Generate code for each file in the project structure with:
   - Exact file paths and names
   - Complete implementation using custom algorithms and free APIs
   - Each file delivered as a separate code artifact
   - Individual commit messages for each file for GitHub commits

**Technical Stack Preferences:**
- Focus on modern web technologies
- Prioritize free APIs and services
- Implement custom algorithms for core functionality
- Ensure privacy-first approach to data handling
- Make the system scalable and maintainable

Please deliver all components systematically, starting with the README.md, domain research, architecture diagrams, project structure, and then individual file implementations with commit messages.