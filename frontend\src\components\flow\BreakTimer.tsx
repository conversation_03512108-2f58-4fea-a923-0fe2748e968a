'use client';

import React, { useState } from 'react';
import { 
  Coffee, 
  Play, 
  Pause, 
  Ski<PERSON><PERSON><PERSON><PERSON>, 
  Clock, 
  RefreshCw,
  Check<PERSON><PERSON>cle,
  Lightbulb,
  Heart,
  Eye,
  Dumbbell
} from 'lucide-react';
import { useBreakTimer } from '@/hooks/useBreakTimer';
import { useSessionPreferences } from '@/hooks/useFlowSession';
import { BreakType } from '@/types/flow-session';
import { cn } from '@/lib/utils';

interface BreakTimerProps {
  className?: string;
  onBreakComplete?: () => void;
  autoStart?: boolean;
}

export function BreakTimer({ className, onBreakComplete, autoStart = false }: BreakTimerProps) {
  const [selectedBreakType, setSelectedBreakType] = useState<BreakType>('short');
  const [customDuration, setCustomDuration] = useState(10);
  
  const {
    breakTimer,
    isActive,
    timeRemaining,
    startBreak,
    endBreak,
    skipBreak,
    formattedTimeRemaining,
    progress,
    suggestions
  } = useBreakTimer();

  const { preferences } = useSessionPreferences();

  const breakTypes = [
    {
      type: 'short' as BreakType,
      name: 'Short Break',
      duration: preferences.timerConfig.shortBreakDuration,
      icon: Coffee,
      description: 'Quick 5-minute refresh',
      color: 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800'
    },
    {
      type: 'long' as BreakType,
      name: 'Long Break',
      duration: preferences.timerConfig.longBreakDuration,
      icon: RefreshCw,
      description: 'Extended 15-minute break',
      color: 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800'
    },
    {
      type: 'custom' as BreakType,
      name: 'Custom',
      duration: customDuration,
      icon: Clock,
      description: 'Set your own duration',
      color: 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-800'
    }
  ];

  const handleStartBreak = (type: BreakType) => {
    const duration = type === 'custom' ? customDuration : breakTypes.find(bt => bt.type === type)?.duration;
    startBreak(type, duration);
  };

  const handleEndBreak = () => {
    endBreak();
    onBreakComplete?.();
  };

  if (isActive && breakTimer) {
    return (
      <div className={cn('bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6', className)}>
        {/* Break Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center">
              <Coffee className="w-5 h-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Break Time
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {breakTimer.type === 'short' && 'Short Break'}
                {breakTimer.type === 'long' && 'Long Break'}
                {breakTimer.type === 'custom' && 'Custom Break'}
              </p>
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-2xl font-mono font-bold text-orange-600 dark:text-orange-400">
              {formattedTimeRemaining}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {Math.round(progress)}% complete
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="h-2 bg-orange-500 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Break Suggestions */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Break Suggestions
          </h4>
          <div className="grid grid-cols-1 gap-2">
            {suggestions.slice(0, 3).map((suggestion, index) => (
              <div
                key={index}
                className="flex items-center space-x-3 p-3 bg-orange-50 dark:bg-orange-900/10 rounded-lg"
              >
                <div className="w-6 h-6 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center">
                  {index === 0 && <Eye className="w-3 h-3 text-orange-600 dark:text-orange-400" />}
                  {index === 1 && <Dumbbell className="w-3 h-3 text-orange-600 dark:text-orange-400" />}
                  {index === 2 && <Heart className="w-3 h-3 text-orange-600 dark:text-orange-400" />}
                </div>
                <span className="text-sm text-gray-700 dark:text-gray-300">{suggestion}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Break Controls */}
        <div className="flex items-center justify-center space-x-3">
          <button
            onClick={handleEndBreak}
            className="flex items-center space-x-2 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
          >
            <CheckCircle className="w-4 h-4" />
            <span>End Break</span>
          </button>
          
          <button
            onClick={skipBreak}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            <SkipForward className="w-4 h-4" />
            <span>Skip</span>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6', className)}>
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center">
          <Coffee className="w-5 h-5 text-orange-600 dark:text-orange-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Take a Break
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Choose your break duration
          </p>
        </div>
      </div>

      {/* Break Type Selection */}
      <div className="space-y-3 mb-6">
        {breakTypes.map((breakType) => {
          const Icon = breakType.icon;
          const isSelected = selectedBreakType === breakType.type;
          
          return (
            <button
              key={breakType.type}
              onClick={() => setSelectedBreakType(breakType.type)}
              className={cn(
                'w-full flex items-center space-x-3 p-4 rounded-lg border-2 transition-all duration-200 text-left',
                isSelected
                  ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800'
              )}
            >
              <div className={cn('w-8 h-8 rounded-lg flex items-center justify-center', breakType.color)}>
                <Icon className="w-4 h-4" />
              </div>
              
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-gray-900 dark:text-white">
                    {breakType.name}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {breakType.duration} min
                  </span>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {breakType.description}
                </p>
              </div>
              
              {isSelected && (
                <CheckCircle className="w-5 h-5 text-orange-500" />
              )}
            </button>
          );
        })}
      </div>

      {/* Custom Duration Input */}
      {selectedBreakType === 'custom' && (
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Custom Duration (minutes)
          </label>
          <input
            type="number"
            min="1"
            max="60"
            value={customDuration}
            onChange={(e) => setCustomDuration(parseInt(e.target.value) || 10)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
        </div>
      )}

      {/* Start Break Button */}
      <button
        onClick={() => handleStartBreak(selectedBreakType)}
        className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors font-medium"
      >
        <Play className="w-4 h-4" />
        <span>Start Break</span>
      </button>

      {/* Break Tips */}
      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <div className="flex items-start space-x-2">
          <Lightbulb className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
              Break Tips
            </h4>
            <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
              <li>• Step away from your screen</li>
              <li>• Do some light stretching</li>
              <li>• Take deep breaths</li>
              <li>• Hydrate yourself</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

// Compact Break Timer for sidebar/header
export function CompactBreakTimer({ className }: { className?: string }) {
  const { isActive, formattedTimeRemaining, endBreak, skipBreak } = useBreakTimer();

  if (!isActive) return null;

  return (
    <div className={cn('flex items-center space-x-2 px-3 py-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg', className)}>
      <Coffee className="w-4 h-4 text-orange-600 dark:text-orange-400" />
      <span className="text-sm font-medium text-orange-700 dark:text-orange-300">
        Break: {formattedTimeRemaining}
      </span>
      <div className="flex items-center space-x-1">
        <button
          onClick={endBreak}
          className="p-1 rounded hover:bg-orange-200 dark:hover:bg-orange-800 transition-colors"
          title="End break"
        >
          <CheckCircle className="w-3 h-3 text-orange-600 dark:text-orange-400" />
        </button>
        <button
          onClick={skipBreak}
          className="p-1 rounded hover:bg-orange-200 dark:hover:bg-orange-800 transition-colors"
          title="Skip break"
        >
          <SkipForward className="w-3 h-3 text-orange-600 dark:text-orange-400" />
        </button>
      </div>
    </div>
  );
}
