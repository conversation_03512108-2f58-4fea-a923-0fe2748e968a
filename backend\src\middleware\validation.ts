import { Request, Response, NextFunction } from 'express';
import { body, query, param, validationResult, ValidationChain } from 'express-validator';
import { ValidationError } from './errorHandler';

// Validation result handler
export const validationMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.param,
      message: error.msg,
      value: error.value,
    }));

    throw new ValidationError('Validation failed', errorMessages);
  }
  next();
};

// Common validation rules
export const commonValidations = {
  // Email validation
  email: () => body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Must be a valid email address'),

  // Password validation
  password: () => body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be between 8 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),

  // Name validation
  firstName: () => body('firstName')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),

  lastName: () => body('lastName')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),

  // ID validation
  id: (paramName: string = 'id') => param(paramName)
    .isString()
    .isLength({ min: 1 })
    .withMessage(`${paramName} is required`),

  // Date validation
  date: (fieldName: string, location: 'body' | 'query' = 'query') => {
    const validator = location === 'body' ? body(fieldName) : query(fieldName);
    return validator
      .isISO8601()
      .withMessage(`${fieldName} must be a valid ISO 8601 date`);
  },

  // Date range validation
  dateRange: () => [
    query('startDate')
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    query('endDate')
      .isISO8601()
      .withMessage('End date must be a valid ISO 8601 date')
      .custom((endDate, { req }) => {
        const startDate = req.query.startDate as string;
        if (startDate && new Date(endDate) <= new Date(startDate)) {
          throw new Error('End date must be after start date');
        }
        return true;
      }),
  ],

  // Pagination validation
  pagination: () => [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
  ],

  // Timezone validation
  timezone: () => body('timezone')
    .optional()
    .isString()
    .custom((value) => {
      try {
        Intl.DateTimeFormat(undefined, { timeZone: value });
        return true;
      } catch (error) {
        throw new Error('Invalid timezone');
      }
    }),
};

// Energy data validation
export const energyValidations = {
  recordEnergy: () => [
    body('energyLevel')
      .isInt({ min: 1, max: 5 })
      .withMessage('Energy level must be between 1 and 5'),
    body('mood')
      .isInt({ min: 1, max: 10 })
      .withMessage('Mood must be between 1 and 10'),
    body('stressLevel')
      .isInt({ min: 1, max: 10 })
      .withMessage('Stress level must be between 1 and 10'),
    body('sleepQuality')
      .optional()
      .isInt({ min: 1, max: 10 })
      .withMessage('Sleep quality must be between 1 and 10'),
    body('physicalActivity')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Physical activity must be a non-negative integer'),
    body('caffeine')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Caffeine must be a non-negative integer'),
    body('context')
      .optional()
      .isString()
      .isLength({ max: 500 })
      .withMessage('Context must be less than 500 characters'),
    body('timestamp')
      .optional()
      .isISO8601()
      .withMessage('Timestamp must be a valid ISO 8601 date'),
  ],

  getEnergyData: () => [
    ...commonValidations.dateRange(),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Limit must be between 1 and 1000'),
  ],

  predictEnergy: () => [
    query('timestamp')
      .isISO8601()
      .withMessage('Timestamp must be a valid ISO 8601 date')
      .custom((timestamp) => {
        const targetDate = new Date(timestamp);
        const maxFutureDate = new Date();
        maxFutureDate.setDate(maxFutureDate.getDate() + 7);
        
        if (targetDate > maxFutureDate) {
          throw new Error('Cannot predict more than 7 days in the future');
        }
        return true;
      }),
  ],
};

// Flow session validation
export const flowValidations = {
  startSession: () => [
    body('taskType')
      .isString()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Task type is required and must be less than 100 characters'),
    body('environment')
      .isObject()
      .withMessage('Environment data is required'),
    body('environment.location')
      .isString()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Location is required and must be less than 100 characters'),
    body('environment.noiseLevel')
      .isInt({ min: 1, max: 10 })
      .withMessage('Noise level must be between 1 and 10'),
    body('environment.lighting')
      .isString()
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Lighting is required and must be less than 50 characters'),
    body('environment.temperature')
      .optional()
      .isNumeric()
      .withMessage('Temperature must be a number'),
    body('environment.musicType')
      .optional()
      .isString()
      .trim()
      .isLength({ max: 50 })
      .withMessage('Music type must be less than 50 characters'),
    body('environment.distractions')
      .optional()
      .isArray()
      .withMessage('Distractions must be an array'),
  ],

  updateSession: () => [
    param('sessionId')
      .isString()
      .isLength({ min: 1 })
      .withMessage('Session ID is required'),
    body('intensity')
      .optional()
      .isIn(['light', 'moderate', 'deep', 'peak'])
      .withMessage('Invalid intensity level'),
    body('interruptions')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Interruptions must be a non-negative integer'),
    body('productivity')
      .optional()
      .isInt({ min: 1, max: 10 })
      .withMessage('Productivity must be between 1 and 10'),
    body('satisfaction')
      .optional()
      .isInt({ min: 1, max: 10 })
      .withMessage('Satisfaction must be between 1 and 10'),
    body('triggers')
      .optional()
      .isArray()
      .withMessage('Triggers must be an array'),
    body('notes')
      .optional()
      .isString()
      .isLength({ max: 1000 })
      .withMessage('Notes must be less than 1000 characters'),
  ],

  endSession: () => [
    param('sessionId')
      .isString()
      .isLength({ min: 1 })
      .withMessage('Session ID is required'),
    body('productivity')
      .isInt({ min: 1, max: 10 })
      .withMessage('Productivity rating is required (1-10)'),
    body('satisfaction')
      .isInt({ min: 1, max: 10 })
      .withMessage('Satisfaction rating is required (1-10)'),
    body('notes')
      .optional()
      .isString()
      .isLength({ max: 1000 })
      .withMessage('Notes must be less than 1000 characters'),
  ],

  getSessions: () => [
    ...commonValidations.dateRange(),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
  ],
};

// Habit validation
export const habitValidations = {
  createHabit: () => [
    body('name')
      .isString()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Habit name is required and must be less than 100 characters'),
    body('description')
      .optional()
      .isString()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description must be less than 500 characters'),
    body('category')
      .isIn(['PRODUCTIVITY', 'HEALTH', 'LEARNING', 'MINDFULNESS', 'SOCIAL', 'CREATIVE', 'FINANCIAL'])
      .withMessage('Invalid habit category'),
    body('frequency')
      .isIn(['DAILY', 'WEEKLY', 'MONTHLY', 'CUSTOM'])
      .withMessage('Invalid habit frequency'),
    body('targetValue')
      .isInt({ min: 1 })
      .withMessage('Target value must be a positive integer'),
    body('unit')
      .isString()
      .trim()
      .isLength({ min: 1, max: 20 })
      .withMessage('Unit is required and must be less than 20 characters'),
    body('preferredTime')
      .optional()
      .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('Preferred time must be in HH:MM format'),
    body('reminderEnabled')
      .isBoolean()
      .withMessage('Reminder enabled must be a boolean'),
  ],

  recordCompletion: () => [
    body('habitId')
      .isString()
      .isLength({ min: 1 })
      .withMessage('Habit ID is required'),
    body('completedAt')
      .isISO8601()
      .withMessage('Completed at must be a valid ISO 8601 date'),
    body('value')
      .isInt({ min: 1 })
      .withMessage('Value must be a positive integer'),
    body('notes')
      .optional()
      .isString()
      .isLength({ max: 500 })
      .withMessage('Notes must be less than 500 characters'),
    body('mood')
      .optional()
      .isInt({ min: 1, max: 10 })
      .withMessage('Mood must be between 1 and 10'),
    body('difficulty')
      .optional()
      .isInt({ min: 1, max: 10 })
      .withMessage('Difficulty must be between 1 and 10'),
  ],
};

// Task validation
export const taskValidations = {
  createTask: () => [
    body('title')
      .isString()
      .trim()
      .isLength({ min: 1, max: 200 })
      .withMessage('Task title is required and must be less than 200 characters'),
    body('description')
      .optional()
      .isString()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Description must be less than 1000 characters'),
    body('taskType')
      .optional()
      .isIn(['WORK', 'PERSONAL', 'HEALTH', 'LEARNING', 'CREATIVE', 'ADMINISTRATIVE', 'SOCIAL'])
      .withMessage('Invalid task type'),
    body('priority')
      .optional()
      .isIn(['LOW', 'MEDIUM', 'HIGH', 'URGENT'])
      .withMessage('Invalid priority level'),
    body('estimatedDuration')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Estimated duration must be a positive integer'),
    body('energyRequired')
      .optional()
      .isInt({ min: 1, max: 5 })
      .withMessage('Energy required must be between 1 and 5'),
    body('focusRequired')
      .optional()
      .isBoolean()
      .withMessage('Focus required must be a boolean'),
    body('dueDate')
      .optional()
      .isISO8601()
      .withMessage('Due date must be a valid ISO 8601 date'),
    body('scheduledAt')
      .optional()
      .isISO8601()
      .withMessage('Scheduled at must be a valid ISO 8601 date'),
  ],
};

// User validation
export const userValidations = {
  updateProfile: () => [
    body('firstName')
      .optional()
      .isString()
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('First name must be between 1 and 50 characters'),
    body('lastName')
      .optional()
      .isString()
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Last name must be between 1 and 50 characters'),
    body('displayName')
      .optional()
      .isString()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Display name must be between 1 and 100 characters'),
    commonValidations.timezone(),
  ],

  updatePreferences: () => [
    body('emailNotifications')
      .optional()
      .isBoolean()
      .withMessage('Email notifications must be a boolean'),
    body('pushNotifications')
      .optional()
      .isBoolean()
      .withMessage('Push notifications must be a boolean'),
    body('theme')
      .optional()
      .isIn(['light', 'dark', 'auto'])
      .withMessage('Theme must be light, dark, or auto'),
    body('language')
      .optional()
      .isString()
      .isLength({ min: 2, max: 5 })
      .withMessage('Language must be a valid language code'),
    body('workingHours')
      .optional()
      .isObject()
      .withMessage('Working hours must be an object'),
    body('workingDays')
      .optional()
      .isArray()
      .withMessage('Working days must be an array'),
  ],
};

// Create validation chain
export const createValidationChain = (validations: ValidationChain[]) => {
  return [...validations, validationMiddleware];
};

// Sanitization helpers
export const sanitizeInput = {
  trim: (field: string) => body(field).trim(),
  escape: (field: string) => body(field).escape(),
  normalizeEmail: (field: string) => body(field).normalizeEmail(),
  toInt: (field: string) => body(field).toInt(),
  toFloat: (field: string) => body(field).toFloat(),
  toBoolean: (field: string) => body(field).toBoolean(),
};

export default validationMiddleware;
