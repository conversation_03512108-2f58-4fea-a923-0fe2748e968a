'use client';

import React from 'react';
import { 
  BarChart3, 
  Clock, 
  Target, 
  TrendingUp, 
  Calendar,
  Award,
  Zap,
  CheckCircle
} from 'lucide-react';
import { useFlowSessions } from '@/hooks/useApi';
import { formatDuration } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface SessionAnalyticsProps {
  className?: string;
  timeRange?: 'week' | 'month' | 'quarter';
}

export function SessionAnalytics({ className, timeRange = 'week' }: SessionAnalyticsProps) {
  const { data: sessionsResponse } = useFlowSessions({
    // Add date range params based on timeRange
    limit: 100
  });

  const sessions = sessionsResponse?.data || [];

  // Calculate analytics
  const analytics = React.useMemo(() => {
    if (sessions.length === 0) {
      return {
        totalSessions: 0,
        totalTime: 0,
        averageSessionLength: 0,
        completionRate: 0,
        mostProductiveHour: 0,
        favoriteSessionTypes: [],
        streakDays: 0,
        longestSession: 0
      };
    }

    const totalSessions = sessions.length;
    const completedSessions = sessions.filter(s => s.endTime).length;
    const totalTime = sessions.reduce((sum, s) => sum + (s.duration || 0), 0);
    const averageSessionLength = totalTime / totalSessions;
    const completionRate = (completedSessions / totalSessions) * 100;

    // Most productive hour
    const hourCounts: { [hour: number]: number } = {};
    sessions.forEach(session => {
      const hour = new Date(session.startTime).getHours();
      hourCounts[hour] = (hourCounts[hour] || 0) + 1;
    });
    const mostProductiveHour = Object.entries(hourCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 0;

    // Favorite session types
    const typeCounts: { [type: string]: { count: number; totalTime: number } } = {};
    sessions.forEach(session => {
      if (!typeCounts[session.taskType]) {
        typeCounts[session.taskType] = { count: 0, totalTime: 0 };
      }
      typeCounts[session.taskType].count++;
      typeCounts[session.taskType].totalTime += session.duration || 0;
    });

    const favoriteSessionTypes = Object.entries(typeCounts)
      .map(([type, data]) => ({ type, ...data }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3);

    // Longest session
    const longestSession = Math.max(...sessions.map(s => s.duration || 0));

    // Simple streak calculation (consecutive days with sessions)
    const sessionDates = [...new Set(sessions.map(s => 
      new Date(s.startTime).toDateString()
    ))].sort();
    
    let streakDays = 0;
    const today = new Date().toDateString();
    let currentDate = new Date();
    
    for (let i = 0; i < 30; i++) { // Check last 30 days
      const dateStr = currentDate.toDateString();
      if (sessionDates.includes(dateStr)) {
        streakDays++;
      } else if (dateStr !== today) {
        break;
      }
      currentDate.setDate(currentDate.getDate() - 1);
    }

    return {
      totalSessions,
      totalTime,
      averageSessionLength,
      completionRate,
      mostProductiveHour: parseInt(mostProductiveHour as string),
      favoriteSessionTypes,
      streakDays,
      longestSession
    };
  }, [sessions]);

  const formatHour = (hour: number) => {
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:00 ${period}`;
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Overview Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Target className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Sessions</span>
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {analytics.totalSessions}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            This {timeRange}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Clock className="w-4 h-4 text-green-600 dark:text-green-400" />
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Time</span>
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {formatDuration(analytics.totalTime * 60 * 1000)}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Focus time
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center space-x-2 mb-2">
            <CheckCircle className="w-4 h-4 text-purple-600 dark:text-purple-400" />
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Completion</span>
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {Math.round(analytics.completionRate)}%
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Success rate
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Award className="w-4 h-4 text-orange-600 dark:text-orange-400" />
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Streak</span>
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {analytics.streakDays}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Days
          </div>
        </div>
      </div>

      {/* Insights */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Session Types */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Favorite Session Types
          </h3>
          <div className="space-y-3">
            {analytics.favoriteSessionTypes.map((sessionType, index) => (
              <div key={sessionType.type} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={cn('w-8 h-8 rounded-lg flex items-center justify-center text-sm font-medium', {
                    'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400': index === 0,
                    'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400': index === 1,
                    'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400': index === 2
                  })}>
                    {index + 1}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {sessionType.type}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {sessionType.count} sessions
                    </div>
                  </div>
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {formatDuration(sessionType.totalTime * 60 * 1000)}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Productivity Insights */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Productivity Insights
          </h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                <Clock className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  Most Productive Hour
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {formatHour(analytics.mostProductiveHour)}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-4 h-4 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  Average Session
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {formatDuration(analytics.averageSessionLength * 60 * 1000)}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <Zap className="w-4 h-4 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  Longest Session
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {formatDuration(analytics.longestSession * 60 * 1000)}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Sessions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Recent Sessions
        </h3>
        <div className="space-y-3">
          {sessions.slice(0, 5).map((session) => (
            <div key={session.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className={cn('w-2 h-2 rounded-full', {
                  'bg-green-500': session.endTime,
                  'bg-yellow-500': !session.endTime
                })} />
                <div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {session.taskType}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {new Date(session.startTime).toLocaleDateString()} at {new Date(session.startTime).toLocaleTimeString()}
                  </div>
                </div>
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {session.duration ? formatDuration(session.duration * 60 * 1000) : 'In progress'}
              </div>
            </div>
          ))}
          
          {sessions.length === 0 && (
            <div className="text-center py-8">
              <BarChart3 className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <div className="text-gray-500 dark:text-gray-400">
                No sessions yet. Start your first flow session to see analytics!
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
