import { Router, Request, Response } from 'express';
import { query, param, validationResult } from 'express-validator';
import { CoachingService, CoachingType } from '../services/coachingService';
import { logger } from '../utils/logger';

const router = Router();
const coachingService = CoachingService.getInstance();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// GET /api/coaching/daily - Get daily coaching session
router.get('/daily', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const coachingSession = await coachingService.generateDailyCoaching(userId);

    res.json({
      data: coachingSession,
    });

    logger.info(`Daily coaching session generated for user ${userId}`);
  } catch (error) {
    logger.error('Error generating daily coaching session:', error);
    res.status(500).json({
      error: 'Failed to generate daily coaching session',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/coaching/weekly - Get weekly coaching session
router.get('/weekly', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const coachingSession = await coachingService.generateWeeklyCoaching(userId);

    res.json({
      data: coachingSession,
    });

    logger.info(`Weekly coaching session generated for user ${userId}`);
  } catch (error) {
    logger.error('Error generating weekly coaching session:', error);
    res.status(500).json({
      error: 'Failed to generate weekly coaching session',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/coaching/insights - Get personalized insights
router.get('/insights', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const insights = await coachingService.getPersonalizedInsights(userId);

    res.json({
      data: insights,
      count: insights.length,
    });
  } catch (error) {
    logger.error('Error getting personalized insights:', error);
    res.status(500).json({
      error: 'Failed to get personalized insights',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/coaching/sessions - Get coaching session history
router.get('/sessions',
  [
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
    query('sessionType').optional().isIn(['DAILY_CHECKIN', 'WEEKLY_REVIEW', 'MONTHLY_ANALYSIS', 'GOAL_SETTING', 'HABIT_COACHING', 'ENERGY_OPTIMIZATION']).withMessage('Invalid session type'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { limit = '10', sessionType } = req.query;

      let sessions = await coachingService.getRecentCoachingSessions(userId, parseInt(limit as string));

      // Filter by session type if specified
      if (sessionType) {
        sessions = sessions.filter(session => session.sessionType === sessionType);
      }

      res.json({
        data: sessions,
        count: sessions.length,
      });
    } catch (error) {
      logger.error('Error getting coaching sessions:', error);
      res.status(500).json({
        error: 'Failed to get coaching sessions',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/coaching/sessions/:sessionId - Get specific coaching session
router.get('/sessions/:sessionId',
  [
    param('sessionId').isString().isLength({ min: 1 }).withMessage('Session ID is required'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { sessionId } = req.params;

      const sessions = await coachingService.getRecentCoachingSessions(userId, 100);
      const session = sessions.find(s => s.id === sessionId);

      if (!session) {
        return res.status(404).json({
          error: 'Session not found',
          message: 'Coaching session not found or does not belong to user',
        });
      }

      res.json({
        data: session,
      });
    } catch (error) {
      logger.error('Error getting coaching session:', error);
      res.status(500).json({
        error: 'Failed to get coaching session',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/coaching/recommendations - Get current recommendations
router.get('/recommendations',
  [
    query('category').optional().isIn(['energy', 'flow', 'habits', 'scheduling', 'wellness']).withMessage('Invalid category'),
    query('priority').optional().isIn(['low', 'medium', 'high']).withMessage('Invalid priority'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { category, priority } = req.query;

      // Get latest coaching session to extract recommendations
      const sessions = await coachingService.getRecentCoachingSessions(userId, 1);
      
      if (sessions.length === 0) {
        // Generate a daily coaching session if none exists
        const newSession = await coachingService.generateDailyCoaching(userId);
        let recommendations = newSession.recommendations;

        // Apply filters
        if (category) {
          recommendations = recommendations.filter(rec => rec.category === category);
        }
        if (priority) {
          recommendations = recommendations.filter(rec => rec.expectedImpact === priority);
        }

        return res.json({
          data: recommendations,
          count: recommendations.length,
        });
      }

      let recommendations = sessions[0].recommendations;

      // Apply filters
      if (category) {
        recommendations = recommendations.filter(rec => rec.category === category);
      }
      if (priority) {
        recommendations = recommendations.filter(rec => rec.expectedImpact === priority);
      }

      res.json({
        data: recommendations,
        count: recommendations.length,
      });
    } catch (error) {
      logger.error('Error getting recommendations:', error);
      res.status(500).json({
        error: 'Failed to get recommendations',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/coaching/metrics - Get coaching metrics and progress
router.get('/metrics',
  [
    query('period').optional().isIn(['week', 'month', 'quarter']).withMessage('Period must be week, month, or quarter'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const period = (req.query.period as string) || 'month';

      const endDate = new Date();
      const startDate = new Date();

      switch (period) {
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(startDate.getMonth() - 3);
          break;
      }

      const sessions = await coachingService.getRecentCoachingSessions(userId, 100);
      const periodSessions = sessions.filter(session => 
        new Date(session.createdAt) >= startDate && new Date(session.createdAt) <= endDate
      );

      if (periodSessions.length === 0) {
        return res.json({
          data: {
            period,
            sessionCount: 0,
            averageProductivityScore: 0,
            averageWellnessScore: 0,
            averageProgressScore: 0,
            trends: {},
          },
        });
      }

      // Calculate average metrics
      const avgProductivity = periodSessions.reduce((sum, s) => sum + s.metrics.productivityScore, 0) / periodSessions.length;
      const avgWellness = periodSessions.reduce((sum, s) => sum + s.metrics.wellnessScore, 0) / periodSessions.length;
      const avgProgress = periodSessions.reduce((sum, s) => sum + s.metrics.progressScore, 0) / periodSessions.length;

      // Calculate trends (compare first half vs second half)
      const firstHalf = periodSessions.slice(0, Math.floor(periodSessions.length / 2));
      const secondHalf = periodSessions.slice(Math.floor(periodSessions.length / 2));

      let trends = {};
      if (firstHalf.length > 0 && secondHalf.length > 0) {
        const firstAvgProductivity = firstHalf.reduce((sum, s) => sum + s.metrics.productivityScore, 0) / firstHalf.length;
        const secondAvgProductivity = secondHalf.reduce((sum, s) => sum + s.metrics.productivityScore, 0) / secondHalf.length;

        const firstAvgWellness = firstHalf.reduce((sum, s) => sum + s.metrics.wellnessScore, 0) / firstHalf.length;
        const secondAvgWellness = secondHalf.reduce((sum, s) => sum + s.metrics.wellnessScore, 0) / secondHalf.length;

        trends = {
          productivityTrend: secondAvgProductivity > firstAvgProductivity ? 'improving' : 'declining',
          wellnessTrend: secondAvgWellness > firstAvgWellness ? 'improving' : 'declining',
          productivityChange: ((secondAvgProductivity - firstAvgProductivity) / firstAvgProductivity * 100).toFixed(1),
          wellnessChange: ((secondAvgWellness - firstAvgWellness) / firstAvgWellness * 100).toFixed(1),
        };
      }

      // Count insights by type
      const insightTypes: { [key: string]: number } = {};
      periodSessions.forEach(session => {
        session.insights.forEach(insight => {
          insightTypes[insight.type] = (insightTypes[insight.type] || 0) + 1;
        });
      });

      const metrics = {
        period,
        sessionCount: periodSessions.length,
        averageProductivityScore: Math.round(avgProductivity * 100) / 100,
        averageWellnessScore: Math.round(avgWellness * 100) / 100,
        averageProgressScore: Math.round(avgProgress * 100) / 100,
        trends,
        insightTypes,
        totalInsights: periodSessions.reduce((sum, s) => sum + s.insights.length, 0),
        totalRecommendations: periodSessions.reduce((sum, s) => sum + s.recommendations.length, 0),
      };

      res.json({
        data: metrics,
      });
    } catch (error) {
      logger.error('Error getting coaching metrics:', error);
      res.status(500).json({
        error: 'Failed to get coaching metrics',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/coaching/summary - Get coaching summary
router.get('/summary', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    // Get recent sessions
    const recentSessions = await coachingService.getRecentCoachingSessions(userId, 7);
    
    // Get latest insights
    const latestInsights = await coachingService.getPersonalizedInsights(userId);

    // Calculate summary stats
    const totalSessions = recentSessions.length;
    const lastSessionDate = recentSessions.length > 0 ? recentSessions[0].createdAt : null;
    
    const avgProductivity = recentSessions.length > 0
      ? recentSessions.reduce((sum, s) => sum + s.metrics.productivityScore, 0) / recentSessions.length
      : 0;

    const avgWellness = recentSessions.length > 0
      ? recentSessions.reduce((sum, s) => sum + s.metrics.wellnessScore, 0) / recentSessions.length
      : 0;

    // Count high priority insights
    const highPriorityInsights = latestInsights.filter(insight => insight.priority === 'high').length;

    // Get latest recommendations
    const latestRecommendations = recentSessions.length > 0 
      ? recentSessions[0].recommendations.slice(0, 3) // Top 3 recommendations
      : [];

    const summary = {
      totalSessions,
      lastSessionDate,
      averageProductivityScore: Math.round(avgProductivity * 100) / 100,
      averageWellnessScore: Math.round(avgWellness * 100) / 100,
      highPriorityInsights,
      totalInsights: latestInsights.length,
      latestRecommendations,
      needsAttention: highPriorityInsights > 0,
    };

    res.json({
      data: summary,
    });
  } catch (error) {
    logger.error('Error getting coaching summary:', error);
    res.status(500).json({
      error: 'Failed to get coaching summary',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/coaching/progress - Get progress tracking
router.get('/progress',
  [
    query('metric').optional().isIn(['productivity', 'wellness', 'energy', 'habits', 'flow']).withMessage('Invalid metric'),
    query('period').optional().isIn(['week', 'month', 'quarter']).withMessage('Period must be week, month, or quarter'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { metric = 'productivity', period = 'month' } = req.query;

      const endDate = new Date();
      const startDate = new Date();

      switch (period) {
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(startDate.getMonth() - 3);
          break;
      }

      const sessions = await coachingService.getRecentCoachingSessions(userId, 100);
      const periodSessions = sessions.filter(session => 
        new Date(session.createdAt) >= startDate && new Date(session.createdAt) <= endDate
      ).reverse(); // Oldest first for progress tracking

      // Create progress data points
      const progressData = periodSessions.map(session => ({
        date: session.createdAt,
        value: metric === 'productivity' ? session.metrics.productivityScore :
               metric === 'wellness' ? session.metrics.wellnessScore :
               session.metrics.progressScore,
        sessionType: session.sessionType,
      }));

      // Calculate progress trend
      let trend = 'stable';
      if (progressData.length >= 2) {
        const firstValue = progressData[0].value;
        const lastValue = progressData[progressData.length - 1].value;
        const change = ((lastValue - firstValue) / firstValue) * 100;
        
        if (change > 5) trend = 'improving';
        else if (change < -5) trend = 'declining';
      }

      const progress = {
        metric,
        period,
        trend,
        dataPoints: progressData,
        currentValue: progressData.length > 0 ? progressData[progressData.length - 1].value : 0,
        averageValue: progressData.length > 0 
          ? progressData.reduce((sum, p) => sum + p.value, 0) / progressData.length 
          : 0,
      };

      res.json({
        data: progress,
      });
    } catch (error) {
      logger.error('Error getting progress tracking:', error);
      res.status(500).json({
        error: 'Failed to get progress tracking',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

export default router;
