'use client';

import React, { useState } from 'react';
import { format, parseISO } from 'date-fns';
import { Edit2, Trash2, MessageSquare, Clock, Heart, Brain } from 'lucide-react';
import { EnergyData } from '@/types/api';
import { getEnergyLevelLabel, getEnergyLevelColor, formatRelativeTime, cn } from '@/lib/utils';

interface EnergyHistoryProps {
  entries: EnergyData[];
  onEdit?: (entry: EnergyData) => void;
  onDelete?: (entryId: string) => void;
  showActions?: boolean;
}

export function EnergyHistory({ entries, onEdit, onDelete, showActions = false }: EnergyHistoryProps) {
  const [expandedEntry, setExpandedEntry] = useState<string | null>(null);

  if (!entries || entries.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
        <p className="text-sm">No energy logs yet</p>
        <p className="text-xs mt-1">Start tracking your energy to see your history</p>
      </div>
    );
  }

  const toggleExpanded = (entryId: string) => {
    setExpandedEntry(expandedEntry === entryId ? null : entryId);
  };

  return (
    <div className="space-y-3">
      {entries.map((entry) => {
        const isExpanded = expandedEntry === entry.id;
        const energyColor = getEnergyLevelColor(entry.energyLevel);
        
        return (
          <div
            key={entry.id}
            className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
          >
            {/* Main Entry Row */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 flex-1">
                {/* Energy Level Badge */}
                <div className={cn('px-3 py-1 rounded-full text-xs font-medium border', energyColor)}>
                  {getEnergyLevelLabel(entry.energyLevel)}
                </div>

                {/* Timestamp */}
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {format(parseISO(entry.timestamp), 'h:mm a')}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {formatRelativeTime(entry.timestamp)}
                  </span>
                </div>

                {/* Quick Metrics */}
                <div className="hidden sm:flex items-center space-x-4 text-xs text-gray-600 dark:text-gray-400">
                  <div className="flex items-center space-x-1">
                    <Heart className="w-3 h-3" />
                    <span>{entry.mood}/10</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Brain className="w-3 h-3" />
                    <span>{entry.stressLevel}/10</span>
                  </div>
                </div>

                {/* Context Preview */}
                {entry.context && (
                  <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                    <MessageSquare className="w-3 h-3" />
                    <span className="truncate max-w-[100px]">
                      {entry.context.substring(0, 30)}
                      {entry.context.length > 30 ? '...' : ''}
                    </span>
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => toggleExpanded(entry.id)}
                  className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  {isExpanded ? 'Less' : 'More'}
                </button>

                {showActions && (
                  <>
                    {onEdit && (
                      <button
                        onClick={() => onEdit(entry)}
                        className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                        title="Edit entry"
                      >
                        <Edit2 className="w-4 h-4" />
                      </button>
                    )}

                    {onDelete && (
                      <button
                        onClick={() => onDelete(entry.id)}
                        className="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                        title="Delete entry"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Expanded Details */}
            {isExpanded && (
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-900 dark:text-white">
                      {entry.energyLevel}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Energy Level
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="text-lg font-bold text-pink-600 dark:text-pink-400">
                      {entry.mood}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Mood
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="text-lg font-bold text-red-600 dark:text-red-400">
                      {entry.stressLevel}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Stress Level
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                      {entry.sleepQuality || 'N/A'}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Sleep Quality
                    </div>
                  </div>
                </div>

                {/* Additional Metrics */}
                {(entry.physicalActivity || entry.caffeine !== undefined) && (
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    {entry.physicalActivity && (
                      <div className="text-center">
                        <div className="text-lg font-bold text-green-600 dark:text-green-400">
                          {entry.physicalActivity}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Physical Activity
                        </div>
                      </div>
                    )}

                    {entry.caffeine !== undefined && (
                      <div className="text-center">
                        <div className="text-lg font-bold text-amber-600 dark:text-amber-400">
                          {entry.caffeine}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Caffeine Intake
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Context */}
                {entry.context && (
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                    <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Context:
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {entry.context}
                    </p>
                  </div>
                )}

                {/* Full Timestamp */}
                <div className="mt-3 text-xs text-gray-500 dark:text-gray-400 text-center">
                  Logged on {format(parseISO(entry.timestamp), 'EEEE, MMMM d, yyyy \'at\' h:mm a')}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
