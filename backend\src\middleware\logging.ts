import { Request, Response, NextFunction } from 'express';
import morgan from 'morgan';
import { logger, morganStream, logApiRequest, logSecurityEvent, logUserActivity } from '../utils/logger';

// Custom token for Morgan to include user ID
morgan.token('user-id', (req: Request) => {
  return req.user?.id || 'anonymous';
});

// Custom token for request ID
morgan.token('request-id', (req: Request) => {
  return req.headers['x-request-id'] as string || 'none';
});

// Custom token for response time in milliseconds
morgan.token('response-time-ms', (req: Request, res: Response) => {
  const startTime = req.startTime;
  if (!startTime) return '0';
  return `${Date.now() - startTime}ms`;
});

// Custom token for request body size
morgan.token('req-size', (req: Request) => {
  return req.headers['content-length'] || '0';
});

// Custom token for response body size
morgan.token('res-size', (req: Request, res: Response) => {
  return res.get('content-length') || '0';
});

// Development logging format
const developmentFormat = morgan(
  ':method :url :status :response-time-ms - :res[content-length] bytes - User: :user-id - :request-id',
  {
    stream: morganStream,
    skip: (req: Request) => {
      // Skip health check endpoints in development
      return req.url === '/health' || req.url === '/api/health';
    },
  }
);

// Production logging format
const productionFormat = morgan(
  ':remote-addr - :user-id [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :response-time-ms :request-id',
  {
    stream: morganStream,
    skip: (req: Request) => {
      // Skip health check endpoints and successful static file requests
      return req.url === '/health' || 
             req.url === '/api/health' || 
             (req.url.startsWith('/static') && req.statusCode < 400);
    },
  }
);

// Request timing middleware
export const requestTiming = (req: Request, res: Response, next: NextFunction): void => {
  req.startTime = Date.now();
  next();
};

// Request ID middleware
export const requestId = (req: Request, res: Response, next: NextFunction): void => {
  const requestId = req.headers['x-request-id'] as string || 
                   `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  req.headers['x-request-id'] = requestId;
  res.setHeader('X-Request-ID', requestId);
  
  next();
};

// Detailed request logging middleware
export const detailedLogging = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  
  // Log request details
  logger.debug('Incoming request', {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    requestId: req.headers['x-request-id'],
    headers: req.headers,
    query: req.query,
    body: req.method !== 'GET' ? sanitizeRequestBody(req.body) : undefined,
  });

  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any) {
    const duration = Date.now() - startTime;
    
    // Log response details
    logger.debug('Outgoing response', {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration,
      userId: req.user?.id,
      requestId: req.headers['x-request-id'],
      responseHeaders: res.getHeaders(),
    });

    // Log API request for analytics
    logApiRequest(req, res, duration);

    // Call original end method
    originalEnd.call(this, chunk, encoding);
  };

  next();
};

// Security logging middleware
export const securityLogging = (req: Request, res: Response, next: NextFunction): void => {
  // Log suspicious activities
  const suspiciousPatterns = [
    /\.\./,  // Directory traversal
    /<script/i,  // XSS attempts
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
    /eval\(/i,  // Code injection
  ];

  const url = req.originalUrl;
  const userAgent = req.get('User-Agent') || '';
  const body = JSON.stringify(req.body);

  // Check for suspicious patterns
  const isSuspicious = suspiciousPatterns.some(pattern => 
    pattern.test(url) || pattern.test(userAgent) || pattern.test(body)
  );

  if (isSuspicious) {
    logSecurityEvent('Suspicious request detected', {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent,
      userId: req.user?.id,
      body: sanitizeRequestBody(req.body),
    }, 'high');
  }

  // Log failed authentication attempts
  res.on('finish', () => {
    if (res.statusCode === 401 && req.originalUrl.includes('/auth/')) {
      logSecurityEvent('Authentication failure', {
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent,
        statusCode: res.statusCode,
      }, 'medium');
    }

    // Log rate limiting
    if (res.statusCode === 429) {
      logSecurityEvent('Rate limit exceeded', {
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent,
        userId: req.user?.id,
      }, 'medium');
    }
  });

  next();
};

// User activity logging middleware
export const userActivityLogging = (req: Request, res: Response, next: NextFunction): void => {
  // Only log for authenticated users
  if (!req.user) {
    return next();
  }

  // Log significant user activities
  res.on('finish', () => {
    if (res.statusCode >= 200 && res.statusCode < 300) {
      const activity = getActivityFromRequest(req);
      if (activity) {
        logUserActivity(req.user!.id, activity, {
          method: req.method,
          url: req.originalUrl,
          statusCode: res.statusCode,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        });
      }
    }
  });

  next();
};

// Performance logging middleware
export const performanceLogging = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    // Log slow requests
    if (duration > 1000) { // Requests taking more than 1 second
      logger.warn('Slow request detected', {
        method: req.method,
        url: req.originalUrl,
        duration,
        statusCode: res.statusCode,
        userId: req.user?.id,
        ip: req.ip,
      });
    }

    // Log performance metrics for specific endpoints
    if (req.originalUrl.startsWith('/api/')) {
      logger.debug('API performance', {
        endpoint: req.originalUrl,
        method: req.method,
        duration,
        statusCode: res.statusCode,
        userId: req.user?.id,
      });
    }
  });

  next();
};

// Error logging middleware (should be used after error handler)
export const errorLogging = (err: Error, req: Request, res: Response, next: NextFunction): void => {
  logger.error('Request error', {
    error: {
      message: err.message,
      stack: err.stack,
      name: err.name,
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      requestId: req.headers['x-request-id'],
      body: sanitizeRequestBody(req.body),
      query: req.query,
    },
  });

  next(err);
};

// Get HTTP logging middleware based on environment
export const getHttpLoggingMiddleware = () => {
  return process.env.NODE_ENV === 'production' ? productionFormat : developmentFormat;
};

// Sanitize request body for logging (remove sensitive data)
function sanitizeRequestBody(body: any): any {
  if (!body || typeof body !== 'object') {
    return body;
  }

  const sensitiveFields = [
    'password',
    'token',
    'accessToken',
    'refreshToken',
    'secret',
    'key',
    'authorization',
    'auth',
    'credential',
    'credentials',
  ];

  const sanitized = { ...body };

  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  }

  return sanitized;
}

// Get activity description from request
function getActivityFromRequest(req: Request): string | null {
  const { method, originalUrl } = req;

  // Map common endpoints to activities
  const activityMap: { [key: string]: string } = {
    'POST /api/energy/record': 'Energy data recorded',
    'POST /api/flow/session/start': 'Flow session started',
    'POST /api/flow/session/:id/end': 'Flow session ended',
    'POST /api/habits': 'Habit created',
    'POST /api/habits/:id/complete': 'Habit completed',
    'POST /api/tasks': 'Task created',
    'PUT /api/tasks/:id': 'Task updated',
    'POST /api/integrations/*/connect': 'Integration connected',
    'DELETE /api/integrations/*/disconnect': 'Integration disconnected',
    'PUT /api/users/profile': 'Profile updated',
    'PUT /api/users/preferences': 'Preferences updated',
  };

  // Create a pattern key from the request
  const patternKey = `${method} ${originalUrl.replace(/\/[a-f0-9-]{20,}/g, '/:id')}`;
  
  return activityMap[patternKey] || null;
}

// Middleware to skip logging for certain routes
export const skipLogging = (patterns: (string | RegExp)[]): ((req: Request, res: Response, next: NextFunction) => void) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const shouldSkip = patterns.some(pattern => {
      if (typeof pattern === 'string') {
        return req.originalUrl === pattern;
      } else {
        return pattern.test(req.originalUrl);
      }
    });

    if (shouldSkip) {
      req.skipLogging = true;
    }

    next();
  };
};

// Combine all logging middleware
export const createLoggingMiddleware = () => {
  return [
    requestId,
    requestTiming,
    getHttpLoggingMiddleware(),
    detailedLogging,
    securityLogging,
    userActivityLogging,
    performanceLogging,
  ];
};

// Export individual middleware for selective use
export {
  developmentFormat,
  productionFormat,
};

// Extend Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      startTime?: number;
      skipLogging?: boolean;
    }
  }
}

export default createLoggingMiddleware;
