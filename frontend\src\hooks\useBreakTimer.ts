import { useState, useEffect, useCallback, useRef } from 'react';
import { BreakTimer, BreakType, STORAGE_KEYS } from '@/types/flow-session';

export function useBreakTimer() {
  const [breakTimer, setBreakTimer] = useState<BreakTimer | null>(null);
  const [isActive, setIsActive] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(0);
  
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<Date | null>(null);

  // Break suggestions based on type
  const getBreakSuggestions = useCallback((type: BreakType): string[] => {
    switch (type) {
      case 'short':
        return [
          'Take a few deep breaths',
          'Stretch your arms and shoulders',
          'Look away from the screen',
          'Drink some water',
          'Do a quick walk around'
        ];
      case 'long':
        return [
          'Take a walk outside',
          'Do some light exercise',
          'Have a healthy snack',
          'Practice mindfulness',
          'Chat with a colleague',
          'Step away from your workspace'
        ];
      case 'custom':
        return [
          'Use this time as you planned',
          'Focus on your break activity',
          'Relax and recharge'
        ];
      default:
        return ['Take a moment to rest'];
    }
  }, []);

  // Start break timer
  const startBreak = useCallback((type: BreakType, duration?: number) => {
    const breakDuration = duration || (type === 'short' ? 5 : type === 'long' ? 15 : 10);
    
    const newBreakTimer: BreakTimer = {
      type,
      duration: breakDuration,
      startTime: new Date(),
      isActive: true,
      suggestions: getBreakSuggestions(type)
    };

    setBreakTimer(newBreakTimer);
    setIsActive(true);
    setTimeRemaining(breakDuration * 60); // Convert to seconds
    startTimeRef.current = new Date();

    // Save to localStorage
    localStorage.setItem(STORAGE_KEYS.ACTIVE_SESSION, JSON.stringify({
      ...JSON.parse(localStorage.getItem(STORAGE_KEYS.ACTIVE_SESSION) || '{}'),
      breakTimer: newBreakTimer
    }));
  }, [getBreakSuggestions]);

  // End break timer
  const endBreak = useCallback(() => {
    if (breakTimer) {
      const endedBreak = {
        ...breakTimer,
        endTime: new Date(),
        isActive: false
      };
      
      setBreakTimer(endedBreak);
      setIsActive(false);
      setTimeRemaining(0);
      
      // Clear timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      // Update localStorage
      const activeSession = JSON.parse(localStorage.getItem(STORAGE_KEYS.ACTIVE_SESSION) || '{}');
      delete activeSession.breakTimer;
      localStorage.setItem(STORAGE_KEYS.ACTIVE_SESSION, JSON.stringify(activeSession));

      // Clear break timer after a short delay
      setTimeout(() => {
        setBreakTimer(null);
      }, 1000);
    }
  }, [breakTimer]);

  // Skip break
  const skipBreak = useCallback(() => {
    endBreak();
  }, [endBreak]);

  // Timer effect
  useEffect(() => {
    if (isActive && timeRemaining > 0) {
      timerRef.current = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            // Break time is up
            endBreak();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isActive, timeRemaining, endBreak]);

  // Load break timer from localStorage on mount
  useEffect(() => {
    const activeSession = localStorage.getItem(STORAGE_KEYS.ACTIVE_SESSION);
    if (activeSession) {
      const session = JSON.parse(activeSession);
      if (session.breakTimer && session.breakTimer.isActive) {
        const savedBreak = session.breakTimer;
        const startTime = new Date(savedBreak.startTime);
        const now = new Date();
        const elapsedSeconds = Math.floor((now.getTime() - startTime.getTime()) / 1000);
        const totalSeconds = savedBreak.duration * 60;
        const remaining = Math.max(0, totalSeconds - elapsedSeconds);

        if (remaining > 0) {
          setBreakTimer(savedBreak);
          setIsActive(true);
          setTimeRemaining(remaining);
          startTimeRef.current = startTime;
        }
      }
    }
  }, []);

  // Format time remaining for display
  const formatTimeRemaining = useCallback((seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  // Get break progress percentage
  const getProgress = useCallback((): number => {
    if (!breakTimer || !isActive) return 0;
    const totalSeconds = breakTimer.duration * 60;
    const elapsedSeconds = totalSeconds - timeRemaining;
    return Math.min(100, (elapsedSeconds / totalSeconds) * 100);
  }, [breakTimer, isActive, timeRemaining]);

  return {
    // State
    breakTimer,
    isActive,
    timeRemaining,
    
    // Actions
    startBreak,
    endBreak,
    skipBreak,
    
    // Computed values
    formattedTimeRemaining: formatTimeRemaining(timeRemaining),
    progress: getProgress(),
    
    // Suggestions
    suggestions: breakTimer?.suggestions || []
  };
}

// Hook for managing ambient sounds
export function useAmbientSound() {
  const [currentSound, setCurrentSound] = useState<string | null>(null);
  const [volume, setVolume] = useState(50);
  const [isPlaying, setIsPlaying] = useState(false);
  
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Load settings from localStorage
  useEffect(() => {
    const settings = localStorage.getItem(STORAGE_KEYS.AMBIENT_SOUND_SETTINGS);
    if (settings) {
      const { soundId, volume: savedVolume, isPlaying: wasPlaying } = JSON.parse(settings);
      setCurrentSound(soundId);
      setVolume(savedVolume);
      setIsPlaying(wasPlaying);
    }
  }, []);

  // Save settings to localStorage
  const saveSettings = useCallback(() => {
    const settings = {
      soundId: currentSound,
      volume,
      isPlaying
    };
    localStorage.setItem(STORAGE_KEYS.AMBIENT_SOUND_SETTINGS, JSON.stringify(settings));
  }, [currentSound, volume, isPlaying]);

  // Play sound
  const playSound = useCallback((soundId: string) => {
    const sound = AMBIENT_SOUNDS.find(s => s.id === soundId);
    if (!sound) return;

    if (audioRef.current) {
      audioRef.current.pause();
    }

    audioRef.current = new Audio(sound.url);
    audioRef.current.loop = sound.isLooping;
    audioRef.current.volume = volume / 100;
    
    audioRef.current.play().then(() => {
      setCurrentSound(soundId);
      setIsPlaying(true);
      saveSettings();
    }).catch(error => {
      console.error('Failed to play ambient sound:', error);
    });
  }, [volume, saveSettings]);

  // Stop sound
  const stopSound = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }
    setIsPlaying(false);
    saveSettings();
  }, [saveSettings]);

  // Change volume
  const changeVolume = useCallback((newVolume: number) => {
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume / 100;
    }
    saveSettings();
  }, [saveSettings]);

  // Toggle play/pause
  const togglePlayback = useCallback(() => {
    if (isPlaying) {
      stopSound();
    } else if (currentSound) {
      playSound(currentSound);
    }
  }, [isPlaying, currentSound, playSound, stopSound]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
      }
    };
  }, []);

  return {
    // State
    currentSound,
    volume,
    isPlaying,
    
    // Actions
    playSound,
    stopSound,
    changeVolume,
    togglePlayback,
    
    // Available sounds
    availableSounds: AMBIENT_SOUNDS
  };
}
