import { Router, Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import bcrypt from 'bcryptjs';
import { prisma } from '../config/database';
import { TokenService, SessionService } from '../middleware/auth';
import { logger } from '../utils/logger';

const router = Router();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// POST /api/auth/register - User registration
router.post('/register',
  [
    body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
    body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/).withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
    body('firstName').optional().isString().isLength({ min: 1, max: 50 }).withMessage('First name must be between 1 and 50 characters'),
    body('lastName').optional().isString().isLength({ min: 1, max: 50 }).withMessage('Last name must be between 1 and 50 characters'),
    body('timezone').optional().isString().withMessage('Timezone must be a string'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { email, password, firstName, lastName, timezone } = req.body;

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        return res.status(409).json({
          error: 'User already exists',
          message: 'An account with this email already exists',
        });
      }

      // Hash password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // Create user
      const user = await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          firstName,
          lastName,
          displayName: firstName && lastName ? `${firstName} ${lastName}` : firstName || email.split('@')[0],
          timezone: timezone || 'UTC',
          role: 'USER',
          isActive: true,
          isEmailVerified: false, // In production, implement email verification
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          displayName: true,
          role: true,
          isActive: true,
          createdAt: true,
        },
      });

      // Create default user preferences
      await prisma.userPreferences.create({
        data: {
          userId: user.id,
          emailNotifications: true,
          pushNotifications: true,
          theme: 'light',
          language: 'en',
          workingHours: { start: '09:00', end: '17:00' },
          workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        },
      });

      // Generate tokens
      const accessToken = TokenService.generateAccessToken(user);
      const refreshToken = TokenService.generateRefreshToken(user);

      // Create session
      const sessionId = await SessionService.createSession(user.id, {
        userAgent: req.headers['user-agent'],
        ip: req.ip,
      });

      res.status(201).json({
        message: 'User registered successfully',
        data: {
          user,
          tokens: {
            accessToken,
            refreshToken,
          },
          sessionId,
        },
      });

      logger.info(`User registered: ${user.email}`);
    } catch (error) {
      logger.error('Error during user registration:', error);
      res.status(500).json({
        error: 'Registration failed',
        message: 'An error occurred during registration',
      });
    }
  }
);

// POST /api/auth/login - User login
router.post('/login',
  [
    body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
    body('password').isLength({ min: 1 }).withMessage('Password is required'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { email, password } = req.body;

      // Find user
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          id: true,
          email: true,
          password: true,
          firstName: true,
          lastName: true,
          displayName: true,
          role: true,
          isActive: true,
          isEmailVerified: true,
        },
      });

      if (!user) {
        return res.status(401).json({
          error: 'Invalid credentials',
          message: 'Email or password is incorrect',
        });
      }

      // Check if user is active
      if (!user.isActive) {
        return res.status(401).json({
          error: 'Account deactivated',
          message: 'Your account has been deactivated',
        });
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password!);
      if (!isPasswordValid) {
        return res.status(401).json({
          error: 'Invalid credentials',
          message: 'Email or password is incorrect',
        });
      }

      // Update last login
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
      });

      // Generate tokens
      const accessToken = TokenService.generateAccessToken(user);
      const refreshToken = TokenService.generateRefreshToken(user);

      // Create session
      const sessionId = await SessionService.createSession(user.id, {
        userAgent: req.headers['user-agent'],
        ip: req.ip,
      });

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user;

      res.json({
        message: 'Login successful',
        data: {
          user: userWithoutPassword,
          tokens: {
            accessToken,
            refreshToken,
          },
          sessionId,
        },
      });

      logger.info(`User logged in: ${user.email}`);
    } catch (error) {
      logger.error('Error during user login:', error);
      res.status(500).json({
        error: 'Login failed',
        message: 'An error occurred during login',
      });
    }
  }
);

// POST /api/auth/refresh - Refresh access token
router.post('/refresh',
  [
    body('refreshToken').isString().withMessage('Refresh token is required'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { refreshToken } = req.body;

      // Verify refresh token
      let decoded;
      try {
        decoded = TokenService.verifyRefreshToken(refreshToken);
      } catch (error) {
        return res.status(401).json({
          error: 'Invalid refresh token',
          message: 'Refresh token is invalid or expired',
        });
      }

      // Check if token type is refresh token
      if (decoded.type !== 'refresh') {
        return res.status(401).json({
          error: 'Invalid token type',
          message: 'Refresh token required',
        });
      }

      // Find user
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          email: true,
          role: true,
          isActive: true,
        },
      });

      if (!user || !user.isActive) {
        return res.status(401).json({
          error: 'User not found or inactive',
          message: 'User associated with token does not exist or is inactive',
        });
      }

      // Generate new access token
      const newAccessToken = TokenService.generateAccessToken(user);

      res.json({
        message: 'Token refreshed successfully',
        data: {
          accessToken: newAccessToken,
        },
      });

      logger.info(`Token refreshed for user: ${user.email}`);
    } catch (error) {
      logger.error('Error during token refresh:', error);
      res.status(500).json({
        error: 'Token refresh failed',
        message: 'An error occurred during token refresh',
      });
    }
  }
);

// POST /api/auth/logout - User logout
router.post('/logout',
  [
    body('sessionId').optional().isString().withMessage('Session ID must be a string'),
  ],
  async (req: Request, res: Response) => {
    try {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        
        try {
          const decoded = TokenService.verifyRefreshToken(token);
          const { sessionId } = req.body;

          if (sessionId) {
            await SessionService.revokeSession(decoded.userId, sessionId);
          }

          // In a production environment, you might want to blacklist the token
          logger.info(`User logged out: ${decoded.userId}`);
        } catch (error) {
          // Token might be invalid, but we still want to allow logout
          logger.warn('Invalid token during logout:', error);
        }
      }

      res.json({
        message: 'Logout successful',
      });
    } catch (error) {
      logger.error('Error during logout:', error);
      res.status(500).json({
        error: 'Logout failed',
        message: 'An error occurred during logout',
      });
    }
  }
);

// POST /api/auth/forgot-password - Request password reset
router.post('/forgot-password',
  [
    body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { email } = req.body;

      // Find user
      const user = await prisma.user.findUnique({
        where: { email },
        select: { id: true, email: true, isActive: true },
      });

      // Always return success to prevent email enumeration
      res.json({
        message: 'If an account with that email exists, a password reset link has been sent',
      });

      if (user && user.isActive) {
        // In production, generate a secure reset token and send email
        // For now, just log the action
        logger.info(`Password reset requested for user: ${user.email}`);
        
        // TODO: Implement password reset token generation and email sending
        // const resetToken = generateSecureToken();
        // await storeResetToken(user.id, resetToken);
        // await sendPasswordResetEmail(user.email, resetToken);
      }
    } catch (error) {
      logger.error('Error during password reset request:', error);
      res.status(500).json({
        error: 'Password reset failed',
        message: 'An error occurred during password reset request',
      });
    }
  }
);

// POST /api/auth/reset-password - Reset password with token
router.post('/reset-password',
  [
    body('token').isString().withMessage('Reset token is required'),
    body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/).withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { token, password } = req.body;

      // In production, verify the reset token and find associated user
      // For now, return an error since token system is not implemented
      return res.status(400).json({
        error: 'Password reset not implemented',
        message: 'Password reset functionality is not yet implemented',
      });

      // TODO: Implement password reset logic
      // const userId = await verifyResetToken(token);
      // if (!userId) {
      //   return res.status(400).json({
      //     error: 'Invalid or expired token',
      //     message: 'The reset token is invalid or has expired',
      //   });
      // }

      // const hashedPassword = await bcrypt.hash(password, 12);
      // await prisma.user.update({
      //   where: { id: userId },
      //   data: { password: hashedPassword },
      // });

      // await invalidateResetToken(token);

      // res.json({
      //   message: 'Password reset successful',
      // });
    } catch (error) {
      logger.error('Error during password reset:', error);
      res.status(500).json({
        error: 'Password reset failed',
        message: 'An error occurred during password reset',
      });
    }
  }
);

// GET /api/auth/me - Get current user info (requires authentication)
router.get('/me', async (req: Request, res: Response) => {
  try {
    // This endpoint requires authentication, so user should be attached by middleware
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Please provide a valid access token',
      });
    }

    const token = authHeader.substring(7);
    const decoded = TokenService.verifyRefreshToken(token);

    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        displayName: true,
        avatar: true,
        timezone: true,
        role: true,
        isActive: true,
        isEmailVerified: true,
        isPremium: true,
        onboardingCompleted: true,
        createdAt: true,
        lastLoginAt: true,
      },
    });

    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User associated with token does not exist',
      });
    }

    res.json({
      data: user,
    });
  } catch (error) {
    logger.error('Error getting user info:', error);
    res.status(500).json({
      error: 'Failed to get user info',
      message: 'An error occurred while retrieving user information',
    });
  }
});

// POST /api/auth/change-password - Change password (requires authentication)
router.post('/change-password',
  [
    body('currentPassword').isString().withMessage('Current password is required'),
    body('newPassword').isLength({ min: 8 }).withMessage('New password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/).withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'Please provide a valid access token',
        });
      }

      const token = authHeader.substring(7);
      const decoded = TokenService.verifyRefreshToken(token);
      const { currentPassword, newPassword } = req.body;

      // Find user with password
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          email: true,
          password: true,
        },
      });

      if (!user) {
        return res.status(404).json({
          error: 'User not found',
          message: 'User not found',
        });
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password!);
      if (!isCurrentPasswordValid) {
        return res.status(400).json({
          error: 'Invalid current password',
          message: 'Current password is incorrect',
        });
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(newPassword, 12);

      // Update password
      await prisma.user.update({
        where: { id: user.id },
        data: { password: hashedNewPassword },
      });

      // Revoke all existing tokens for security
      await TokenService.revokeUserTokens(user.id);

      res.json({
        message: 'Password changed successfully',
      });

      logger.info(`Password changed for user: ${user.email}`);
    } catch (error) {
      logger.error('Error changing password:', error);
      res.status(500).json({
        error: 'Password change failed',
        message: 'An error occurred while changing password',
      });
    }
  }
);

export default router;
