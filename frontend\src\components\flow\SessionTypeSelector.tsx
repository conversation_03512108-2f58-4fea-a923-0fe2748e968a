'use client';

import React, { useState } from 'react';
import { 
  Target, 
  Palette, 
  BookOpen, 
  Calendar, 
  MessageCircle, 
  Settings,
  Clock,
  Coffee,
  ChevronDown,
  Check
} from 'lucide-react';
import { SessionType, SESSION_TYPES } from '@/types/flow-session';
import { cn } from '@/lib/utils';

interface SessionTypeSelectorProps {
  selectedType?: SessionType;
  onSelect: (sessionType: SessionType) => void;
  className?: string;
  showDuration?: boolean;
  compact?: boolean;
}

const iconMap = {
  Target,
  Palette,
  BookOpen,
  Calendar,
  MessageCircle,
  Settings
};

export function SessionTypeSelector({ 
  selectedType, 
  onSelect, 
  className,
  showDuration = true,
  compact = false 
}: SessionTypeSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  const getIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap] || Target;
    return IconComponent;
  };

  const getColorClasses = (color: string) => {
    const colorMap = {
      purple: 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-800',
      pink: 'bg-pink-100 dark:bg-pink-900/20 text-pink-700 dark:text-pink-300 border-pink-200 dark:border-pink-800',
      blue: 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800',
      green: 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800',
      orange: 'bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-800',
      gray: 'bg-gray-100 dark:bg-gray-900/20 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-800'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.purple;
  };

  if (compact) {
    return (
      <div className={cn('relative', className)}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center space-x-2 px-3 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          {selectedType && (
            <>
              <div className={cn('w-6 h-6 rounded flex items-center justify-center', getColorClasses(selectedType.color))}>
                {React.createElement(getIcon(selectedType.icon), { className: 'w-3 h-3' })}
              </div>
              <span className="text-sm font-medium">{selectedType.name}</span>
            </>
          )}
          {!selectedType && (
            <>
              <Target className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-500">Select type</span>
            </>
          )}
          <ChevronDown className="w-4 h-4 text-gray-400" />
        </button>

        {isOpen && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
            <div className="p-2 space-y-1">
              {SESSION_TYPES.map((type) => {
                const Icon = getIcon(type.icon);
                return (
                  <button
                    key={type.id}
                    onClick={() => {
                      onSelect(type);
                      setIsOpen(false);
                    }}
                    className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-left"
                  >
                    <div className={cn('w-6 h-6 rounded flex items-center justify-center', getColorClasses(type.color))}>
                      <Icon className="w-3 h-3" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">{type.name}</div>
                      {showDuration && (
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {type.defaultDuration} min
                        </div>
                      )}
                    </div>
                    {selectedType?.id === type.id && (
                      <Check className="w-4 h-4 text-green-500" />
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Choose Session Type
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Select the type of work you'll be focusing on
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {SESSION_TYPES.map((type) => {
          const Icon = getIcon(type.icon);
          const isSelected = selectedType?.id === type.id;
          
          return (
            <button
              key={type.id}
              onClick={() => onSelect(type)}
              className={cn(
                'p-4 rounded-lg border-2 transition-all duration-200 text-left',
                isSelected
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800'
              )}
            >
              <div className="flex items-start space-x-3">
                <div className={cn('w-10 h-10 rounded-lg flex items-center justify-center', getColorClasses(type.color))}>
                  <Icon className="w-5 h-5" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="text-sm font-semibold text-gray-900 dark:text-white">
                      {type.name}
                    </h4>
                    {isSelected && (
                      <Check className="w-4 h-4 text-blue-500" />
                    )}
                  </div>
                  
                  <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                    {type.description}
                  </p>
                  
                  <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>{type.defaultDuration} min</span>
                    </div>
                    
                    {type.suggestedBreakInterval && (
                      <div className="flex items-center space-x-1">
                        <Coffee className="w-3 h-3" />
                        <span>Break every {type.suggestedBreakInterval} min</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </button>
          );
        })}
      </div>

      {selectedType && (
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-start space-x-3">
            <div className={cn('w-8 h-8 rounded flex items-center justify-center', getColorClasses(selectedType.color))}>
              {React.createElement(getIcon(selectedType.icon), { className: 'w-4 h-4' })}
            </div>
            <div>
              <h4 className="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-1">
                {selectedType.name} Session
              </h4>
              <p className="text-xs text-blue-700 dark:text-blue-300 mb-2">
                {selectedType.description}
              </p>
              <div className="flex items-center space-x-4 text-xs text-blue-600 dark:text-blue-400">
                <span>Duration: {selectedType.defaultDuration} minutes</span>
                {selectedType.suggestedBreakInterval && (
                  <span>Break interval: {selectedType.suggestedBreakInterval} minutes</span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
