import { prisma } from '../config/database';
import { cacheService, <PERSON><PERSON><PERSON>ey<PERSON> } from '../config/redis';
import { logger } from '../utils/logger';
import * as tf from '@tensorflow/tfjs-node';

// Energy level enum
export enum EnergyLevel {
  VERY_LOW = 1,
  LOW = 2,
  MODERATE = 3,
  HIGH = 4,
  VERY_HIGH = 5,
}

// Energy data interfaces
export interface EnergyDataPoint {
  timestamp: Date;
  energyLevel: EnergyLevel;
  mood: number; // 1-10 scale
  stressLevel: number; // 1-10 scale
  sleepQuality?: number; // 1-10 scale
  physicalActivity?: number; // minutes
  caffeine?: number; // mg
  context?: string;
}

export interface CircadianPattern {
  userId: string;
  peakEnergyHours: number[];
  lowEnergyHours: number[];
  averagePattern: number[];
  confidence: number;
  lastUpdated: Date;
}

export interface EnergyPrediction {
  timestamp: Date;
  predictedEnergyLevel: number;
  confidence: number;
  factors: {
    sleepImpact: number;
    circadianImpact: number;
    activityImpact: number;
    stressImpact: number;
  };
}

export class EnergyAnalysisService {
  private static instance: EnergyAnalysisService;
  private mlModel: tf.LayersModel | null = null;

  private constructor() {
    this.initializeMLModel();
  }

  public static getInstance(): EnergyAnalysisService {
    if (!EnergyAnalysisService.instance) {
      EnergyAnalysisService.instance = new EnergyAnalysisService();
    }
    return EnergyAnalysisService.instance;
  }

  private async initializeMLModel(): Promise<void> {
    try {
      // In a real implementation, you would load a pre-trained model
      // For now, we'll create a simple neural network for energy prediction
      this.mlModel = tf.sequential({
        layers: [
          tf.layers.dense({ inputShape: [24], units: 64, activation: 'relu' }),
          tf.layers.dropout({ rate: 0.2 }),
          tf.layers.dense({ units: 32, activation: 'relu' }),
          tf.layers.dropout({ rate: 0.2 }),
          tf.layers.dense({ units: 16, activation: 'relu' }),
          tf.layers.dense({ units: 1, activation: 'sigmoid' }),
        ],
      });

      this.mlModel.compile({
        optimizer: 'adam',
        loss: 'meanSquaredError',
        metrics: ['mae'],
      });

      logger.info('Energy analysis ML model initialized');
    } catch (error) {
      logger.error('Failed to initialize ML model:', error);
    }
  }

  // Record energy data point
  async recordEnergyData(userId: string, data: EnergyDataPoint): Promise<void> {
    try {
      // Store in database
      await prisma.energyData.create({
        data: {
          userId,
          timestamp: data.timestamp,
          energyLevel: data.energyLevel,
          mood: data.mood,
          stressLevel: data.stressLevel,
          sleepQuality: data.sleepQuality,
          physicalActivity: data.physicalActivity,
          caffeine: data.caffeine,
          context: data.context,
        },
      });

      // Invalidate cache to trigger pattern recalculation
      const cacheKey = CacheKeys.energyData(userId, data.timestamp.toISOString().split('T')[0]);
      await cacheService.del(cacheKey);

      // Trigger pattern analysis if we have enough data
      await this.updateCircadianPattern(userId);

      logger.info(`Energy data recorded for user ${userId}`);
    } catch (error) {
      logger.error('Error recording energy data:', error);
      throw error;
    }
  }

  // Get energy data for a specific date range
  async getEnergyData(userId: string, startDate: Date, endDate: Date): Promise<EnergyDataPoint[]> {
    try {
      const cacheKey = CacheKeys.energyData(userId, `${startDate.toISOString()}-${endDate.toISOString()}`);
      let energyData = await cacheService.get<EnergyDataPoint[]>(cacheKey);

      if (!energyData) {
        const data = await prisma.energyData.findMany({
          where: {
            userId,
            timestamp: {
              gte: startDate,
              lte: endDate,
            },
          },
          orderBy: { timestamp: 'asc' },
        });

        energyData = data.map(d => ({
          timestamp: d.timestamp,
          energyLevel: d.energyLevel as EnergyLevel,
          mood: d.mood,
          stressLevel: d.stressLevel,
          sleepQuality: d.sleepQuality || undefined,
          physicalActivity: d.physicalActivity || undefined,
          caffeine: d.caffeine || undefined,
          context: d.context || undefined,
        }));

        // Cache for 5 minutes
        await cacheService.set(cacheKey, energyData, 300);
      }

      return energyData;
    } catch (error) {
      logger.error('Error getting energy data:', error);
      throw error;
    }
  }

  // Analyze and update circadian pattern
  async updateCircadianPattern(userId: string): Promise<CircadianPattern> {
    try {
      // Get last 30 days of energy data
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
      
      const energyData = await this.getEnergyData(userId, startDate, endDate);

      if (energyData.length < 14) {
        throw new Error('Insufficient data for pattern analysis (minimum 14 data points required)');
      }

      // Group data by hour of day
      const hourlyData: { [hour: number]: number[] } = {};
      for (let i = 0; i < 24; i++) {
        hourlyData[i] = [];
      }

      energyData.forEach(point => {
        const hour = point.timestamp.getHours();
        hourlyData[hour].push(point.energyLevel);
      });

      // Calculate average energy for each hour
      const averagePattern: number[] = [];
      for (let i = 0; i < 24; i++) {
        if (hourlyData[i].length > 0) {
          const average = hourlyData[i].reduce((sum, val) => sum + val, 0) / hourlyData[i].length;
          averagePattern.push(average);
        } else {
          averagePattern.push(3); // Default moderate energy
        }
      }

      // Identify peak and low energy hours
      const sortedHours = Array.from({ length: 24 }, (_, i) => i)
        .sort((a, b) => averagePattern[b] - averagePattern[a]);

      const peakEnergyHours = sortedHours.slice(0, 6); // Top 6 hours
      const lowEnergyHours = sortedHours.slice(-6); // Bottom 6 hours

      // Calculate confidence based on data consistency
      const confidence = this.calculatePatternConfidence(hourlyData);

      const pattern: CircadianPattern = {
        userId,
        peakEnergyHours: peakEnergyHours.sort((a, b) => a - b),
        lowEnergyHours: lowEnergyHours.sort((a, b) => a - b),
        averagePattern,
        confidence,
        lastUpdated: new Date(),
      };

      // Store pattern in database
      await prisma.circadianPattern.upsert({
        where: { userId },
        update: {
          peakEnergyHours: pattern.peakEnergyHours,
          lowEnergyHours: pattern.lowEnergyHours,
          averagePattern: pattern.averagePattern,
          confidence: pattern.confidence,
          lastUpdated: pattern.lastUpdated,
        },
        create: {
          userId,
          peakEnergyHours: pattern.peakEnergyHours,
          lowEnergyHours: pattern.lowEnergyHours,
          averagePattern: pattern.averagePattern,
          confidence: pattern.confidence,
          lastUpdated: pattern.lastUpdated,
        },
      });

      // Cache the pattern
      const cacheKey = CacheKeys.analyticsData(userId, 'circadian-pattern');
      await cacheService.set(cacheKey, pattern, 3600); // Cache for 1 hour

      logger.info(`Circadian pattern updated for user ${userId} with confidence ${confidence}`);
      return pattern;
    } catch (error) {
      logger.error('Error updating circadian pattern:', error);
      throw error;
    }
  }

  // Get user's circadian pattern
  async getCircadianPattern(userId: string): Promise<CircadianPattern | null> {
    try {
      const cacheKey = CacheKeys.analyticsData(userId, 'circadian-pattern');
      let pattern = await cacheService.get<CircadianPattern>(cacheKey);

      if (!pattern) {
        const dbPattern = await prisma.circadianPattern.findUnique({
          where: { userId },
        });

        if (dbPattern) {
          pattern = {
            userId: dbPattern.userId,
            peakEnergyHours: dbPattern.peakEnergyHours,
            lowEnergyHours: dbPattern.lowEnergyHours,
            averagePattern: dbPattern.averagePattern,
            confidence: dbPattern.confidence,
            lastUpdated: dbPattern.lastUpdated,
          };

          await cacheService.set(cacheKey, pattern, 3600);
        }
      }

      return pattern;
    } catch (error) {
      logger.error('Error getting circadian pattern:', error);
      throw error;
    }
  }

  // Predict energy level for a specific time
  async predictEnergyLevel(userId: string, targetTime: Date): Promise<EnergyPrediction> {
    try {
      const pattern = await this.getCircadianPattern(userId);
      if (!pattern) {
        throw new Error('No circadian pattern available for prediction');
      }

      const hour = targetTime.getHours();
      const baseEnergyLevel = pattern.averagePattern[hour];

      // Get recent sleep and activity data for context
      const recentData = await this.getEnergyData(
        userId,
        new Date(targetTime.getTime() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
        targetTime
      );

      // Calculate impact factors
      const factors = this.calculatePredictionFactors(recentData, targetTime);

      // Apply ML model if available
      let predictedEnergyLevel = baseEnergyLevel;
      if (this.mlModel && recentData.length > 0) {
        predictedEnergyLevel = await this.applyMLPrediction(recentData, targetTime, baseEnergyLevel);
      }

      // Apply factor adjustments
      predictedEnergyLevel += factors.sleepImpact + factors.activityImpact - factors.stressImpact;
      predictedEnergyLevel = Math.max(1, Math.min(5, predictedEnergyLevel)); // Clamp to 1-5 range

      const prediction: EnergyPrediction = {
        timestamp: targetTime,
        predictedEnergyLevel,
        confidence: pattern.confidence * 0.8, // Reduce confidence for predictions
        factors,
      };

      return prediction;
    } catch (error) {
      logger.error('Error predicting energy level:', error);
      throw error;
    }
  }

  // Calculate pattern confidence based on data consistency
  private calculatePatternConfidence(hourlyData: { [hour: number]: number[] }): number {
    let totalVariance = 0;
    let hoursWithData = 0;

    for (let hour = 0; hour < 24; hour++) {
      const data = hourlyData[hour];
      if (data.length > 1) {
        const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
        const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
        totalVariance += variance;
        hoursWithData++;
      }
    }

    if (hoursWithData === 0) return 0;

    const averageVariance = totalVariance / hoursWithData;
    const confidence = Math.max(0, Math.min(1, 1 - (averageVariance / 4))); // Normalize to 0-1

    return confidence;
  }

  // Calculate prediction factors
  private calculatePredictionFactors(recentData: EnergyDataPoint[], targetTime: Date): any {
    const factors = {
      sleepImpact: 0,
      circadianImpact: 0,
      activityImpact: 0,
      stressImpact: 0,
    };

    if (recentData.length === 0) return factors;

    // Calculate average sleep quality impact
    const sleepData = recentData.filter(d => d.sleepQuality !== undefined);
    if (sleepData.length > 0) {
      const avgSleepQuality = sleepData.reduce((sum, d) => sum + (d.sleepQuality || 0), 0) / sleepData.length;
      factors.sleepImpact = (avgSleepQuality - 5) * 0.2; // Scale to -1 to +1
    }

    // Calculate activity impact
    const activityData = recentData.filter(d => d.physicalActivity !== undefined);
    if (activityData.length > 0) {
      const avgActivity = activityData.reduce((sum, d) => sum + (d.physicalActivity || 0), 0) / activityData.length;
      factors.activityImpact = Math.min(0.5, avgActivity / 60 * 0.3); // Positive impact up to 0.5
    }

    // Calculate stress impact
    const avgStress = recentData.reduce((sum, d) => sum + d.stressLevel, 0) / recentData.length;
    factors.stressImpact = (avgStress - 5) * 0.15; // Negative impact for high stress

    return factors;
  }

  // Apply ML model prediction (placeholder implementation)
  private async applyMLPrediction(
    recentData: EnergyDataPoint[],
    targetTime: Date,
    baseEnergyLevel: number
  ): Promise<number> {
    try {
      if (!this.mlModel) return baseEnergyLevel;

      // Create feature vector (24 hours + additional features)
      const features = new Array(24).fill(0);
      
      // Set circadian features
      const hour = targetTime.getHours();
      features[hour] = 1;

      // Add recent data features (simplified)
      if (recentData.length > 0) {
        const recent = recentData[recentData.length - 1];
        features.push(
          recent.energyLevel / 5,
          recent.mood / 10,
          recent.stressLevel / 10,
          (recent.sleepQuality || 5) / 10
        );
      }

      // Pad or truncate to expected input size
      const inputTensor = tf.tensor2d([features.slice(0, 24)]);
      const prediction = this.mlModel.predict(inputTensor) as tf.Tensor;
      const result = await prediction.data();

      inputTensor.dispose();
      prediction.dispose();

      return result[0] * 5; // Scale back to 1-5 range
    } catch (error) {
      logger.error('Error applying ML prediction:', error);
      return baseEnergyLevel;
    }
  }

  // Get energy insights for user
  async getEnergyInsights(userId: string): Promise<any> {
    try {
      const pattern = await this.getCircadianPattern(userId);
      if (!pattern) {
        return { message: 'Not enough data for insights. Please log energy levels for at least 2 weeks.' };
      }

      const insights = {
        peakEnergyTime: this.formatHours(pattern.peakEnergyHours),
        lowEnergyTime: this.formatHours(pattern.lowEnergyHours),
        confidence: pattern.confidence,
        recommendations: this.generateRecommendations(pattern),
        lastUpdated: pattern.lastUpdated,
      };

      return insights;
    } catch (error) {
      logger.error('Error getting energy insights:', error);
      throw error;
    }
  }

  private formatHours(hours: number[]): string {
    return hours.map(h => `${h}:00`).join(', ');
  }

  private generateRecommendations(pattern: CircadianPattern): string[] {
    const recommendations: string[] = [];

    // Peak energy recommendations
    if (pattern.peakEnergyHours.length > 0) {
      const peakStart = Math.min(...pattern.peakEnergyHours);
      const peakEnd = Math.max(...pattern.peakEnergyHours);
      recommendations.push(
        `Schedule your most demanding tasks between ${peakStart}:00 and ${peakEnd}:00 when your energy is highest.`
      );
    }

    // Low energy recommendations
    if (pattern.lowEnergyHours.length > 0) {
      recommendations.push(
        'Use your low-energy periods for lighter tasks like email, planning, or administrative work.'
      );
    }

    // Confidence-based recommendations
    if (pattern.confidence < 0.6) {
      recommendations.push(
        'Continue logging your energy levels to improve the accuracy of your personalized schedule.'
      );
    }

    return recommendations;
  }
}
