import { Request, Response, NextFunction } from 'express';
import { logger, logError, logSecurityEvent } from '../utils/logger';

// Custom error class
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;

  constructor(message: string, statusCode: number, isOperational = true, code?: string) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Error types
export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, true, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401, true, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, true, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, true, 'NOT_FOUND_ERROR');
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409, true, 'CONFLICT_ERROR');
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, true, 'RATE_LIMIT_ERROR');
    this.name = 'RateLimitError';
  }
}

export class ExternalServiceError extends AppError {
  constructor(service: string, message: string = 'External service error') {
    super(`${service}: ${message}`, 502, true, 'EXTERNAL_SERVICE_ERROR');
    this.name = 'ExternalServiceError';
  }
}

export class DatabaseError extends AppError {
  constructor(message: string = 'Database operation failed') {
    super(message, 500, false, 'DATABASE_ERROR');
    this.name = 'DatabaseError';
  }
}

// Error handler middleware
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let err = error;

  // Convert known errors to AppError
  if (!(err instanceof AppError)) {
    err = convertToAppError(err);
  }

  const appError = err as AppError;

  // Log error
  logError('Request error', appError, {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    body: req.method !== 'GET' ? req.body : undefined,
    query: req.query,
  });

  // Log security events for certain error types
  if (appError instanceof AuthenticationError || appError instanceof AuthorizationError) {
    logSecurityEvent('Authentication/Authorization failure', {
      error: appError.message,
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
    }, 'medium');
  }

  // Send error response
  sendErrorResponse(res, appError, req);
};

// Convert unknown errors to AppError
function convertToAppError(error: Error): AppError {
  // Prisma errors
  if (error.name === 'PrismaClientKnownRequestError') {
    const prismaError = error as any;
    switch (prismaError.code) {
      case 'P2002':
        return new ConflictError('A record with this information already exists');
      case 'P2025':
        return new NotFoundError('Record not found');
      case 'P2003':
        return new ValidationError('Foreign key constraint failed');
      default:
        return new DatabaseError('Database operation failed');
    }
  }

  // JWT errors
  if (error.name === 'JsonWebTokenError') {
    return new AuthenticationError('Invalid token');
  }

  if (error.name === 'TokenExpiredError') {
    return new AuthenticationError('Token expired');
  }

  // Validation errors (express-validator)
  if (error.name === 'ValidationError') {
    return new ValidationError(error.message);
  }

  // MongoDB/Mongoose errors (if using MongoDB)
  if (error.name === 'MongoError' || error.name === 'MongooseError') {
    return new DatabaseError('Database operation failed');
  }

  // Axios errors (external API calls)
  if (error.name === 'AxiosError') {
    const axiosError = error as any;
    const service = axiosError.config?.baseURL || 'External service';
    return new ExternalServiceError(service, axiosError.message);
  }

  // Default to internal server error
  return new AppError(
    process.env.NODE_ENV === 'production' ? 'Internal server error' : error.message,
    500,
    false
  );
}

// Send error response
function sendErrorResponse(res: Response, error: AppError, req: Request): void {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isProduction = process.env.NODE_ENV === 'production';

  // Base error response
  const errorResponse: any = {
    error: error.name || 'Error',
    message: error.message,
    statusCode: error.statusCode,
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    method: req.method,
  };

  // Add error code if available
  if (error.code) {
    errorResponse.code = error.code;
  }

  // Add stack trace in development
  if (isDevelopment && error.stack) {
    errorResponse.stack = error.stack;
  }

  // Add request ID if available
  if (req.headers['x-request-id']) {
    errorResponse.requestId = req.headers['x-request-id'];
  }

  // Don't expose sensitive information in production
  if (isProduction && !error.isOperational) {
    errorResponse.message = 'Internal server error';
    delete errorResponse.stack;
  }

  // Set appropriate status code
  res.status(error.statusCode || 500);

  // Send JSON response
  res.json(errorResponse);
}

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Not found handler
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};

// Unhandled promise rejection handler
export const handleUnhandledRejection = (reason: any, promise: Promise<any>): void => {
  logger.error('Unhandled Promise Rejection:', {
    reason: reason?.message || reason,
    stack: reason?.stack,
    promise: promise.toString(),
  });

  // In production, you might want to gracefully shut down the server
  if (process.env.NODE_ENV === 'production') {
    process.exit(1);
  }
};

// Uncaught exception handler
export const handleUncaughtException = (error: Error): void => {
  logger.error('Uncaught Exception:', {
    message: error.message,
    stack: error.stack,
    name: error.name,
  });

  // Gracefully shut down the server
  process.exit(1);
};

// Validation error formatter
export const formatValidationErrors = (errors: any[]): string => {
  return errors
    .map(error => `${error.param}: ${error.msg}`)
    .join(', ');
};

// Error factory functions
export const createValidationError = (message: string, details?: any): ValidationError => {
  return new ValidationError(message, details);
};

export const createAuthenticationError = (message?: string): AuthenticationError => {
  return new AuthenticationError(message);
};

export const createAuthorizationError = (message?: string): AuthorizationError => {
  return new AuthorizationError(message);
};

export const createNotFoundError = (resource: string): NotFoundError => {
  return new NotFoundError(`${resource} not found`);
};

export const createConflictError = (resource: string): ConflictError => {
  return new ConflictError(`${resource} already exists`);
};

export const createRateLimitError = (): RateLimitError => {
  return new RateLimitError();
};

export const createExternalServiceError = (service: string, message?: string): ExternalServiceError => {
  return new ExternalServiceError(service, message);
};

export const createDatabaseError = (message?: string): DatabaseError => {
  return new DatabaseError(message);
};

// Health check error
export const createHealthCheckError = (component: string, details?: any): AppError => {
  return new AppError(`Health check failed for ${component}`, 503, true, 'HEALTH_CHECK_ERROR');
};

// Rate limiting helper
export const isRateLimited = (error: any): boolean => {
  return error instanceof RateLimitError || error.statusCode === 429;
};

// Check if error should be retried
export const isRetryableError = (error: any): boolean => {
  if (error instanceof AppError) {
    return error.statusCode >= 500 && error.statusCode < 600;
  }
  return false;
};

// Get error severity
export const getErrorSeverity = (error: AppError): 'low' | 'medium' | 'high' | 'critical' => {
  if (error.statusCode >= 500) return 'critical';
  if (error.statusCode === 401 || error.statusCode === 403) return 'high';
  if (error.statusCode >= 400) return 'medium';
  return 'low';
};

export default errorHandler;
