import { prisma } from '../config/database';
import { cacheService, CacheKeys } from '../config/redis';
import { logger } from '../utils/logger';

// Habit interfaces
export interface HabitData {
  id?: string;
  name: string;
  description?: string;
  category: HabitCategory;
  frequency: HabitFrequency;
  targetValue: number;
  unit: string;
  preferredTime?: string;
  reminderEnabled: boolean;
}

export interface HabitCompletion {
  id?: string;
  habitId: string;
  completedAt: Date;
  value: number;
  notes?: string;
  mood?: number;
  difficulty?: number;
}

export interface HabitStats {
  habitId: string;
  currentStreak: number;
  longestStreak: number;
  totalCompletions: number;
  successRate: number;
  averageMood?: number;
  averageDifficulty?: number;
  lastCompletion?: Date;
}

export interface HabitInsight {
  habitId: string;
  habitName: string;
  insight: string;
  recommendation: string;
  priority: 'low' | 'medium' | 'high';
  category: 'streak' | 'timing' | 'difficulty' | 'mood' | 'consistency';
}

export enum HabitCategory {
  PRODUCTIVITY = 'PRODUCTIVITY',
  HEALTH = 'HEALTH',
  LEARNING = 'LEARNING',
  MINDFULNESS = 'MINDFULNESS',
  SOCIAL = 'SOCIAL',
  CREATIVE = 'CREATIVE',
  FINANCIAL = 'FINANCIAL',
}

export enum HabitFrequency {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  CUSTOM = 'CUSTOM',
}

export class HabitFormationService {
  private static instance: HabitFormationService;

  private constructor() {}

  public static getInstance(): HabitFormationService {
    if (!HabitFormationService.instance) {
      HabitFormationService.instance = new HabitFormationService();
    }
    return HabitFormationService.instance;
  }

  // Create a new habit
  async createHabit(userId: string, habitData: HabitData): Promise<any> {
    try {
      const habit = await prisma.habit.create({
        data: {
          userId,
          name: habitData.name,
          description: habitData.description,
          category: habitData.category,
          frequency: habitData.frequency,
          targetValue: habitData.targetValue,
          unit: habitData.unit,
          preferredTime: habitData.preferredTime,
          reminderEnabled: habitData.reminderEnabled,
        },
      });

      // Clear cache
      await cacheService.delPattern(CacheKeys.habitData(userId));

      logger.info(`Habit created for user ${userId}: ${habit.name}`);
      return habit;
    } catch (error) {
      logger.error('Error creating habit:', error);
      throw error;
    }
  }

  // Get user's habits
  async getUserHabits(userId: string, activeOnly: boolean = true): Promise<any[]> {
    try {
      const cacheKey = CacheKeys.habitData(userId) + (activeOnly ? ':active' : ':all');
      let habits = await cacheService.get<any[]>(cacheKey);

      if (!habits) {
        habits = await prisma.habit.findMany({
          where: {
            userId,
            ...(activeOnly && { isActive: true }),
          },
          include: {
            completions: {
              orderBy: { completedAt: 'desc' },
              take: 30, // Last 30 completions
            },
            patterns: true,
          },
          orderBy: { createdAt: 'desc' },
        });

        await cacheService.set(cacheKey, habits, 1800); // Cache for 30 minutes
      }

      return habits;
    } catch (error) {
      logger.error('Error getting user habits:', error);
      throw error;
    }
  }

  // Record habit completion
  async recordHabitCompletion(userId: string, completion: HabitCompletion): Promise<any> {
    try {
      // Verify habit belongs to user
      const habit = await prisma.habit.findFirst({
        where: {
          id: completion.habitId,
          userId,
          isActive: true,
        },
      });

      if (!habit) {
        throw new Error('Habit not found or not active');
      }

      // Check if already completed today (for daily habits)
      if (habit.frequency === HabitFrequency.DAILY) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const existingCompletion = await prisma.habitCompletion.findFirst({
          where: {
            habitId: completion.habitId,
            completedAt: {
              gte: today,
              lt: tomorrow,
            },
          },
        });

        if (existingCompletion) {
          throw new Error('Habit already completed today');
        }
      }

      // Record completion
      const habitCompletion = await prisma.habitCompletion.create({
        data: {
          habitId: completion.habitId,
          completedAt: completion.completedAt,
          value: completion.value,
          notes: completion.notes,
          mood: completion.mood,
          difficulty: completion.difficulty,
        },
      });

      // Update habit statistics
      await this.updateHabitStats(completion.habitId);

      // Clear cache
      await cacheService.delPattern(CacheKeys.habitData(userId));

      logger.info(`Habit completion recorded for user ${userId}, habit ${completion.habitId}`);
      return habitCompletion;
    } catch (error) {
      logger.error('Error recording habit completion:', error);
      throw error;
    }
  }

  // Update habit statistics
  private async updateHabitStats(habitId: string): Promise<void> {
    try {
      const completions = await prisma.habitCompletion.findMany({
        where: { habitId },
        orderBy: { completedAt: 'desc' },
      });

      if (completions.length === 0) return;

      // Calculate current streak
      const currentStreak = this.calculateCurrentStreak(completions);
      
      // Calculate longest streak
      const longestStreak = this.calculateLongestStreak(completions);

      // Update habit
      await prisma.habit.update({
        where: { id: habitId },
        data: {
          streak: currentStreak,
          longestStreak: Math.max(longestStreak, currentStreak),
          totalCompletions: completions.length,
        },
      });

      // Update habit pattern
      await this.updateHabitPattern(habitId);
    } catch (error) {
      logger.error('Error updating habit stats:', error);
      throw error;
    }
  }

  // Calculate current streak
  private calculateCurrentStreak(completions: any[]): number {
    if (completions.length === 0) return 0;

    let streak = 0;
    const today = new Date();
    today.setHours(23, 59, 59, 999);

    // Sort completions by date (most recent first)
    const sortedCompletions = completions.sort(
      (a, b) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime()
    );

    let currentDate = new Date(today);
    
    for (const completion of sortedCompletions) {
      const completionDate = new Date(completion.completedAt);
      completionDate.setHours(0, 0, 0, 0);
      currentDate.setHours(0, 0, 0, 0);

      const daysDiff = Math.floor(
        (currentDate.getTime() - completionDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      if (daysDiff === 0 || daysDiff === 1) {
        streak++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else {
        break;
      }
    }

    return streak;
  }

  // Calculate longest streak
  private calculateLongestStreak(completions: any[]): number {
    if (completions.length === 0) return 0;

    const sortedCompletions = completions.sort(
      (a, b) => new Date(a.completedAt).getTime() - new Date(b.completedAt).getTime()
    );

    let longestStreak = 0;
    let currentStreak = 1;
    let previousDate = new Date(sortedCompletions[0].completedAt);

    for (let i = 1; i < sortedCompletions.length; i++) {
      const currentDate = new Date(sortedCompletions[i].completedAt);
      const daysDiff = Math.floor(
        (currentDate.getTime() - previousDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      if (daysDiff === 1) {
        currentStreak++;
      } else {
        longestStreak = Math.max(longestStreak, currentStreak);
        currentStreak = 1;
      }

      previousDate = currentDate;
    }

    return Math.max(longestStreak, currentStreak);
  }

  // Update habit pattern analysis
  private async updateHabitPattern(habitId: string): Promise<void> {
    try {
      const habit = await prisma.habit.findUnique({
        where: { id: habitId },
        include: {
          completions: {
            orderBy: { completedAt: 'desc' },
            take: 90, // Last 90 days
          },
        },
      });

      if (!habit || habit.completions.length < 7) return; // Need at least a week of data

      const completions = habit.completions;
      
      // Analyze optimal time
      const timeFrequency: { [time: string]: number } = {};
      completions.forEach(completion => {
        const time = completion.completedAt.toTimeString().substring(0, 5);
        timeFrequency[time] = (timeFrequency[time] || 0) + 1;
      });

      const optimalTime = Object.entries(timeFrequency)
        .sort(([,a], [,b]) => b - a)[0]?.[0];

      // Calculate success rate (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const recentCompletions = completions.filter(
        c => new Date(c.completedAt) >= thirtyDaysAgo
      );
      
      const expectedCompletions = habit.frequency === HabitFrequency.DAILY ? 30 : 
                                 habit.frequency === HabitFrequency.WEEKLY ? 4 : 1;
      const successRate = Math.min(1, recentCompletions.length / expectedCompletions);

      // Calculate average mood
      const moodCompletions = completions.filter(c => c.mood !== null);
      const averageMood = moodCompletions.length > 0 
        ? moodCompletions.reduce((sum, c) => sum + (c.mood || 0), 0) / moodCompletions.length
        : null;

      // Streak pattern analysis
      const streakPattern = {
        averageStreak: habit.totalCompletions > 0 ? habit.streak : 0,
        longestStreak: habit.longestStreak,
        streakConsistency: successRate,
      };

      // Upsert habit pattern
      await prisma.habitPattern.upsert({
        where: {
          userId_habitId: {
            userId: habit.userId,
            habitId: habitId,
          },
        },
        update: {
          optimalTime,
          successRate,
          averageMood,
          streakPattern: JSON.stringify(streakPattern),
        },
        create: {
          userId: habit.userId,
          habitId,
          optimalTime,
          successRate,
          averageMood,
          streakPattern: JSON.stringify(streakPattern),
        },
      });
    } catch (error) {
      logger.error('Error updating habit pattern:', error);
      throw error;
    }
  }

  // Get habit statistics
  async getHabitStats(userId: string, habitId: string): Promise<HabitStats> {
    try {
      const habit = await prisma.habit.findFirst({
        where: {
          id: habitId,
          userId,
        },
        include: {
          completions: {
            orderBy: { completedAt: 'desc' },
          },
          patterns: true,
        },
      });

      if (!habit) {
        throw new Error('Habit not found');
      }

      const completions = habit.completions;
      const pattern = habit.patterns[0];

      // Calculate success rate
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const recentCompletions = completions.filter(
        c => new Date(c.completedAt) >= thirtyDaysAgo
      );
      
      const expectedCompletions = habit.frequency === HabitFrequency.DAILY ? 30 : 
                                 habit.frequency === HabitFrequency.WEEKLY ? 4 : 1;
      const successRate = Math.min(1, recentCompletions.length / expectedCompletions);

      // Calculate averages
      const moodCompletions = completions.filter(c => c.mood !== null);
      const difficultyCompletions = completions.filter(c => c.difficulty !== null);

      const stats: HabitStats = {
        habitId,
        currentStreak: habit.streak,
        longestStreak: habit.longestStreak,
        totalCompletions: habit.totalCompletions,
        successRate,
        averageMood: moodCompletions.length > 0 
          ? moodCompletions.reduce((sum, c) => sum + (c.mood || 0), 0) / moodCompletions.length
          : undefined,
        averageDifficulty: difficultyCompletions.length > 0 
          ? difficultyCompletions.reduce((sum, c) => sum + (c.difficulty || 0), 0) / difficultyCompletions.length
          : undefined,
        lastCompletion: completions.length > 0 ? completions[0].completedAt : undefined,
      };

      return stats;
    } catch (error) {
      logger.error('Error getting habit stats:', error);
      throw error;
    }
  }

  // Generate habit insights
  async generateHabitInsights(userId: string): Promise<HabitInsight[]> {
    try {
      const habits = await this.getUserHabits(userId, true);
      const insights: HabitInsight[] = [];

      for (const habit of habits) {
        const stats = await this.getHabitStats(userId, habit.id);
        
        // Streak insights
        if (stats.currentStreak === 0 && stats.totalCompletions > 0) {
          insights.push({
            habitId: habit.id,
            habitName: habit.name,
            insight: 'Your streak has been broken',
            recommendation: 'Start small - commit to just one completion to rebuild momentum',
            priority: 'high',
            category: 'streak',
          });
        } else if (stats.currentStreak >= 7) {
          insights.push({
            habitId: habit.id,
            habitName: habit.name,
            insight: `Great job! You're on a ${stats.currentStreak}-day streak`,
            recommendation: 'Keep the momentum going - consistency is key to habit formation',
            priority: 'low',
            category: 'streak',
          });
        }

        // Success rate insights
        if (stats.successRate < 0.5) {
          insights.push({
            habitId: habit.id,
            habitName: habit.name,
            insight: 'Low completion rate detected',
            recommendation: 'Consider reducing the target or changing the timing',
            priority: 'high',
            category: 'consistency',
          });
        }

        // Difficulty insights
        if (stats.averageDifficulty && stats.averageDifficulty > 7) {
          insights.push({
            habitId: habit.id,
            habitName: habit.name,
            insight: 'This habit seems consistently difficult',
            recommendation: 'Break it down into smaller, more manageable steps',
            priority: 'medium',
            category: 'difficulty',
          });
        }

        // Mood insights
        if (stats.averageMood && stats.averageMood < 5) {
          insights.push({
            habitId: habit.id,
            habitName: habit.name,
            insight: 'Low mood associated with this habit',
            recommendation: 'Consider pairing with a more enjoyable activity or reward',
            priority: 'medium',
            category: 'mood',
          });
        }
      }

      return insights.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });
    } catch (error) {
      logger.error('Error generating habit insights:', error);
      throw error;
    }
  }

  // Update habit
  async updateHabit(userId: string, habitId: string, updates: Partial<HabitData>): Promise<any> {
    try {
      const habit = await prisma.habit.findFirst({
        where: {
          id: habitId,
          userId,
        },
      });

      if (!habit) {
        throw new Error('Habit not found');
      }

      const updatedHabit = await prisma.habit.update({
        where: { id: habitId },
        data: updates,
      });

      // Clear cache
      await cacheService.delPattern(CacheKeys.habitData(userId));

      logger.info(`Habit updated for user ${userId}: ${habitId}`);
      return updatedHabit;
    } catch (error) {
      logger.error('Error updating habit:', error);
      throw error;
    }
  }

  // Delete habit
  async deleteHabit(userId: string, habitId: string): Promise<void> {
    try {
      const habit = await prisma.habit.findFirst({
        where: {
          id: habitId,
          userId,
        },
      });

      if (!habit) {
        throw new Error('Habit not found');
      }

      await prisma.habit.update({
        where: { id: habitId },
        data: { isActive: false },
      });

      // Clear cache
      await cacheService.delPattern(CacheKeys.habitData(userId));

      logger.info(`Habit deactivated for user ${userId}: ${habitId}`);
    } catch (error) {
      logger.error('Error deleting habit:', error);
      throw error;
    }
  }
}
