import { apiClient } from '@/lib/api';
import {
  User,
  UserPreferences,
  EnergyData,
  EnergyDataRequest,
  CircadianPattern,
  EnergyPrediction,
  FlowSession,
  FlowSessionRequest,
  FlowSessionUpdate,
  FlowPattern,
  Habit,
  HabitRequest,
  HabitCompletion,
  HabitCompletionRequest,
  HabitStats,
  Task,
  TaskRequest,
  CoachingSession,
  Integration,
  AvailableIntegration,
  DashboardData,
  ApiResponse,
  PaginationParams,
  DateRangeParams,
} from '@/types/api';

// User API
export const userApi = {
  getProfile: (): Promise<ApiResponse<User>> =>
    apiClient.get('/api/users/profile'),

  updateProfile: (data: Partial<User>): Promise<ApiResponse<User>> =>
    apiClient.put('/api/users/profile', data),

  getPreferences: (): Promise<ApiResponse<UserPreferences>> =>
    apiClient.get('/api/users/preferences'),

  updatePreferences: (data: Partial<UserPreferences>): Promise<ApiResponse<UserPreferences>> =>
    apiClient.put('/api/users/preferences', data),

  getDashboard: (): Promise<ApiResponse<DashboardData>> =>
    apiClient.get('/api/users/dashboard'),

  getStats: (period?: 'week' | 'month' | 'quarter' | 'year'): Promise<ApiResponse<any>> =>
    apiClient.get('/api/users/stats', { params: { period } }),

  completeOnboarding: (): Promise<ApiResponse<{ onboardingCompleted: boolean }>> =>
    apiClient.post('/api/users/onboarding/complete'),

  exportData: (): Promise<ApiResponse<any>> =>
    apiClient.get('/api/users/export'),

  deleteAccount: (): Promise<ApiResponse<void>> =>
    apiClient.delete('/api/users/account'),
};

// Energy API
export const energyApi = {
  recordEnergy: (data: EnergyDataRequest): Promise<ApiResponse<EnergyData>> =>
    apiClient.post('/api/energy/record', data),

  getEnergyData: (params?: DateRangeParams & PaginationParams): Promise<ApiResponse<EnergyData[]>> =>
    apiClient.get('/api/energy', { params }),

  getEnergyById: (id: string): Promise<ApiResponse<EnergyData>> =>
    apiClient.get(`/api/energy/${id}`),

  updateEnergy: (id: string, data: Partial<EnergyDataRequest>): Promise<ApiResponse<EnergyData>> =>
    apiClient.put(`/api/energy/${id}`, data),

  deleteEnergy: (id: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/api/energy/${id}`),

  getCircadianPattern: (): Promise<ApiResponse<CircadianPattern>> =>
    apiClient.get('/api/energy/circadian-pattern'),

  getPredictions: (hours?: number): Promise<ApiResponse<EnergyPrediction[]>> =>
    apiClient.get('/api/energy/predictions', { params: { hours } }),

  getInsights: (): Promise<ApiResponse<any[]>> =>
    apiClient.get('/api/energy/insights'),

  getStats: (period?: 'week' | 'month' | 'quarter'): Promise<ApiResponse<any>> =>
    apiClient.get('/api/energy/stats', { params: { period } }),
};

// Flow API
export const flowApi = {
  startSession: (data: FlowSessionRequest): Promise<ApiResponse<FlowSession>> =>
    apiClient.post('/api/flow/session/start', data),

  endSession: (id: string, data: FlowSessionUpdate): Promise<ApiResponse<FlowSession>> =>
    apiClient.post(`/api/flow/session/${id}/end`, data),

  getSessions: (params?: DateRangeParams & PaginationParams): Promise<ApiResponse<FlowSession[]>> =>
    apiClient.get('/api/flow/sessions', { params }),

  getSessionById: (id: string): Promise<ApiResponse<FlowSession>> =>
    apiClient.get(`/api/flow/sessions/${id}`),

  updateSession: (id: string, data: Partial<FlowSessionUpdate>): Promise<ApiResponse<FlowSession>> =>
    apiClient.put(`/api/flow/sessions/${id}`, data),

  deleteSession: (id: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/api/flow/sessions/${id}`),

  getActiveSession: (): Promise<ApiResponse<FlowSession | null>> =>
    apiClient.get('/api/flow/session/active'),

  getPattern: (): Promise<ApiResponse<FlowPattern>> =>
    apiClient.get('/api/flow/pattern'),

  getInsights: (): Promise<ApiResponse<any[]>> =>
    apiClient.get('/api/flow/insights'),

  getStats: (period?: 'week' | 'month' | 'quarter'): Promise<ApiResponse<any>> =>
    apiClient.get('/api/flow/stats', { params: { period } }),
};

// Habits API
export const habitsApi = {
  createHabit: (data: HabitRequest): Promise<ApiResponse<Habit>> =>
    apiClient.post('/api/habits', data),

  getHabits: (params?: { category?: string; isActive?: boolean } & PaginationParams): Promise<ApiResponse<Habit[]>> =>
    apiClient.get('/api/habits', { params }),

  getHabitById: (id: string): Promise<ApiResponse<Habit>> =>
    apiClient.get(`/api/habits/${id}`),

  updateHabit: (id: string, data: Partial<HabitRequest>): Promise<ApiResponse<Habit>> =>
    apiClient.put(`/api/habits/${id}`, data),

  deleteHabit: (id: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/api/habits/${id}`),

  completeHabit: (data: HabitCompletionRequest): Promise<ApiResponse<HabitCompletion>> =>
    apiClient.post('/api/habits/complete', data),

  getCompletions: (habitId: string, params?: DateRangeParams): Promise<ApiResponse<HabitCompletion[]>> =>
    apiClient.get(`/api/habits/${habitId}/completions`, { params }),

  getStats: (habitId: string): Promise<ApiResponse<HabitStats>> =>
    apiClient.get(`/api/habits/${habitId}/stats`),

  getInsights: (): Promise<ApiResponse<any[]>> =>
    apiClient.get('/api/habits/insights'),

  getOverview: (): Promise<ApiResponse<any>> =>
    apiClient.get('/api/habits/overview'),
};

// Tasks API
export const tasksApi = {
  createTask: (data: TaskRequest): Promise<ApiResponse<Task>> =>
    apiClient.post('/api/tasks', data),

  getTasks: (params?: {
    status?: string;
    priority?: string;
    taskType?: string;
    startDate?: string;
    endDate?: string;
  } & PaginationParams): Promise<ApiResponse<Task[]>> =>
    apiClient.get('/api/tasks', { params }),

  getTaskById: (id: string): Promise<ApiResponse<Task>> =>
    apiClient.get(`/api/tasks/${id}`),

  updateTask: (id: string, data: Partial<TaskRequest>): Promise<ApiResponse<Task>> =>
    apiClient.put(`/api/tasks/${id}`, data),

  deleteTask: (id: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/api/tasks/${id}`),

  rescheduleTask: (id: string, newDateTime: string): Promise<ApiResponse<Task>> =>
    apiClient.post(`/api/tasks/${id}/reschedule`, { newDateTime }),

  getTodaySchedule: (): Promise<ApiResponse<any>> =>
    apiClient.get('/api/tasks/schedule/today'),

  getScheduleRange: (params: DateRangeParams): Promise<ApiResponse<any[]>> =>
    apiClient.get('/api/tasks/schedule/range', { params }),

  analyzeWorkload: (date: string): Promise<ApiResponse<any>> =>
    apiClient.get('/api/tasks/workload/analyze', { params: { date } }),

  getStats: (period?: 'week' | 'month' | 'quarter'): Promise<ApiResponse<any>> =>
    apiClient.get('/api/tasks/stats/overview', { params: { period } }),
};

// Coaching API
export const coachingApi = {
  getDailyCoaching: (): Promise<ApiResponse<CoachingSession>> =>
    apiClient.get('/api/coaching/daily'),

  getWeeklyCoaching: (): Promise<ApiResponse<CoachingSession>> =>
    apiClient.get('/api/coaching/weekly'),

  getInsights: (): Promise<ApiResponse<any[]>> =>
    apiClient.get('/api/coaching/insights'),

  getSessions: (params?: {
    limit?: number;
    sessionType?: string;
  }): Promise<ApiResponse<CoachingSession[]>> =>
    apiClient.get('/api/coaching/sessions', { params }),

  getSessionById: (id: string): Promise<ApiResponse<CoachingSession>> =>
    apiClient.get(`/api/coaching/sessions/${id}`),

  getRecommendations: (params?: {
    category?: string;
    priority?: string;
  }): Promise<ApiResponse<any[]>> =>
    apiClient.get('/api/coaching/recommendations', { params }),

  getMetrics: (period?: 'week' | 'month' | 'quarter'): Promise<ApiResponse<any>> =>
    apiClient.get('/api/coaching/metrics', { params: { period } }),

  getSummary: (): Promise<ApiResponse<any>> =>
    apiClient.get('/api/coaching/summary'),

  getProgress: (params?: {
    metric?: string;
    period?: string;
  }): Promise<ApiResponse<any>> =>
    apiClient.get('/api/coaching/progress', { params }),
};

// Integrations API
export const integrationsApi = {
  getIntegrations: (): Promise<ApiResponse<Integration[]>> =>
    apiClient.get('/api/integrations'),

  getAvailableIntegrations: (): Promise<ApiResponse<AvailableIntegration[]>> =>
    apiClient.get('/api/integrations/available'),

  connectGoogleCalendar: (code: string): Promise<ApiResponse<void>> =>
    apiClient.post('/api/integrations/google-calendar/connect', { code }),

  connectFitbit: (code: string): Promise<ApiResponse<void>> =>
    apiClient.post('/api/integrations/fitbit/connect', { code }),

  disconnectIntegration: (provider: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/api/integrations/${provider}/disconnect`),

  syncIntegration: (provider: string): Promise<ApiResponse<void>> =>
    apiClient.post(`/api/integrations/${provider}/sync`),

  getGoogleCalendarEvents: (params: DateRangeParams): Promise<ApiResponse<any[]>> =>
    apiClient.get('/api/integrations/google-calendar/events', { params }),

  getFitbitActivity: (date: string): Promise<ApiResponse<any>> =>
    apiClient.get('/api/integrations/fitbit/activity', { params: { date } }),

  getFitbitSleep: (date: string): Promise<ApiResponse<any>> =>
    apiClient.get('/api/integrations/fitbit/sleep', { params: { date } }),

  getStatusSummary: (): Promise<ApiResponse<any>> =>
    apiClient.get('/api/integrations/status/summary'),
};

// Health check
export const healthApi = {
  check: (): Promise<{ status: string; timestamp: string }> =>
    apiClient.getClient().get('/health').then(res => res.data),
};
