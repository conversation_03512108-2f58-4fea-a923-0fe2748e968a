import { prisma } from '../config/database';
import { cacheService, Cache<PERSON>eys } from '../config/redis';
import { logger } from '../utils/logger';
import { EnergyAnalysisService } from './energyAnalysisService';
import { FlowDetectionService } from './flowDetectionService';
import { HabitFormationService } from './habitFormationService';
import { SchedulingService } from './schedulingService';

// Coaching interfaces
export interface CoachingInsight {
  id: string;
  type: InsightType;
  title: string;
  message: string;
  data: any;
  priority: 'low' | 'medium' | 'high';
  actionable: boolean;
  recommendations: string[];
  createdAt: Date;
}

export interface CoachingSession {
  id: string;
  userId: string;
  sessionType: CoachingType;
  insights: CoachingInsight[];
  recommendations: Recommendation[];
  goals: Goal[];
  metrics: SessionMetrics;
  createdAt: Date;
}

export interface Recommendation {
  id: string;
  category: 'energy' | 'flow' | 'habits' | 'scheduling' | 'wellness';
  title: string;
  description: string;
  actionSteps: string[];
  expectedImpact: 'low' | 'medium' | 'high';
  timeframe: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface Goal {
  id: string;
  title: string;
  description: string;
  category: string;
  targetValue: number;
  currentValue: number;
  unit: string;
  deadline: Date;
  isActive: boolean;
}

export interface SessionMetrics {
  productivityScore: number;
  wellnessScore: number;
  progressScore: number;
  energyOptimization: number;
  habitConsistency: number;
  flowStateUtilization: number;
}

export enum InsightType {
  ENERGY_PATTERN = 'energy_pattern',
  FLOW_OPTIMIZATION = 'flow_optimization',
  HABIT_PROGRESS = 'habit_progress',
  SCHEDULE_EFFICIENCY = 'schedule_efficiency',
  WELLNESS_ALERT = 'wellness_alert',
  ACHIEVEMENT = 'achievement',
  TREND_ANALYSIS = 'trend_analysis',
}

export enum CoachingType {
  DAILY_CHECKIN = 'DAILY_CHECKIN',
  WEEKLY_REVIEW = 'WEEKLY_REVIEW',
  MONTHLY_ANALYSIS = 'MONTHLY_ANALYSIS',
  GOAL_SETTING = 'GOAL_SETTING',
  HABIT_COACHING = 'HABIT_COACHING',
  ENERGY_OPTIMIZATION = 'ENERGY_OPTIMIZATION',
}

export class CoachingService {
  private static instance: CoachingService;
  private energyService: EnergyAnalysisService;
  private flowService: FlowDetectionService;
  private habitService: HabitFormationService;
  private schedulingService: SchedulingService;

  private constructor() {
    this.energyService = EnergyAnalysisService.getInstance();
    this.flowService = FlowDetectionService.getInstance();
    this.habitService = HabitFormationService.getInstance();
    this.schedulingService = SchedulingService.getInstance();
  }

  public static getInstance(): CoachingService {
    if (!CoachingService.instance) {
      CoachingService.instance = new CoachingService();
    }
    return CoachingService.instance;
  }

  // Generate daily coaching session
  async generateDailyCoaching(userId: string): Promise<CoachingSession> {
    try {
      const insights = await this.generateDailyInsights(userId);
      const recommendations = await this.generateDailyRecommendations(userId, insights);
      const metrics = await this.calculateDailyMetrics(userId);

      const session = await prisma.coachingSession.create({
        data: {
          userId,
          sessionType: CoachingType.DAILY_CHECKIN,
          insights: JSON.stringify(insights),
          recommendations: JSON.stringify(recommendations),
          goals: JSON.stringify([]), // Goals are managed separately
          productivityScore: metrics.productivityScore,
          wellnessScore: metrics.wellnessScore,
          progressScore: metrics.progressScore,
        },
      });

      const coachingSession: CoachingSession = {
        id: session.id,
        userId: session.userId,
        sessionType: session.sessionType as CoachingType,
        insights,
        recommendations,
        goals: [],
        metrics,
        createdAt: session.createdAt,
      };

      // Cache the session
      const cacheKey = CacheKeys.analyticsData(userId, 'daily-coaching');
      await cacheService.set(cacheKey, coachingSession, 86400); // Cache for 24 hours

      logger.info(`Daily coaching session generated for user ${userId}`);
      return coachingSession;
    } catch (error) {
      logger.error('Error generating daily coaching:', error);
      throw error;
    }
  }

  // Generate weekly coaching session
  async generateWeeklyCoaching(userId: string): Promise<CoachingSession> {
    try {
      const insights = await this.generateWeeklyInsights(userId);
      const recommendations = await this.generateWeeklyRecommendations(userId, insights);
      const goals = await this.reviewAndUpdateGoals(userId);
      const metrics = await this.calculateWeeklyMetrics(userId);

      const session = await prisma.coachingSession.create({
        data: {
          userId,
          sessionType: CoachingType.WEEKLY_REVIEW,
          insights: JSON.stringify(insights),
          recommendations: JSON.stringify(recommendations),
          goals: JSON.stringify(goals),
          productivityScore: metrics.productivityScore,
          wellnessScore: metrics.wellnessScore,
          progressScore: metrics.progressScore,
        },
      });

      const coachingSession: CoachingSession = {
        id: session.id,
        userId: session.userId,
        sessionType: session.sessionType as CoachingType,
        insights,
        recommendations,
        goals,
        metrics,
        createdAt: session.createdAt,
      };

      logger.info(`Weekly coaching session generated for user ${userId}`);
      return coachingSession;
    } catch (error) {
      logger.error('Error generating weekly coaching:', error);
      throw error;
    }
  }

  // Generate daily insights
  private async generateDailyInsights(userId: string): Promise<CoachingInsight[]> {
    const insights: CoachingInsight[] = [];
    const today = new Date();

    try {
      // Energy insights
      const energyPrediction = await this.energyService.predictEnergyLevel(userId, today);
      if (energyPrediction.predictedEnergyLevel < 3) {
        insights.push({
          id: `energy-low-${Date.now()}`,
          type: InsightType.ENERGY_PATTERN,
          title: 'Low Energy Predicted',
          message: `Your energy level is predicted to be ${energyPrediction.predictedEnergyLevel.toFixed(1)}/5 today.`,
          data: energyPrediction,
          priority: 'medium',
          actionable: true,
          recommendations: [
            'Consider scheduling lighter tasks for today',
            'Ensure you get adequate sleep tonight',
            'Take regular breaks throughout the day',
          ],
          createdAt: new Date(),
        });
      }

      // Flow state insights
      const flowPrediction = await this.flowService.predictOptimalFlowTime(userId, today);
      if (flowPrediction.probability > 0.7) {
        insights.push({
          id: `flow-optimal-${Date.now()}`,
          type: InsightType.FLOW_OPTIMIZATION,
          title: 'Optimal Flow Time Detected',
          message: `High probability (${(flowPrediction.probability * 100).toFixed(0)}%) of entering flow state today.`,
          data: flowPrediction,
          priority: 'high',
          actionable: true,
          recommendations: [
            'Schedule your most important deep work during this time',
            'Minimize distractions and interruptions',
            'Prepare your optimal work environment',
          ],
          createdAt: new Date(),
        });
      }

      // Habit insights
      const habitInsights = await this.habitService.generateHabitInsights(userId);
      habitInsights.slice(0, 2).forEach(habitInsight => {
        insights.push({
          id: `habit-${habitInsight.habitId}-${Date.now()}`,
          type: InsightType.HABIT_PROGRESS,
          title: `Habit: ${habitInsight.habitName}`,
          message: habitInsight.insight,
          data: habitInsight,
          priority: habitInsight.priority,
          actionable: true,
          recommendations: [habitInsight.recommendation],
          createdAt: new Date(),
        });
      });

      // Schedule efficiency
      const workloadAnalysis = await this.schedulingService.analyzeWorkload(userId, today);
      if (workloadAnalysis.stressLevel > 7) {
        insights.push({
          id: `workload-high-${Date.now()}`,
          type: InsightType.SCHEDULE_EFFICIENCY,
          title: 'High Workload Detected',
          message: `Your stress level is predicted to be ${workloadAnalysis.stressLevel.toFixed(1)}/10 today.`,
          data: workloadAnalysis,
          priority: 'high',
          actionable: true,
          recommendations: workloadAnalysis.suggestedAdjustments,
          createdAt: new Date(),
        });
      }

    } catch (error) {
      logger.error('Error generating daily insights:', error);
    }

    return insights;
  }

  // Generate weekly insights
  private async generateWeeklyInsights(userId: string): Promise<CoachingInsight[]> {
    const insights: CoachingInsight[] = [];
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);

    try {
      // Energy pattern analysis
      const energyPattern = await this.energyService.getCircadianPattern(userId);
      if (energyPattern && energyPattern.confidence > 0.7) {
        insights.push({
          id: `energy-pattern-${Date.now()}`,
          type: InsightType.ENERGY_PATTERN,
          title: 'Strong Energy Pattern Detected',
          message: `Your circadian rhythm is well-established with ${(energyPattern.confidence * 100).toFixed(0)}% confidence.`,
          data: energyPattern,
          priority: 'low',
          actionable: true,
          recommendations: [
            'Continue maintaining consistent sleep schedule',
            'Schedule demanding tasks during peak energy hours',
            'Use low-energy periods for lighter activities',
          ],
          createdAt: new Date(),
        });
      }

      // Flow state utilization
      const flowSessions = await this.flowService.getFlowSessions(userId, startDate, endDate);
      const totalFlowTime = flowSessions.reduce((sum, session) => sum + (session.duration || 0), 0);
      
      if (totalFlowTime > 0) {
        const avgProductivity = flowSessions.reduce((sum, session) => sum + session.productivity, 0) / flowSessions.length;
        
        insights.push({
          id: `flow-utilization-${Date.now()}`,
          type: InsightType.FLOW_OPTIMIZATION,
          title: 'Flow State Analysis',
          message: `You spent ${totalFlowTime} minutes in flow state this week with average productivity of ${avgProductivity.toFixed(1)}/10.`,
          data: { totalFlowTime, avgProductivity, sessionCount: flowSessions.length },
          priority: 'medium',
          actionable: true,
          recommendations: [
            totalFlowTime < 180 ? 'Try to increase your deep work sessions' : 'Great job maintaining focus!',
            avgProductivity < 7 ? 'Consider optimizing your work environment' : 'Your flow sessions are highly productive',
          ],
          createdAt: new Date(),
        });
      }

      // Habit consistency analysis
      const habits = await this.habitService.getUserHabits(userId, true);
      const habitStats = await Promise.all(
        habits.map(habit => this.habitService.getHabitStats(userId, habit.id))
      );

      const avgSuccessRate = habitStats.reduce((sum, stat) => sum + stat.successRate, 0) / habitStats.length;
      
      if (habitStats.length > 0) {
        insights.push({
          id: `habit-consistency-${Date.now()}`,
          type: InsightType.HABIT_PROGRESS,
          title: 'Habit Consistency Review',
          message: `Your average habit success rate this week is ${(avgSuccessRate * 100).toFixed(0)}%.`,
          data: { avgSuccessRate, habitCount: habits.length },
          priority: avgSuccessRate < 0.6 ? 'high' : 'low',
          actionable: true,
          recommendations: [
            avgSuccessRate < 0.6 ? 'Consider reducing habit difficulty or frequency' : 'Excellent habit consistency!',
            'Focus on your most important habits first',
            'Celebrate small wins to maintain motivation',
          ],
          createdAt: new Date(),
        });
      }

    } catch (error) {
      logger.error('Error generating weekly insights:', error);
    }

    return insights;
  }

  // Generate daily recommendations
  private async generateDailyRecommendations(userId: string, insights: CoachingInsight[]): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];

    // Energy-based recommendations
    const energyInsights = insights.filter(i => i.type === InsightType.ENERGY_PATTERN);
    if (energyInsights.length > 0) {
      recommendations.push({
        id: `energy-rec-${Date.now()}`,
        category: 'energy',
        title: 'Optimize Your Energy Today',
        description: 'Align your tasks with your predicted energy levels',
        actionSteps: [
          'Check your energy prediction for today',
          'Schedule demanding tasks during high-energy periods',
          'Plan lighter activities during low-energy times',
          'Take breaks when energy dips',
        ],
        expectedImpact: 'high',
        timeframe: 'Today',
        difficulty: 'easy',
      });
    }

    // Flow-based recommendations
    const flowInsights = insights.filter(i => i.type === InsightType.FLOW_OPTIMIZATION);
    if (flowInsights.length > 0) {
      recommendations.push({
        id: `flow-rec-${Date.now()}`,
        category: 'flow',
        title: 'Maximize Your Flow State',
        description: 'Take advantage of optimal flow conditions',
        actionSteps: [
          'Block time for deep work during optimal hours',
          'Turn off notifications and distractions',
          'Prepare your workspace in advance',
          'Choose your most important task for flow time',
        ],
        expectedImpact: 'high',
        timeframe: 'Today',
        difficulty: 'medium',
      });
    }

    return recommendations;
  }

  // Generate weekly recommendations
  private async generateWeeklyRecommendations(userId: string, insights: CoachingInsight[]): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];

    // Pattern-based recommendations
    recommendations.push({
      id: `pattern-rec-${Date.now()}`,
      category: 'scheduling',
      title: 'Optimize Your Weekly Schedule',
      description: 'Use your energy and flow patterns to plan better',
      actionSteps: [
        'Review your energy patterns from this week',
        'Identify your most productive times',
        'Plan next week\'s important tasks during peak times',
        'Schedule breaks and lighter tasks during low-energy periods',
      ],
      expectedImpact: 'high',
      timeframe: 'Next week',
      difficulty: 'medium',
    });

    // Habit improvement recommendations
    const habitInsights = insights.filter(i => i.type === InsightType.HABIT_PROGRESS);
    if (habitInsights.some(i => i.priority === 'high')) {
      recommendations.push({
        id: `habit-rec-${Date.now()}`,
        category: 'habits',
        title: 'Strengthen Your Habits',
        description: 'Focus on consistency and sustainable progress',
        actionSteps: [
          'Choose one habit to focus on this week',
          'Reduce the difficulty if you\'re struggling',
          'Set up environmental cues for success',
          'Track your progress daily',
        ],
        expectedImpact: 'medium',
        timeframe: 'This week',
        difficulty: 'easy',
      });
    }

    return recommendations;
  }

  // Calculate daily metrics
  private async calculateDailyMetrics(userId: string): Promise<SessionMetrics> {
    const today = new Date();
    
    // Simplified metrics calculation
    const metrics: SessionMetrics = {
      productivityScore: 7.5, // Would be calculated from actual task completion data
      wellnessScore: 8.0, // Would be calculated from energy, stress, and habit data
      progressScore: 7.0, // Would be calculated from goal progress
      energyOptimization: 6.5, // Would be calculated from energy alignment
      habitConsistency: 8.5, // Would be calculated from habit completion rates
      flowStateUtilization: 7.0, // Would be calculated from flow session data
    };

    return metrics;
  }

  // Calculate weekly metrics
  private async calculateWeeklyMetrics(userId: string): Promise<SessionMetrics> {
    // Similar to daily but aggregated over the week
    const metrics: SessionMetrics = {
      productivityScore: 7.8,
      wellnessScore: 7.5,
      progressScore: 7.2,
      energyOptimization: 7.0,
      habitConsistency: 8.0,
      flowStateUtilization: 7.5,
    };

    return metrics;
  }

  // Review and update goals (placeholder)
  private async reviewAndUpdateGoals(userId: string): Promise<Goal[]> {
    // In a real implementation, this would analyze goal progress and suggest updates
    return [];
  }

  // Get recent coaching sessions
  async getRecentCoachingSessions(userId: string, limit: number = 10): Promise<CoachingSession[]> {
    try {
      const sessions = await prisma.coachingSession.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: limit,
      });

      return sessions.map(session => ({
        id: session.id,
        userId: session.userId,
        sessionType: session.sessionType as CoachingType,
        insights: JSON.parse(session.insights),
        recommendations: JSON.parse(session.recommendations),
        goals: JSON.parse(session.goals || '[]'),
        metrics: {
          productivityScore: session.productivityScore || 0,
          wellnessScore: session.wellnessScore || 0,
          progressScore: session.progressScore || 0,
          energyOptimization: 0,
          habitConsistency: 0,
          flowStateUtilization: 0,
        },
        createdAt: session.createdAt,
      }));
    } catch (error) {
      logger.error('Error getting coaching sessions:', error);
      throw error;
    }
  }

  // Get personalized insights
  async getPersonalizedInsights(userId: string): Promise<CoachingInsight[]> {
    try {
      const cacheKey = CacheKeys.analyticsData(userId, 'personalized-insights');
      let insights = await cacheService.get<CoachingInsight[]>(cacheKey);

      if (!insights) {
        insights = await this.generateDailyInsights(userId);
        await cacheService.set(cacheKey, insights, 3600); // Cache for 1 hour
      }

      return insights;
    } catch (error) {
      logger.error('Error getting personalized insights:', error);
      throw error;
    }
  }
}
