'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON>Off, Check, X, Alert<PERSON>ircle, Settings } from 'lucide-react';
import { useNotificationPreferences } from '@/hooks/useNotifications';
import { cn } from '@/lib/utils';

interface NotificationSettingsProps {
  className?: string;
  compact?: boolean;
}

export function NotificationSettings({ className, compact = false }: NotificationSettingsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  const {
    isEnabled,
    permission,
    isSupported,
    canEnable,
    enableNotifications,
    disableNotifications,
    toggleNotifications
  } = useNotificationPreferences();

  const handleToggleNotifications = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const enabled = await toggleNotifications();
      setSuccess(enabled ? 'Notifications enabled!' : 'Notifications disabled');
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to toggle notifications');
    } finally {
      setIsLoading(false);
    }
  };

  const getPermissionStatus = () => {
    switch (permission) {
      case 'granted':
        return { text: 'Granted', color: 'text-green-600 dark:text-green-400' };
      case 'denied':
        return { text: 'Denied', color: 'text-red-600 dark:text-red-400' };
      default:
        return { text: 'Not requested', color: 'text-yellow-600 dark:text-yellow-400' };
    }
  };

  if (!isSupported) {
    return (
      <div className={cn('bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4', className)}>
        <div className="flex items-center space-x-2">
          <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
          <span className="text-sm text-yellow-700 dark:text-yellow-300">
            Notifications are not supported in this browser
          </span>
        </div>
      </div>
    );
  }

  if (compact) {
    return (
      <div className={cn('flex items-center space-x-3', className)}>
        <button
          onClick={handleToggleNotifications}
          disabled={isLoading || !canEnable}
          className={cn(
            'flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm',
            isEnabled
              ? 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-900/30'
              : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700',
            (!canEnable || isLoading) && 'opacity-50 cursor-not-allowed'
          )}
        >
          {isEnabled ? <Bell className="w-4 h-4" /> : <BellOff className="w-4 h-4" />}
          <span>{isLoading ? 'Loading...' : isEnabled ? 'Notifications On' : 'Notifications Off'}</span>
        </button>

        {error && (
          <div className="text-xs text-red-600 dark:text-red-400">
            {error}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn('bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6', className)}>
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
          <Bell className="w-5 h-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Notification Settings
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Manage your session notifications
          </p>
        </div>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <X className="w-4 h-4 text-red-600 dark:text-red-400" />
            <span className="text-sm text-red-700 dark:text-red-300">{error}</span>
          </div>
        </div>
      )}

      {success && (
        <div className="mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
            <span className="text-sm text-green-700 dark:text-green-300">{success}</span>
          </div>
        </div>
      )}

      {/* Current Status */}
      <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Status
            </div>
            <div className={cn('text-sm font-medium', isEnabled ? 'text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-400')}>
              {isEnabled ? 'Enabled' : 'Disabled'}
            </div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Browser Permission
            </div>
            <div className={cn('text-sm font-medium', getPermissionStatus().color)}>
              {getPermissionStatus().text}
            </div>
          </div>
        </div>
      </div>

      {/* Toggle Button */}
      <div className="mb-6">
        <button
          onClick={handleToggleNotifications}
          disabled={isLoading || !canEnable}
          className={cn(
            'w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg transition-colors font-medium',
            isEnabled
              ? 'bg-red-500 hover:bg-red-600 text-white'
              : 'bg-blue-500 hover:bg-blue-600 text-white',
            (!canEnable || isLoading) && 'opacity-50 cursor-not-allowed'
          )}
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
              <span>Loading...</span>
            </>
          ) : isEnabled ? (
            <>
              <BellOff className="w-4 h-4" />
              <span>Disable Notifications</span>
            </>
          ) : (
            <>
              <Bell className="w-4 h-4" />
              <span>Enable Notifications</span>
            </>
          )}
        </button>

        {permission === 'denied' && (
          <p className="mt-2 text-xs text-gray-500 dark:text-gray-400 text-center">
            Notifications are blocked. Please enable them in your browser settings.
          </p>
        )}
      </div>

      {/* Notification Types */}
      {isEnabled && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            You'll receive notifications for:
          </h4>
          <div className="space-y-2">
            {[
              'Session started',
              'Session completed',
              'Break reminders',
              'Milestone achievements',
              'Break time started/ended'
            ].map((type, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Check className="w-4 h-4 text-green-500" />
                <span className="text-sm text-gray-600 dark:text-gray-400">{type}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="mt-6 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <div className="flex items-start space-x-2">
          <AlertCircle className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5" />
          <div className="text-xs text-blue-700 dark:text-blue-300">
            <p className="font-medium mb-1">About Notifications</p>
            <p>
              Notifications help you stay focused by alerting you to session milestones, 
              break reminders, and completion achievements. They work best when RhythmAI 
              is running in a background tab.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// Quick notification toggle for headers/toolbars
export function NotificationToggle({ className }: { className?: string }) {
  const { isEnabled, toggleNotifications, isSupported } = useNotificationPreferences();
  const [isLoading, setIsLoading] = useState(false);

  if (!isSupported) return null;

  const handleToggle = async () => {
    setIsLoading(true);
    try {
      await toggleNotifications();
    } catch (error) {
      console.error('Failed to toggle notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleToggle}
      disabled={isLoading}
      className={cn(
        'p-2 rounded-lg transition-colors',
        isEnabled
          ? 'text-blue-600 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/20'
          : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700',
        isLoading && 'opacity-50 cursor-not-allowed',
        className
      )}
      title={isEnabled ? 'Notifications enabled' : 'Notifications disabled'}
    >
      {isLoading ? (
        <div className="w-5 h-5 animate-spin rounded-full border-2 border-current border-t-transparent" />
      ) : isEnabled ? (
        <Bell className="w-5 h-5" />
      ) : (
        <BellOff className="w-5 h-5" />
      )}
    </button>
  );
}
