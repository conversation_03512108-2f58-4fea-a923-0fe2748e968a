import { Router, Request, Response } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { SchedulingService } from '../services/schedulingService';
import { prisma } from '../config/database';
import { logger } from '../utils/logger';

const router = Router();
const schedulingService = SchedulingService.getInstance();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// POST /api/tasks - Create a new task
router.post('/',
  [
    body('title').isString().trim().isLength({ min: 1, max: 200 }).withMessage('Task title is required and must be less than 200 characters'),
    body('description').optional().isString().trim().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
    body('taskType').optional().isIn(['WORK', 'PERSONAL', 'HEALTH', 'LEARNING', 'CREATIVE', 'ADMINISTRATIVE', 'SOCIAL']).withMessage('Invalid task type'),
    body('priority').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).withMessage('Invalid priority level'),
    body('estimatedDuration').optional().isInt({ min: 1 }).withMessage('Estimated duration must be a positive integer'),
    body('energyRequired').optional().isInt({ min: 1, max: 5 }).withMessage('Energy required must be between 1 and 5'),
    body('focusRequired').optional().isBoolean().withMessage('Focus required must be a boolean'),
    body('dueDate').optional().isISO8601().withMessage('Due date must be a valid ISO 8601 date'),
    body('scheduledAt').optional().isISO8601().withMessage('Scheduled at must be a valid ISO 8601 date'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const taskData = req.body;

      const task = await prisma.task.create({
        data: {
          userId,
          title: taskData.title,
          description: taskData.description,
          taskType: taskData.taskType || 'WORK',
          priority: taskData.priority || 'MEDIUM',
          estimatedDuration: taskData.estimatedDuration,
          energyRequired: taskData.energyRequired || 3,
          focusRequired: taskData.focusRequired || false,
          dueDate: taskData.dueDate ? new Date(taskData.dueDate) : null,
          scheduledAt: taskData.scheduledAt ? new Date(taskData.scheduledAt) : null,
          status: 'PENDING',
        },
      });

      res.status(201).json({
        message: 'Task created successfully',
        data: task,
      });

      logger.info(`Task created for user ${userId}: ${task.title}`);
    } catch (error) {
      logger.error('Error creating task:', error);
      res.status(500).json({
        error: 'Failed to create task',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/tasks - Get user's tasks
router.get('/',
  [
    query('status').optional().isIn(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'RESCHEDULED']).withMessage('Invalid status'),
    query('priority').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).withMessage('Invalid priority'),
    query('taskType').optional().isIn(['WORK', 'PERSONAL', 'HEALTH', 'LEARNING', 'CREATIVE', 'ADMINISTRATIVE', 'SOCIAL']).withMessage('Invalid task type'),
    query('startDate').optional().isISO8601().withMessage('Start date must be a valid ISO 8601 date'),
    query('endDate').optional().isISO8601().withMessage('End date must be a valid ISO 8601 date'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { status, priority, taskType, startDate, endDate, limit = '50', offset = '0' } = req.query;

      const whereClause: any = { userId };

      // Apply filters
      if (status) whereClause.status = status;
      if (priority) whereClause.priority = priority;
      if (taskType) whereClause.taskType = taskType;

      // Date range filter
      if (startDate || endDate) {
        whereClause.OR = [
          {
            scheduledAt: {
              ...(startDate && { gte: new Date(startDate as string) }),
              ...(endDate && { lte: new Date(endDate as string) }),
            },
          },
          {
            dueDate: {
              ...(startDate && { gte: new Date(startDate as string) }),
              ...(endDate && { lte: new Date(endDate as string) }),
            },
          },
        ];
      }

      const tasks = await prisma.task.findMany({
        where: whereClause,
        orderBy: [
          { priority: 'desc' },
          { dueDate: 'asc' },
          { createdAt: 'desc' },
        ],
        take: parseInt(limit as string),
        skip: parseInt(offset as string),
      });

      const totalCount = await prisma.task.count({ where: whereClause });

      res.json({
        data: tasks,
        count: tasks.length,
        totalCount,
        hasMore: parseInt(offset as string) + tasks.length < totalCount,
      });
    } catch (error) {
      logger.error('Error getting tasks:', error);
      res.status(500).json({
        error: 'Failed to get tasks',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/tasks/:taskId - Get specific task
router.get('/:taskId',
  [
    param('taskId').isString().isLength({ min: 1 }).withMessage('Task ID is required'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { taskId } = req.params;

      const task = await prisma.task.findFirst({
        where: {
          id: taskId,
          userId,
        },
      });

      if (!task) {
        return res.status(404).json({
          error: 'Task not found',
          message: 'Task not found or does not belong to user',
        });
      }

      res.json({
        data: task,
      });
    } catch (error) {
      logger.error('Error getting task:', error);
      res.status(500).json({
        error: 'Failed to get task',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// PUT /api/tasks/:taskId - Update task
router.put('/:taskId',
  [
    param('taskId').isString().isLength({ min: 1 }).withMessage('Task ID is required'),
    body('title').optional().isString().trim().isLength({ min: 1, max: 200 }).withMessage('Task title must be less than 200 characters'),
    body('description').optional().isString().trim().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
    body('taskType').optional().isIn(['WORK', 'PERSONAL', 'HEALTH', 'LEARNING', 'CREATIVE', 'ADMINISTRATIVE', 'SOCIAL']).withMessage('Invalid task type'),
    body('priority').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).withMessage('Invalid priority level'),
    body('status').optional().isIn(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'RESCHEDULED']).withMessage('Invalid status'),
    body('estimatedDuration').optional().isInt({ min: 1 }).withMessage('Estimated duration must be a positive integer'),
    body('actualDuration').optional().isInt({ min: 1 }).withMessage('Actual duration must be a positive integer'),
    body('energyRequired').optional().isInt({ min: 1, max: 5 }).withMessage('Energy required must be between 1 and 5'),
    body('focusRequired').optional().isBoolean().withMessage('Focus required must be a boolean'),
    body('dueDate').optional().isISO8601().withMessage('Due date must be a valid ISO 8601 date'),
    body('scheduledAt').optional().isISO8601().withMessage('Scheduled at must be a valid ISO 8601 date'),
    body('productivity').optional().isInt({ min: 1, max: 10 }).withMessage('Productivity must be between 1 and 10'),
    body('satisfaction').optional().isInt({ min: 1, max: 10 }).withMessage('Satisfaction must be between 1 and 10'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { taskId } = req.params;
      const updates = req.body;

      // Verify task belongs to user
      const existingTask = await prisma.task.findFirst({
        where: {
          id: taskId,
          userId,
        },
      });

      if (!existingTask) {
        return res.status(404).json({
          error: 'Task not found',
          message: 'Task not found or does not belong to user',
        });
      }

      // Handle completion
      if (updates.status === 'COMPLETED' && existingTask.status !== 'COMPLETED') {
        updates.completedAt = new Date();
      }

      // Convert date strings to Date objects
      if (updates.dueDate) updates.dueDate = new Date(updates.dueDate);
      if (updates.scheduledAt) updates.scheduledAt = new Date(updates.scheduledAt);

      const updatedTask = await prisma.task.update({
        where: { id: taskId },
        data: updates,
      });

      res.json({
        message: 'Task updated successfully',
        data: updatedTask,
      });

      logger.info(`Task updated for user ${userId}: ${taskId}`);
    } catch (error) {
      logger.error('Error updating task:', error);
      res.status(500).json({
        error: 'Failed to update task',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// DELETE /api/tasks/:taskId - Delete task
router.delete('/:taskId',
  [
    param('taskId').isString().isLength({ min: 1 }).withMessage('Task ID is required'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { taskId } = req.params;

      // Verify task belongs to user
      const task = await prisma.task.findFirst({
        where: {
          id: taskId,
          userId,
        },
      });

      if (!task) {
        return res.status(404).json({
          error: 'Task not found',
          message: 'Task not found or does not belong to user',
        });
      }

      await prisma.task.delete({
        where: { id: taskId },
      });

      res.json({
        message: 'Task deleted successfully',
      });

      logger.info(`Task deleted for user ${userId}: ${taskId}`);
    } catch (error) {
      logger.error('Error deleting task:', error);
      res.status(500).json({
        error: 'Failed to delete task',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// POST /api/tasks/:taskId/reschedule - Reschedule task
router.post('/:taskId/reschedule',
  [
    param('taskId').isString().isLength({ min: 1 }).withMessage('Task ID is required'),
    body('newDateTime').isISO8601().withMessage('New date time must be a valid ISO 8601 date'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { taskId } = req.params;
      const { newDateTime } = req.body;

      const updatedTask = await schedulingService.rescheduleTask(userId, taskId, new Date(newDateTime));

      res.json({
        message: 'Task rescheduled successfully',
        data: updatedTask,
      });

      logger.info(`Task rescheduled for user ${userId}: ${taskId}`);
    } catch (error) {
      logger.error('Error rescheduling task:', error);
      res.status(500).json({
        error: 'Failed to reschedule task',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/tasks/schedule/today - Get today's schedule
router.get('/schedule/today', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const today = new Date();

    const schedule = await schedulingService.generateOptimizedSchedule(userId, today);

    res.json({
      data: schedule,
    });
  } catch (error) {
    logger.error('Error getting today\'s schedule:', error);
    res.status(500).json({
      error: 'Failed to get today\'s schedule',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/tasks/schedule/range - Get schedule for date range
router.get('/schedule/range',
  [
    query('startDate').isISO8601().withMessage('Start date must be a valid ISO 8601 date'),
    query('endDate').isISO8601().withMessage('End date must be a valid ISO 8601 date'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { startDate, endDate } = req.query;

      const start = new Date(startDate as string);
      const end = new Date(endDate as string);

      // Limit to 30 days
      const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
      if (daysDiff > 30) {
        return res.status(400).json({
          error: 'Date range too large',
          message: 'Maximum date range is 30 days',
        });
      }

      const schedules = await schedulingService.getSchedule(userId, start, end);

      res.json({
        data: schedules,
        count: schedules.length,
        dateRange: {
          startDate: start,
          endDate: end,
        },
      });
    } catch (error) {
      logger.error('Error getting schedule range:', error);
      res.status(500).json({
        error: 'Failed to get schedule range',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/tasks/workload/analyze - Analyze workload for a date
router.get('/workload/analyze',
  [
    query('date').isISO8601().withMessage('Date must be a valid ISO 8601 date'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { date } = req.query;

      const targetDate = new Date(date as string);
      const workloadAnalysis = await schedulingService.analyzeWorkload(userId, targetDate);

      res.json({
        data: workloadAnalysis,
      });
    } catch (error) {
      logger.error('Error analyzing workload:', error);
      res.status(500).json({
        error: 'Failed to analyze workload',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/tasks/stats - Get task statistics
router.get('/stats/overview',
  [
    query('period').optional().isIn(['week', 'month', 'quarter']).withMessage('Period must be week, month, or quarter'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const period = (req.query.period as string) || 'month';

      const endDate = new Date();
      const startDate = new Date();

      switch (period) {
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(startDate.getMonth() - 3);
          break;
      }

      const tasks = await prisma.task.findMany({
        where: {
          userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Calculate statistics
      const totalTasks = tasks.length;
      const completedTasks = tasks.filter(t => t.status === 'COMPLETED').length;
      const pendingTasks = tasks.filter(t => t.status === 'PENDING').length;
      const inProgressTasks = tasks.filter(t => t.status === 'IN_PROGRESS').length;
      const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

      // Average productivity and satisfaction
      const completedWithRatings = tasks.filter(t => t.status === 'COMPLETED' && t.productivity);
      const avgProductivity = completedWithRatings.length > 0
        ? completedWithRatings.reduce((sum, t) => sum + (t.productivity || 0), 0) / completedWithRatings.length
        : 0;

      const avgSatisfaction = completedWithRatings.length > 0
        ? completedWithRatings.reduce((sum, t) => sum + (t.satisfaction || 0), 0) / completedWithRatings.length
        : 0;

      // Priority breakdown
      const priorityBreakdown = {
        LOW: tasks.filter(t => t.priority === 'LOW').length,
        MEDIUM: tasks.filter(t => t.priority === 'MEDIUM').length,
        HIGH: tasks.filter(t => t.priority === 'HIGH').length,
        URGENT: tasks.filter(t => t.priority === 'URGENT').length,
      };

      // Task type breakdown
      const typeBreakdown: { [key: string]: number } = {};
      tasks.forEach(task => {
        typeBreakdown[task.taskType] = (typeBreakdown[task.taskType] || 0) + 1;
      });

      const stats = {
        period,
        totalTasks,
        completedTasks,
        pendingTasks,
        inProgressTasks,
        completionRate: Math.round(completionRate * 100) / 100,
        avgProductivity: Math.round(avgProductivity * 100) / 100,
        avgSatisfaction: Math.round(avgSatisfaction * 100) / 100,
        priorityBreakdown,
        typeBreakdown,
      };

      res.json({
        data: stats,
      });
    } catch (error) {
      logger.error('Error getting task statistics:', error);
      res.status(500).json({
        error: 'Failed to get task statistics',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

export default router;
