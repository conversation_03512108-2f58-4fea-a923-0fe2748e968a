'use client';

import React, { useState, useEffect } from 'react';
import { X, Keyboard, Command } from 'lucide-react';
import { ShortcutGroup, formatShortcut } from '@/hooks/useKeyboardShortcuts';
import { cn } from '@/lib/utils';

interface ShortcutsModalProps {
  shortcutGroups: ShortcutGroup[];
  isOpen: boolean;
  onClose: () => void;
}

export function ShortcutsModal({ shortcutGroups, isOpen, onClose }: ShortcutsModalProps) {
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 w-full max-w-2xl max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <Keyboard className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Keyboard Shortcuts
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          <div className="space-y-6">
            {shortcutGroups.map((group, groupIndex) => (
              <div key={groupIndex}>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                  {group.name}
                </h3>
                <div className="space-y-2">
                  {group.shortcuts
                    .filter(shortcut => shortcut.enabled !== false)
                    .map((shortcut, shortcutIndex) => (
                    <div
                      key={shortcutIndex}
                      className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                    >
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        {shortcut.description}
                      </span>
                      <div className="flex items-center space-x-1">
                        {formatShortcut(shortcut).split(' + ').map((key, keyIndex) => (
                          <React.Fragment key={keyIndex}>
                            {keyIndex > 0 && (
                              <span className="text-xs text-gray-400 mx-1">+</span>
                            )}
                            <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 dark:text-gray-200 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded shadow-sm">
                              {key}
                            </kbd>
                          </React.Fragment>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Help Text */}
          <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="flex items-start space-x-2">
              <Command className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5" />
              <div className="text-sm text-blue-700 dark:text-blue-300">
                <p className="font-medium mb-1">Tips:</p>
                <ul className="space-y-1 text-xs">
                  <li>• Shortcuts work when you're not typing in input fields</li>
                  <li>• Press <kbd className="px-1 py-0.5 bg-blue-100 dark:bg-blue-800 rounded text-xs">Ctrl + /</kbd> to show this dialog anytime</li>
                  <li>• Press <kbd className="px-1 py-0.5 bg-blue-100 dark:bg-blue-800 rounded text-xs">Esc</kbd> to close dialogs and cancel actions</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}

// Global shortcuts modal manager
export function GlobalShortcutsModal({ shortcutGroups }: { shortcutGroups: ShortcutGroup[] }) {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const handleShowModal = () => setIsOpen(true);
    
    window.addEventListener('show-shortcuts-modal', handleShowModal);
    
    return () => {
      window.removeEventListener('show-shortcuts-modal', handleShowModal);
    };
  }, []);

  return (
    <ShortcutsModal
      shortcutGroups={shortcutGroups}
      isOpen={isOpen}
      onClose={() => setIsOpen(false)}
    />
  );
}

// Keyboard shortcut indicator component
export function ShortcutIndicator({ 
  shortcut, 
  className 
}: { 
  shortcut: string; 
  className?: string; 
}) {
  return (
    <div className={cn('flex items-center space-x-1', className)}>
      {shortcut.split(' + ').map((key, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <span className="text-xs text-gray-400">+</span>
          )}
          <kbd className="px-1.5 py-0.5 text-xs font-semibold text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded shadow-sm">
            {key}
          </kbd>
        </React.Fragment>
      ))}
    </div>
  );
}

// Button with keyboard shortcut indicator
export function ButtonWithShortcut({
  children,
  shortcut,
  onClick,
  disabled,
  className,
  variant = 'primary'
}: {
  children: React.ReactNode;
  shortcut?: string;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline';
}) {
  const baseClasses = 'flex items-center justify-between px-4 py-2 rounded-lg transition-colors font-medium';
  const variantClasses = {
    primary: 'bg-blue-500 hover:bg-blue-600 text-white disabled:opacity-50',
    secondary: 'bg-gray-500 hover:bg-gray-600 text-white disabled:opacity-50',
    outline: 'border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 disabled:opacity-50'
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(baseClasses, variantClasses[variant], className)}
    >
      <span>{children}</span>
      {shortcut && (
        <ShortcutIndicator shortcut={shortcut} className="ml-3" />
      )}
    </button>
  );
}
