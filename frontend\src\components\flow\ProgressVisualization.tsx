'use client';

import React from 'react';
import { Clock, Target, TrendingUp, Award, CheckCircle } from 'lucide-react';
import { useFlowSessionManager } from '@/hooks/useFlowSession';
import { SessionProgress } from '@/types/flow-session';
import { cn, formatDuration } from '@/lib/utils';

interface ProgressVisualizationProps {
  className?: string;
  variant?: 'circular' | 'linear' | 'detailed';
  showMilestones?: boolean;
  showStats?: boolean;
}

export function ProgressVisualization({ 
  className, 
  variant = 'detailed',
  showMilestones = true,
  showStats = true 
}: ProgressVisualizationProps) {
  const { progress, currentSession, sessionState } = useFlowSessionManager();

  if (!currentSession || sessionState === 'idle') {
    return (
      <div className={cn('flex items-center justify-center p-8 text-center', className)}>
        <div className="space-y-4">
          <div className="w-16 h-16 mx-auto rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
            <Target className="w-8 h-8 text-gray-400" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">No Active Session</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">Start a session to see progress</p>
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'circular') {
    return <CircularProgress progress={progress} className={className} />;
  }

  if (variant === 'linear') {
    return <LinearProgress progress={progress} className={className} />;
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Main Progress Display */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Session Progress</h3>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {Math.round(progress.percentage)}% Complete
          </div>
        </div>

        {/* Circular Progress */}
        <div className="flex items-center justify-center mb-6">
          <CircularProgress progress={progress} size="lg" />
        </div>

        {/* Time Stats */}
        {showStats && (
          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {formatTime(progress.elapsedTime)}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Elapsed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {formatTime(progress.remainingTime)}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Remaining</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {formatTime(progress.totalTime)}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Total</div>
            </div>
          </div>
        )}

        {/* Linear Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
            <span>Progress</span>
            <span>{Math.round(progress.percentage)}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div
              className={cn('h-3 rounded-full transition-all duration-500', {
                'bg-gradient-to-r from-green-400 to-green-500': progress.percentage < 50,
                'bg-gradient-to-r from-yellow-400 to-orange-500': progress.percentage >= 50 && progress.percentage < 75,
                'bg-gradient-to-r from-orange-400 to-red-500': progress.percentage >= 75 && progress.percentage < 90,
                'bg-gradient-to-r from-red-400 to-red-600': progress.percentage >= 90
              })}
              style={{ width: `${progress.percentage}%` }}
            />
          </div>
        </div>
      </div>

      {/* Milestones */}
      {showMilestones && progress.milestones.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Milestones</h4>
          <div className="space-y-3">
            {progress.milestones.map((milestone) => (
              <div
                key={milestone.id}
                className={cn('flex items-center space-x-3 p-3 rounded-lg transition-all duration-300', {
                  'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800': milestone.reached,
                  'bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700': !milestone.reached
                })}
              >
                <div className={cn('w-8 h-8 rounded-full flex items-center justify-center', {
                  'bg-green-500 text-white': milestone.reached,
                  'bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400': !milestone.reached
                })}>
                  {milestone.reached ? (
                    <CheckCircle className="w-4 h-4" />
                  ) : (
                    <span className="text-xs font-bold">{milestone.percentage}%</span>
                  )}
                </div>
                
                <div className="flex-1">
                  <div className={cn('font-medium', {
                    'text-green-700 dark:text-green-300': milestone.reached,
                    'text-gray-700 dark:text-gray-300': !milestone.reached
                  })}>
                    {milestone.name}
                  </div>
                  {milestone.reached && milestone.timestamp && (
                    <div className="text-xs text-green-600 dark:text-green-400">
                      Reached at {new Date(milestone.timestamp).toLocaleTimeString()}
                    </div>
                  )}
                </div>

                {milestone.reached && (
                  <Award className="w-5 h-5 text-green-500" />
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Circular Progress Component
function CircularProgress({ 
  progress, 
  size = 'md', 
  className 
}: { 
  progress: SessionProgress; 
  size?: 'sm' | 'md' | 'lg'; 
  className?: string; 
}) {
  const sizeConfig = {
    sm: { radius: 40, strokeWidth: 4, textSize: 'text-sm' },
    md: { radius: 60, strokeWidth: 6, textSize: 'text-lg' },
    lg: { radius: 80, strokeWidth: 8, textSize: 'text-2xl' }
  };

  const config = sizeConfig[size];
  const circumference = 2 * Math.PI * config.radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (progress.percentage / 100) * circumference;

  const getProgressColor = () => {
    if (progress.percentage >= 90) return '#ef4444'; // red
    if (progress.percentage >= 75) return '#f97316'; // orange
    if (progress.percentage >= 50) return '#eab308'; // yellow
    return '#22c55e'; // green
  };

  return (
    <div className={cn('relative inline-flex items-center justify-center', className)}>
      <svg
        width={config.radius * 2 + config.strokeWidth * 2}
        height={config.radius * 2 + config.strokeWidth * 2}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={config.radius + config.strokeWidth}
          cy={config.radius + config.strokeWidth}
          r={config.radius}
          stroke="currentColor"
          strokeWidth={config.strokeWidth}
          fill="none"
          className="text-gray-200 dark:text-gray-700"
        />
        
        {/* Progress circle */}
        <circle
          cx={config.radius + config.strokeWidth}
          cy={config.radius + config.strokeWidth}
          r={config.radius}
          stroke={getProgressColor()}
          strokeWidth={config.strokeWidth}
          fill="none"
          strokeLinecap="round"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className="transition-all duration-500 ease-in-out"
        />
      </svg>
      
      {/* Center content */}
      <div className="absolute inset-0 flex flex-col items-center justify-center">
        <div className={cn('font-bold text-gray-900 dark:text-white', config.textSize)}>
          {Math.round(progress.percentage)}%
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {formatTime(progress.elapsedTime)}
        </div>
      </div>
    </div>
  );
}

// Linear Progress Component
function LinearProgress({ 
  progress, 
  className 
}: { 
  progress: SessionProgress; 
  className?: string; 
}) {
  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex justify-between items-center">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Session Progress
        </span>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {Math.round(progress.percentage)}%
        </span>
      </div>
      
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4">
        <div
          className="h-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-500 ease-out"
          style={{ width: `${progress.percentage}%` }}
        />
      </div>
      
      <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
        <span>{formatTime(progress.elapsedTime)} elapsed</span>
        <span>{formatTime(progress.remainingTime)} remaining</span>
      </div>
    </div>
  );
}

// Helper function to format time
function formatTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
}
