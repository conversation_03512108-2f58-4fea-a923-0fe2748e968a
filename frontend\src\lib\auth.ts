import { api<PERSON><PERSON>, TokenManager } from './api';
import {
  User,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  RefreshTokenRequest,
} from '@/types/api';

export class AuthService {
  // Login user
  static async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/api/auth/login', credentials);
    
    if (response.data.tokens) {
      TokenManager.setTokens(
        response.data.tokens.accessToken,
        response.data.tokens.refreshToken
      );
    }
    
    return response.data;
  }

  // Register user
  static async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/api/auth/register', userData);
    
    if (response.data.tokens) {
      TokenManager.setTokens(
        response.data.tokens.accessToken,
        response.data.tokens.refreshToken
      );
    }
    
    return response.data;
  }

  // Logout user
  static async logout(): Promise<void> {
    try {
      await apiClient.post('/api/auth/logout');
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      TokenManager.clearTokens();
    }
  }

  // Refresh token
  static async refreshToken(): Promise<{ accessToken: string }> {
    const refreshToken = TokenManager.getRefreshToken();
    
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await apiClient.post<{ accessToken: string }>('/api/auth/refresh', {
      refreshToken,
    });

    TokenManager.setTokens(response.data.accessToken, refreshToken);
    
    return response.data;
  }

  // Get current user
  static async getCurrentUser(): Promise<User> {
    const response = await apiClient.get<User>('/api/auth/me');
    return response.data;
  }

  // Change password
  static async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    await apiClient.post('/api/auth/change-password', {
      currentPassword,
      newPassword,
    });
  }

  // Request password reset
  static async requestPasswordReset(email: string): Promise<void> {
    await apiClient.post('/api/auth/forgot-password', { email });
  }

  // Reset password with token
  static async resetPassword(token: string, password: string): Promise<void> {
    await apiClient.post('/api/auth/reset-password', {
      token,
      password,
    });
  }

  // Check if user is authenticated
  static isAuthenticated(): boolean {
    const token = TokenManager.getAccessToken();
    return token !== null && !TokenManager.isTokenExpired(token);
  }

  // Get user role from token
  static getUserRole(): string | null {
    const token = TokenManager.getAccessToken();
    
    if (!token || TokenManager.isTokenExpired(token)) {
      return null;
    }

    try {
      const payload = JSON.parse(atob(token.split('.')[1]!));
      return payload.role || null;
    } catch {
      return null;
    }
  }

  // Get user ID from token
  static getUserId(): string | null {
    const token = TokenManager.getAccessToken();
    
    if (!token || TokenManager.isTokenExpired(token)) {
      return null;
    }

    try {
      const payload = JSON.parse(atob(token.split('.')[1]!));
      return payload.userId || payload.sub || null;
    } catch {
      return null;
    }
  }

  // Check if user has specific role
  static hasRole(role: string): boolean {
    const userRole = this.getUserRole();
    return userRole === role;
  }

  // Check if user is admin
  static isAdmin(): boolean {
    return this.hasRole('ADMIN');
  }
}
