import { prisma } from '../config/database';
import { cacheService, CacheKeys } from '../config/redis';
import { logger } from '../utils/logger';
import { EnergyAnalysisService } from './energyAnalysisService';
import { FlowDetectionService } from './flowDetectionService';

// Scheduling interfaces
export interface TaskScheduleRequest {
  taskId: string;
  preferredDate?: Date;
  deadline?: Date;
  flexibility: 'rigid' | 'flexible' | 'very_flexible';
}

export interface ScheduleSlot {
  startTime: Date;
  endTime: Date;
  energyLevel: number;
  flowProbability: number;
  availability: 'free' | 'busy' | 'tentative';
  conflictLevel: number;
}

export interface OptimizedSchedule {
  date: Date;
  slots: ScheduledTask[];
  energyAlignment: number;
  flowAlignment: number;
  workloadBalance: number;
  overallScore: number;
}

export interface ScheduledTask {
  taskId: string;
  title: string;
  startTime: Date;
  endTime: Date;
  energyRequired: number;
  predictedEnergyLevel: number;
  flowRequired: boolean;
  flowProbability: number;
  priority: string;
  confidence: number;
}

export interface WorkloadAnalysis {
  date: Date;
  totalWorkload: number;
  energyDemand: number;
  availableEnergy: number;
  stressLevel: number;
  recommendation: 'reduce' | 'maintain' | 'increase';
  suggestedAdjustments: string[];
}

export class SchedulingService {
  private static instance: SchedulingService;
  private energyService: EnergyAnalysisService;
  private flowService: FlowDetectionService;

  private constructor() {
    this.energyService = EnergyAnalysisService.getInstance();
    this.flowService = FlowDetectionService.getInstance();
  }

  public static getInstance(): SchedulingService {
    if (!SchedulingService.instance) {
      SchedulingService.instance = new SchedulingService();
    }
    return SchedulingService.instance;
  }

  // Generate optimized schedule for a specific date
  async generateOptimizedSchedule(userId: string, targetDate: Date): Promise<OptimizedSchedule> {
    try {
      // Get user's tasks for the date
      const tasks = await this.getTasksForScheduling(userId, targetDate);
      
      // Get user's energy pattern and flow pattern
      const energyPattern = await this.energyService.getCircadianPattern(userId);
      const flowPattern = await this.flowService.getFlowPattern(userId);
      
      // Get available time slots
      const availableSlots = await this.getAvailableTimeSlots(userId, targetDate);
      
      // Generate schedule using optimization algorithm
      const optimizedSlots = await this.optimizeTaskScheduling(
        tasks,
        availableSlots,
        energyPattern,
        flowPattern,
        targetDate
      );

      // Calculate alignment scores
      const energyAlignment = this.calculateEnergyAlignment(optimizedSlots);
      const flowAlignment = this.calculateFlowAlignment(optimizedSlots);
      const workloadBalance = this.calculateWorkloadBalance(optimizedSlots);
      
      const overallScore = (energyAlignment + flowAlignment + workloadBalance) / 3;

      const schedule: OptimizedSchedule = {
        date: targetDate,
        slots: optimizedSlots,
        energyAlignment,
        flowAlignment,
        workloadBalance,
        overallScore,
      };

      // Cache the schedule
      const cacheKey = CacheKeys.analyticsData(userId, `schedule-${targetDate.toISOString().split('T')[0]}`);
      await cacheService.set(cacheKey, schedule, 3600); // Cache for 1 hour

      logger.info(`Optimized schedule generated for user ${userId} on ${targetDate.toISOString().split('T')[0]}`);
      return schedule;
    } catch (error) {
      logger.error('Error generating optimized schedule:', error);
      throw error;
    }
  }

  // Get tasks that need scheduling
  private async getTasksForScheduling(userId: string, targetDate: Date): Promise<any[]> {
    const startOfDay = new Date(targetDate);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(targetDate);
    endOfDay.setHours(23, 59, 59, 999);

    return await prisma.task.findMany({
      where: {
        userId,
        status: {
          in: ['PENDING', 'IN_PROGRESS'],
        },
        OR: [
          {
            scheduledAt: {
              gte: startOfDay,
              lte: endOfDay,
            },
          },
          {
            scheduledAt: null,
            dueDate: {
              gte: targetDate,
            },
          },
        ],
      },
      orderBy: [
        { priority: 'desc' },
        { dueDate: 'asc' },
      ],
    });
  }

  // Get available time slots from calendar integrations
  private async getAvailableTimeSlots(userId: string, targetDate: Date): Promise<ScheduleSlot[]> {
    try {
      // Get user preferences for working hours
      const userPrefs = await prisma.userPreferences.findUnique({
        where: { userId },
      });

      const workingHours = userPrefs?.workingHours as any || { start: '09:00', end: '17:00' };
      
      // Generate time slots (30-minute intervals)
      const slots: ScheduleSlot[] = [];
      const startTime = new Date(targetDate);
      const [startHour, startMinute] = workingHours.start.split(':').map(Number);
      startTime.setHours(startHour, startMinute, 0, 0);
      
      const endTime = new Date(targetDate);
      const [endHour, endMinute] = workingHours.end.split(':').map(Number);
      endTime.setHours(endHour, endMinute, 0, 0);

      let currentTime = new Date(startTime);
      
      while (currentTime < endTime) {
        const slotEnd = new Date(currentTime.getTime() + 30 * 60 * 1000); // 30 minutes
        
        // Predict energy level for this time
        const energyPrediction = await this.energyService.predictEnergyLevel(userId, currentTime);
        
        // Predict flow probability
        const flowPrediction = await this.flowService.predictOptimalFlowTime(userId, currentTime);
        
        // Check for conflicts (simplified - in real implementation, check calendar integrations)
        const availability = await this.checkSlotAvailability(userId, currentTime, slotEnd);
        
        slots.push({
          startTime: new Date(currentTime),
          endTime: slotEnd,
          energyLevel: energyPrediction.predictedEnergyLevel,
          flowProbability: flowPrediction.probability,
          availability,
          conflictLevel: availability === 'free' ? 0 : availability === 'tentative' ? 0.5 : 1,
        });

        currentTime = slotEnd;
      }

      return slots;
    } catch (error) {
      logger.error('Error getting available time slots:', error);
      return [];
    }
  }

  // Check slot availability (placeholder for calendar integration)
  private async checkSlotAvailability(
    userId: string,
    startTime: Date,
    endTime: Date
  ): Promise<'free' | 'busy' | 'tentative'> {
    // In a real implementation, this would check calendar integrations
    // For now, assume most slots are free
    return 'free';
  }

  // Optimize task scheduling using a greedy algorithm with energy/flow awareness
  private async optimizeTaskScheduling(
    tasks: any[],
    availableSlots: ScheduleSlot[],
    energyPattern: any,
    flowPattern: any,
    targetDate: Date
  ): Promise<ScheduledTask[]> {
    const scheduledTasks: ScheduledTask[] = [];
    const freeSlots = availableSlots.filter(slot => slot.availability === 'free');
    
    // Sort tasks by priority and deadline
    const sortedTasks = tasks.sort((a, b) => {
      const priorityWeight = { URGENT: 4, HIGH: 3, MEDIUM: 2, LOW: 1 };
      const aPriority = priorityWeight[a.priority as keyof typeof priorityWeight] || 1;
      const bPriority = priorityWeight[b.priority as keyof typeof priorityWeight] || 1;
      
      if (aPriority !== bPriority) return bPriority - aPriority;
      
      // If same priority, sort by deadline
      if (a.dueDate && b.dueDate) {
        return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
      }
      
      return 0;
    });

    for (const task of sortedTasks) {
      const taskDuration = task.estimatedDuration || 60; // Default 60 minutes
      const slotsNeeded = Math.ceil(taskDuration / 30); // 30-minute slots
      
      // Find best consecutive slots for this task
      const bestSlotIndex = this.findBestSlotForTask(task, freeSlots, slotsNeeded);
      
      if (bestSlotIndex !== -1) {
        const startSlot = freeSlots[bestSlotIndex];
        const endSlot = freeSlots[bestSlotIndex + slotsNeeded - 1];
        
        const scheduledTask: ScheduledTask = {
          taskId: task.id,
          title: task.title,
          startTime: startSlot.startTime,
          endTime: endSlot.endTime,
          energyRequired: task.energyRequired || 3,
          predictedEnergyLevel: startSlot.energyLevel,
          flowRequired: task.focusRequired || false,
          flowProbability: startSlot.flowProbability,
          priority: task.priority,
          confidence: this.calculateSchedulingConfidence(task, startSlot),
        };

        scheduledTasks.push(scheduledTask);
        
        // Remove used slots
        freeSlots.splice(bestSlotIndex, slotsNeeded);
        
        // Update task in database
        await prisma.task.update({
          where: { id: task.id },
          data: { scheduledAt: startSlot.startTime },
        });
      }
    }

    return scheduledTasks;
  }

  // Find the best slot for a task based on energy and flow requirements
  private findBestSlotForTask(task: any, availableSlots: ScheduleSlot[], slotsNeeded: number): number {
    let bestScore = -1;
    let bestIndex = -1;

    for (let i = 0; i <= availableSlots.length - slotsNeeded; i++) {
      // Check if we have enough consecutive slots
      let consecutiveSlots = true;
      for (let j = 0; j < slotsNeeded - 1; j++) {
        const currentSlot = availableSlots[i + j];
        const nextSlot = availableSlots[i + j + 1];
        if (nextSlot.startTime.getTime() !== currentSlot.endTime.getTime()) {
          consecutiveSlots = false;
          break;
        }
      }

      if (!consecutiveSlots) continue;

      // Calculate score for this slot
      const startSlot = availableSlots[i];
      let score = 0;

      // Energy alignment score
      const energyRequired = task.energyRequired || 3;
      const energyMatch = 1 - Math.abs(startSlot.energyLevel - energyRequired) / 5;
      score += energyMatch * 0.4;

      // Flow alignment score (if task requires focus)
      if (task.focusRequired) {
        score += startSlot.flowProbability * 0.4;
      } else {
        score += 0.2; // Neutral score for non-focus tasks
      }

      // Priority bonus
      const priorityWeight = { URGENT: 0.2, HIGH: 0.15, MEDIUM: 0.1, LOW: 0.05 };
      score += priorityWeight[task.priority as keyof typeof priorityWeight] || 0.05;

      if (score > bestScore) {
        bestScore = score;
        bestIndex = i;
      }
    }

    return bestIndex;
  }

  // Calculate scheduling confidence
  private calculateSchedulingConfidence(task: any, slot: ScheduleSlot): number {
    let confidence = 0.5; // Base confidence

    // Energy match confidence
    const energyRequired = task.energyRequired || 3;
    const energyMatch = 1 - Math.abs(slot.energyLevel - energyRequired) / 5;
    confidence += energyMatch * 0.3;

    // Flow match confidence (if applicable)
    if (task.focusRequired) {
      confidence += slot.flowProbability * 0.2;
    }

    return Math.min(1, Math.max(0, confidence));
  }

  // Calculate energy alignment score
  private calculateEnergyAlignment(scheduledTasks: ScheduledTask[]): number {
    if (scheduledTasks.length === 0) return 1;

    const alignmentScores = scheduledTasks.map(task => {
      const energyMatch = 1 - Math.abs(task.predictedEnergyLevel - task.energyRequired) / 5;
      return Math.max(0, energyMatch);
    });

    return alignmentScores.reduce((sum, score) => sum + score, 0) / alignmentScores.length;
  }

  // Calculate flow alignment score
  private calculateFlowAlignment(scheduledTasks: ScheduledTask[]): number {
    const flowTasks = scheduledTasks.filter(task => task.flowRequired);
    if (flowTasks.length === 0) return 1;

    const flowScores = flowTasks.map(task => task.flowProbability);
    return flowScores.reduce((sum, score) => sum + score, 0) / flowScores.length;
  }

  // Calculate workload balance score
  private calculateWorkloadBalance(scheduledTasks: ScheduledTask[]): number {
    if (scheduledTasks.length === 0) return 1;

    // Calculate total workload vs available time
    const totalDuration = scheduledTasks.reduce((sum, task) => {
      return sum + (task.endTime.getTime() - task.startTime.getTime()) / (1000 * 60);
    }, 0);

    // Assume 8-hour workday (480 minutes)
    const workdayMinutes = 480;
    const utilizationRate = totalDuration / workdayMinutes;

    // Optimal utilization is around 70-80%
    if (utilizationRate >= 0.7 && utilizationRate <= 0.8) {
      return 1;
    } else if (utilizationRate < 0.7) {
      return 0.8; // Underutilized
    } else {
      return Math.max(0, 1 - (utilizationRate - 0.8) * 2); // Overutilized
    }
  }

  // Analyze workload for a specific date
  async analyzeWorkload(userId: string, targetDate: Date): Promise<WorkloadAnalysis> {
    try {
      const schedule = await this.generateOptimizedSchedule(userId, targetDate);
      
      // Calculate metrics
      const totalWorkload = schedule.slots.reduce((sum, task) => {
        return sum + (task.endTime.getTime() - task.startTime.getTime()) / (1000 * 60);
      }, 0);

      const energyDemand = schedule.slots.reduce((sum, task) => {
        return sum + task.energyRequired;
      }, 0) / schedule.slots.length;

      const availableEnergy = schedule.slots.reduce((sum, task) => {
        return sum + task.predictedEnergyLevel;
      }, 0) / schedule.slots.length;

      const stressLevel = Math.max(0, Math.min(10, (energyDemand - availableEnergy) * 2 + 5));

      // Generate recommendation
      let recommendation: 'reduce' | 'maintain' | 'increase';
      const suggestedAdjustments: string[] = [];

      if (stressLevel > 7) {
        recommendation = 'reduce';
        suggestedAdjustments.push('Consider rescheduling some low-priority tasks');
        suggestedAdjustments.push('Add more breaks between demanding tasks');
      } else if (stressLevel < 3 && totalWorkload < 240) { // Less than 4 hours
        recommendation = 'increase';
        suggestedAdjustments.push('You have capacity for additional tasks');
        suggestedAdjustments.push('Consider tackling some items from your backlog');
      } else {
        recommendation = 'maintain';
        suggestedAdjustments.push('Your workload looks well-balanced');
      }

      return {
        date: targetDate,
        totalWorkload,
        energyDemand,
        availableEnergy,
        stressLevel,
        recommendation,
        suggestedAdjustments,
      };
    } catch (error) {
      logger.error('Error analyzing workload:', error);
      throw error;
    }
  }

  // Reschedule a task
  async rescheduleTask(userId: string, taskId: string, newDateTime: Date): Promise<any> {
    try {
      const task = await prisma.task.findFirst({
        where: {
          id: taskId,
          userId,
        },
      });

      if (!task) {
        throw new Error('Task not found');
      }

      const updatedTask = await prisma.task.update({
        where: { id: taskId },
        data: {
          scheduledAt: newDateTime,
          status: 'RESCHEDULED',
        },
      });

      // Clear schedule cache
      const dateKey = newDateTime.toISOString().split('T')[0];
      const cacheKey = CacheKeys.analyticsData(userId, `schedule-${dateKey}`);
      await cacheService.del(cacheKey);

      logger.info(`Task rescheduled for user ${userId}: ${taskId}`);
      return updatedTask;
    } catch (error) {
      logger.error('Error rescheduling task:', error);
      throw error;
    }
  }

  // Get schedule for a date range
  async getSchedule(userId: string, startDate: Date, endDate: Date): Promise<OptimizedSchedule[]> {
    try {
      const schedules: OptimizedSchedule[] = [];
      const currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        const schedule = await this.generateOptimizedSchedule(userId, new Date(currentDate));
        schedules.push(schedule);
        currentDate.setDate(currentDate.getDate() + 1);
      }

      return schedules;
    } catch (error) {
      logger.error('Error getting schedule:', error);
      throw error;
    }
  }
}
