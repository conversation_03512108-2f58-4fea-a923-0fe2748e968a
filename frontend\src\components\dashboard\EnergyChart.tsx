'use client';

import React from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { format, parseISO } from 'date-fns';
import { EnergyData } from '@/types/api';
import { getEnergyLevelLabel, getEnergyLevelColor } from '@/lib/utils';

interface EnergyChartProps {
  data: EnergyData[];
  height?: number;
}

export function EnergyChart({ data, height = 300 }: EnergyChartProps) {
  // Transform data for the chart
  const chartData = data
    .slice(-7) // Last 7 days
    .map((entry) => ({
      date: format(parseISO(entry.timestamp), 'MMM dd'),
      energy: entry.energyLevel,
      mood: entry.mood,
      stress: entry.stressLevel,
      timestamp: entry.timestamp,
    }))
    .reverse(); // Show oldest to newest

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            {label}
          </p>
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 dark:text-gray-400">Energy:</span>
              <span className="text-xs font-medium text-gray-900 dark:text-white">
                {getEnergyLevelLabel(data.energy)} ({data.energy}/5)
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 dark:text-gray-400">Mood:</span>
              <span className="text-xs font-medium text-gray-900 dark:text-white">
                {data.mood}/10
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 dark:text-gray-400">Stress:</span>
              <span className="text-xs font-medium text-gray-900 dark:text-white">
                {data.stress}/10
              </span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
        <div className="text-center">
          <p className="text-sm">No energy data available</p>
          <p className="text-xs mt-1">Start logging your energy levels to see patterns</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full" style={{ height }}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis 
            dataKey="date" 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: 'currentColor' }}
            className="text-gray-600 dark:text-gray-400"
          />
          <YAxis 
            domain={[0, 5]}
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: 'currentColor' }}
            className="text-gray-600 dark:text-gray-400"
            tickFormatter={(value) => `${value}`}
          />
          <Tooltip content={<CustomTooltip />} />
          <Line
            type="monotone"
            dataKey="energy"
            stroke="#0ea5e9"
            strokeWidth={3}
            dot={{ fill: '#0ea5e9', strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: '#0ea5e9', strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
      
      {/* Legend */}
      <div className="flex items-center justify-center mt-4 space-x-6">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
          <span className="text-xs text-gray-600 dark:text-gray-400">Energy Level</span>
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Scale: 1 (Very Low) - 5 (Very High)
        </div>
      </div>
    </div>
  );
}
