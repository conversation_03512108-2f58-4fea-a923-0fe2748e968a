version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: rhythmai-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: rhythmai_dev
      POSTGRES_USER: rhythmai
      POSTGRES_PASSWORD: rhythmai_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - rhythmai-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U rhythmai -d rhythmai_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: rhythmai-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - rhythmai-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # RhythmAI Backend API
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: rhythmai-api
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 8000
      DATABASE_URL: *****************************************************/rhythmai_dev
      REDIS_URL: redis://redis:6379
      JWT_SECRET: dev-jwt-secret-change-in-production
      JWT_REFRESH_SECRET: dev-refresh-secret-change-in-production
      FRONTEND_URL: http://localhost:3000
      LOG_LEVEL: debug
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - rhythmai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Adminer (Database Management)
  adminer:
    image: adminer:4-standalone
    container_name: rhythmai-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
      ADMINER_DESIGN: pepa-linha
    depends_on:
      - postgres
    networks:
      - rhythmai-network

  # Redis Commander (Redis Management)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: rhythmai-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
      HTTP_USER: admin
      HTTP_PASSWORD: admin
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - rhythmai-network

  # Nginx (Reverse Proxy for Production)
  nginx:
    image: nginx:alpine
    container_name: rhythmai-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - api
    networks:
      - rhythmai-network
    profiles:
      - production

  # Prometheus (Monitoring)
  prometheus:
    image: prom/prometheus:latest
    container_name: rhythmai-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - rhythmai-network
    profiles:
      - monitoring

  # Grafana (Metrics Dashboard)
  grafana:
    image: grafana/grafana:latest
    container_name: rhythmai-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning:ro
    depends_on:
      - prometheus
    networks:
      - rhythmai-network
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  rhythmai-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
