'use client';

import React from 'react';
import { 
  Zap, 
  Target, 
  CheckSquare, 
  Calendar, 
  Brain,
  TrendingUp,
  Clock,
  Award,
  AlertCircle,
  Plus
} from 'lucide-react';
import { useDashboard, useEnergyData, useActiveFlowSession, useHabits, useTasks, useCoachingSummary } from '@/hooks/useApi';
// Dashboard components will be created separately
import { formatRelativeTime, formatDuration } from '@/lib/utils';

export default function DashboardPage() {
  const { data: dashboardData, isLoading: isDashboardLoading } = useDashboard();
  const { data: energyData } = useEnergyData({ limit: 7 });
  const { data: activeFlowSession } = useActiveFlowSession();
  const { data: habits } = useHabits({ isActive: true, limit: 5 });
  const { data: tasks } = useTasks({ status: 'PENDING', limit: 5 });
  const { data: coachingSummary } = useCoachingSummary();

  if (isDashboardLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center space-y-4">
          <div className="spinner w-8 h-8"></div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  const dashboard = dashboardData?.data;
  const user = dashboard?.user;
  const stats = dashboard?.stats;
  const needsAttention = dashboard?.needsAttention;

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Welcome back, {user?.displayName || 'there'}! 👋
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Here's what's happening with your productivity today.
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <QuickActions />
        </div>
      </div>

      {/* Attention Alerts */}
      {needsAttention && (
        <div className="space-y-3">
          {needsAttention.onboardingIncomplete && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start">
                <AlertCircle className="h-5 w-5 text-blue-400 mt-0.5" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800 dark:text-blue-300">
                    Complete your setup
                  </h3>
                  <p className="mt-1 text-sm text-blue-700 dark:text-blue-400">
                    Finish setting up your profile to get personalized insights and recommendations.
                  </p>
                  <div className="mt-3">
                    <button className="btn-sm btn-primary">
                      Complete Setup
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {needsAttention.noRecentEnergyData && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
              <div className="flex items-start">
                <Zap className="h-5 w-5 text-yellow-400 mt-0.5" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-300">
                    Track your energy
                  </h3>
                  <p className="mt-1 text-sm text-yellow-700 dark:text-yellow-400">
                    Start logging your energy levels to get personalized insights and optimize your schedule.
                  </p>
                  <div className="mt-3">
                    <button className="btn-sm btn-secondary">
                      Log Energy Now
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {needsAttention.manyPendingTasks && (
            <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
              <div className="flex items-start">
                <Calendar className="h-5 w-5 text-orange-400 mt-0.5" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-orange-800 dark:text-orange-300">
                    High task load
                  </h3>
                  <p className="mt-1 text-sm text-orange-700 dark:text-orange-400">
                    You have many pending tasks. Consider prioritizing or rescheduling some items.
                  </p>
                  <div className="mt-3">
                    <button className="btn-sm btn-secondary">
                      Review Tasks
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Stats Overview */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <DashboardCard
          title="Energy Logs"
          value={stats?.recentEnergyLogs || 0}
          subtitle="This week"
          icon={Zap}
          color="text-yellow-600"
          bgColor="bg-yellow-100 dark:bg-yellow-900/20"
          trend={stats?.recentEnergyLogs > 0 ? 'up' : undefined}
        />
        
        <DashboardCard
          title="Flow Sessions"
          value={stats?.recentFlowSessions || 0}
          subtitle="This week"
          icon={Target}
          color="text-purple-600"
          bgColor="bg-purple-100 dark:bg-purple-900/20"
          trend={stats?.recentFlowSessions > 0 ? 'up' : undefined}
        />
        
        <DashboardCard
          title="Active Habits"
          value={stats?.activeHabits || 0}
          subtitle="Tracking now"
          icon={CheckSquare}
          color="text-green-600"
          bgColor="bg-green-100 dark:bg-green-900/20"
        />
        
        <DashboardCard
          title="Pending Tasks"
          value={stats?.pendingTasks || 0}
          subtitle="To complete"
          icon={Calendar}
          color="text-blue-600"
          bgColor="bg-blue-100 dark:bg-blue-900/20"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column */}
        <div className="lg:col-span-2 space-y-6">
          {/* Energy Chart */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Energy Levels</h3>
              <p className="card-description">Your energy patterns over the last 7 days</p>
            </div>
            <div className="card-content">
              <EnergyChart data={energyData?.data || []} />
            </div>
          </div>

          {/* Progress Overview */}
          <ProgressOverview />

          {/* Recent Activity */}
          <RecentActivity />
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Active Flow Session */}
          {activeFlowSession?.data && (
            <div className="card">
              <div className="card-header">
                <h3 className="card-title flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  Active Flow Session
                </h3>
              </div>
              <div className="card-content">
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {activeFlowSession.data.taskType}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Started {formatRelativeTime(activeFlowSession.data.startTime)}
                    </p>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500 dark:text-gray-400">Intensity</span>
                    <span className={`badge badge-outline ${activeFlowSession.data.intensity === 'peak' ? 'text-purple-600' : 'text-blue-600'}`}>
                      {activeFlowSession.data.intensity}
                    </span>
                  </div>
                  <button className="btn-sm btn-outline w-full">
                    End Session
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* AI Insights */}
          <InsightsPanel />

          {/* Today's Habits */}
          {habits?.data && habits.data.length > 0 && (
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">Today's Habits</h3>
                <p className="card-description">Keep up your momentum</p>
              </div>
              <div className="card-content">
                <div className="space-y-3">
                  {habits.data.slice(0, 3).map((habit) => (
                    <div key={habit.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {habit.name}
                        </span>
                      </div>
                      <button className="btn-sm btn-ghost">
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                  {habits.data.length > 3 && (
                    <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                      <button className="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300">
                        View all habits →
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Upcoming Tasks */}
          {tasks?.data && tasks.data.length > 0 && (
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">Upcoming Tasks</h3>
                <p className="card-description">Your next priorities</p>
              </div>
              <div className="card-content">
                <div className="space-y-3">
                  {tasks.data.slice(0, 3).map((task) => (
                    <div key={task.id} className="flex items-start space-x-3">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        task.priority === 'URGENT' ? 'bg-red-500' :
                        task.priority === 'HIGH' ? 'bg-orange-500' :
                        task.priority === 'MEDIUM' ? 'bg-blue-500' : 'bg-gray-400'
                      }`}></div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {task.title}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                          {task.estimatedDuration && (
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {formatDuration(task.estimatedDuration)}
                            </span>
                          )}
                          {task.dueDate && (
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              Due {formatRelativeTime(task.dueDate)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                  {tasks.data.length > 3 && (
                    <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                      <button className="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300">
                        View all tasks →
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Coaching Summary */}
          {coachingSummary?.data && (
            <div className="card">
              <div className="card-header">
                <h3 className="card-title flex items-center">
                  <Brain className="w-5 h-5 mr-2 text-purple-600" />
                  AI Coaching
                </h3>
              </div>
              <div className="card-content">
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900 dark:text-white">
                        {coachingSummary.data.averageProductivityScore || 0}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Productivity
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900 dark:text-white">
                        {coachingSummary.data.averageWellnessScore || 0}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Wellness
                      </div>
                    </div>
                  </div>
                  
                  {coachingSummary.data.highPriorityInsights > 0 && (
                    <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                      <div className="flex items-center">
                        <AlertCircle className="h-4 w-4 text-yellow-600 mr-2" />
                        <span className="text-sm font-medium text-yellow-800 dark:text-yellow-300">
                          {coachingSummary.data.highPriorityInsights} insights need attention
                        </span>
                      </div>
                    </div>
                  )}
                  
                  <button className="btn-sm btn-primary w-full">
                    View Coaching Session
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
