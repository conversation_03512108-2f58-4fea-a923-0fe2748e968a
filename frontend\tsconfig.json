{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/pages/*": ["./src/pages/*"], "@/app/*": ["./src/app/*"], "@/hooks/*": ["./src/hooks/*"], "@/utils/*": ["./src/utils/*"], "@/lib/*": ["./src/lib/*"], "@/types/*": ["./src/types/*"], "@/styles/*": ["./src/styles/*"], "@/config/*": ["./src/config/*"], "@/constants/*": ["./src/constants/*"], "@/context/*": ["./src/context/*"], "@/layouts/*": ["./src/layouts/*"], "@/api/*": ["./src/api/*"]}, "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "useUnknownInCatchVariables": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "allowUnreachableCode": false, "allowUnusedLabels": false}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "out", "dist"]}