'use client';

import React, { useState } from 'react';
import { 
  Volume2, 
  VolumeX, 
  Play, 
  Pause, 
  CloudRain, 
  Trees, 
  Waves, 
  Music, 
  Headphones,
  Settings,
  ChevronDown,
  Check
} from 'lucide-react';
import { useAmbientSound } from '@/hooks/useBreakTimer';
import { AMBIENT_SOUNDS, SoundCategory } from '@/types/flow-session';
import { cn } from '@/lib/utils';

interface AmbientSoundsProps {
  className?: string;
  compact?: boolean;
  showCategories?: boolean;
}

const categoryIcons = {
  nature: Trees,
  'white-noise': Volume2,
  instrumental: Music,
  binaural: Headphones,
  ambient: Waves
};

const categoryColors = {
  nature: 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20',
  'white-noise': 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/20',
  instrumental: 'text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/20',
  binaural: 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20',
  ambient: 'text-cyan-600 dark:text-cyan-400 bg-cyan-100 dark:bg-cyan-900/20'
};

export function AmbientSounds({ className, compact = false, showCategories = true }: AmbientSoundsProps) {
  const [selectedCategory, setSelectedCategory] = useState<SoundCategory | 'all'>('all');
  const [isExpanded, setIsExpanded] = useState(false);
  
  const {
    currentSound,
    volume,
    isPlaying,
    playSound,
    stopSound,
    changeVolume,
    togglePlayback,
    availableSounds
  } = useAmbientSound();

  const filteredSounds = selectedCategory === 'all' 
    ? availableSounds 
    : availableSounds.filter(sound => sound.category === selectedCategory);

  const categories = Array.from(new Set(availableSounds.map(sound => sound.category)));

  if (compact) {
    return (
      <div className={cn('relative', className)}>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center space-x-2 px-3 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <Volume2 className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            {currentSound ? availableSounds.find(s => s.id === currentSound)?.name : 'Sounds'}
          </span>
          <ChevronDown className="w-4 h-4 text-gray-400" />
        </button>

        {isExpanded && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 min-w-64">
            <div className="p-4 space-y-4">
              {/* Volume Control */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Volume</span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">{volume}%</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={volume}
                  onChange={(e) => changeVolume(parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              {/* Sound Selection */}
              <div className="space-y-2">
                {availableSounds.map((sound) => {
                  const Icon = categoryIcons[sound.category];
                  return (
                    <button
                      key={sound.id}
                      onClick={() => isPlaying && currentSound === sound.id ? stopSound() : playSound(sound.id)}
                      className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-left"
                    >
                      <div className={cn('w-6 h-6 rounded flex items-center justify-center', categoryColors[sound.category])}>
                        <Icon className="w-3 h-3" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">{sound.name}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 truncate">{sound.description}</div>
                      </div>
                      {isPlaying && currentSound === sound.id ? (
                        <Pause className="w-4 h-4 text-green-500" />
                      ) : (
                        <Play className="w-4 h-4 text-gray-400" />
                      )}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn('bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center">
            <Volume2 className="w-5 h-5 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Ambient Sounds
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Background sounds to enhance focus
            </p>
          </div>
        </div>

        {/* Master Controls */}
        <div className="flex items-center space-x-2">
          <button
            onClick={togglePlayback}
            disabled={!currentSound}
            className={cn(
              'p-2 rounded-lg transition-colors',
              isPlaying
                ? 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400'
                : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400',
              !currentSound && 'opacity-50 cursor-not-allowed'
            )}
          >
            {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
          </button>
          
          <button
            onClick={stopSound}
            disabled={!isPlaying}
            className={cn(
              'p-2 rounded-lg transition-colors',
              'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400',
              !isPlaying && 'opacity-50 cursor-not-allowed'
            )}
          >
            <VolumeX className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Volume Control */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Volume</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">{volume}%</span>
        </div>
        <div className="flex items-center space-x-3">
          <VolumeX className="w-4 h-4 text-gray-400" />
          <input
            type="range"
            min="0"
            max="100"
            value={volume}
            onChange={(e) => changeVolume(parseInt(e.target.value))}
            className="flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
          />
          <Volume2 className="w-4 h-4 text-gray-400" />
        </div>
      </div>

      {/* Category Filter */}
      {showCategories && (
        <div className="mb-6">
          <div className="flex items-center space-x-2 overflow-x-auto pb-2">
            <button
              onClick={() => setSelectedCategory('all')}
              className={cn(
                'px-3 py-1 rounded-full text-sm font-medium transition-colors whitespace-nowrap',
                selectedCategory === 'all'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              )}
            >
              All
            </button>
            {categories.map((category) => {
              const Icon = categoryIcons[category];
              return (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={cn(
                    'flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium transition-colors whitespace-nowrap',
                    selectedCategory === category
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  )}
                >
                  <Icon className="w-3 h-3" />
                  <span className="capitalize">{category.replace('-', ' ')}</span>
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Sound Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {filteredSounds.map((sound) => {
          const Icon = categoryIcons[sound.category];
          const isCurrentSound = currentSound === sound.id;
          const isSoundPlaying = isPlaying && isCurrentSound;
          
          return (
            <button
              key={sound.id}
              onClick={() => isSoundPlaying ? stopSound() : playSound(sound.id)}
              className={cn(
                'flex items-center space-x-3 p-4 rounded-lg border-2 transition-all duration-200 text-left',
                isCurrentSound
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800'
              )}
            >
              <div className={cn('w-10 h-10 rounded-lg flex items-center justify-center', categoryColors[sound.category])}>
                <Icon className="w-5 h-5" />
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="text-sm font-semibold text-gray-900 dark:text-white">
                    {sound.name}
                  </h4>
                  {isSoundPlaying && (
                    <div className="flex items-center space-x-1">
                      <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse" />
                      <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }} />
                      <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }} />
                    </div>
                  )}
                </div>
                
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  {sound.description}
                </p>
                
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-400 capitalize">
                    {sound.category.replace('-', ' ')}
                  </span>
                  
                  {isSoundPlaying ? (
                    <Pause className="w-4 h-4 text-green-500" />
                  ) : (
                    <Play className="w-4 h-4 text-gray-400" />
                  )}
                </div>
              </div>
            </button>
          );
        })}
      </div>

      {/* Current Playing */}
      {isPlaying && currentSound && (
        <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
              <Volume2 className="w-4 h-4 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h4 className="text-sm font-semibold text-green-900 dark:text-green-100">
                Now Playing: {availableSounds.find(s => s.id === currentSound)?.name}
              </h4>
              <p className="text-xs text-green-700 dark:text-green-300">
                Volume: {volume}% • {availableSounds.find(s => s.id === currentSound)?.description}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
