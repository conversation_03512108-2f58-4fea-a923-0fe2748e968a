'use client';

import React, { useState } from 'react';
import { Plus, TrendingUp, Calendar, Clock, Zap } from 'lucide-react';
import { useEnergyData, useCircadianPattern, useEnergyPredictions } from '@/hooks/useApi';
import { EnergyLogForm } from '@/components/energy/EnergyLogForm';
import { EnergyChart } from '@/components/energy/EnergyChart';
import { CircadianPattern } from '@/components/energy/CircadianPattern';
import { EnergyInsights } from '@/components/energy/EnergyInsights';
import { EnergyPredictions } from '@/components/energy/EnergyPredictions';
import { EnergyHistory } from '@/components/energy/EnergyHistory';
import { DashboardCard } from '@/components/dashboard/DashboardCard';
import { formatDate, getEnergyLevelLabel } from '@/lib/utils';

export default function EnergyPage() {
  const [showLogForm, setShowLogForm] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter'>('week');

  // Fetch energy data
  const { data: energyData, isLoading: isEnergyLoading } = useEnergyData({
    limit: selectedPeriod === 'week' ? 7 : selectedPeriod === 'month' ? 30 : 90,
  });

  const { data: circadianPattern } = useCircadianPattern();
  const { data: predictions } = useEnergyPredictions(24); // Next 24 hours

  // Calculate stats
  const energyEntries = energyData?.data || [];
  const latestEntry = energyEntries[0];
  const averageEnergy = energyEntries.length > 0 
    ? energyEntries.reduce((sum, entry) => sum + entry.energyLevel, 0) / energyEntries.length 
    : 0;

  const todayEntries = energyEntries.filter(entry => 
    formatDate(entry.timestamp, 'yyyy-MM-dd') === formatDate(new Date(), 'yyyy-MM-dd')
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <Zap className="w-7 h-7 mr-3 text-yellow-500" />
            Energy Tracking
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Monitor and optimize your energy patterns throughout the day
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          {/* Period Selector */}
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as 'week' | 'month' | 'quarter')}
            className="input text-sm"
          >
            <option value="week">Last 7 days</option>
            <option value="month">Last 30 days</option>
            <option value="quarter">Last 90 days</option>
          </select>
          
          <button
            onClick={() => setShowLogForm(true)}
            className="btn-primary btn-md flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Log Energy</span>
          </button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <DashboardCard
          title="Current Energy"
          value={latestEntry ? getEnergyLevelLabel(latestEntry.energyLevel) : 'No data'}
          subtitle={latestEntry ? formatDate(latestEntry.timestamp, 'MMM d, h:mm a') : ''}
          icon={Zap}
          color="text-yellow-600"
          bgColor="bg-yellow-100 dark:bg-yellow-900/20"
        />
        
        <DashboardCard
          title="Average Energy"
          value={averageEnergy.toFixed(1)}
          subtitle={`Over ${selectedPeriod === 'week' ? '7 days' : selectedPeriod === 'month' ? '30 days' : '90 days'}`}
          icon={TrendingUp}
          color="text-blue-600"
          bgColor="bg-blue-100 dark:bg-blue-900/20"
        />
        
        <DashboardCard
          title="Today's Logs"
          value={todayEntries.length}
          subtitle="Energy entries today"
          icon={Calendar}
          color="text-green-600"
          bgColor="bg-green-100 dark:bg-green-900/20"
        />
        
        <DashboardCard
          title="Peak Hours"
          value={circadianPattern?.data?.peakEnergyHours?.[0] ? `${circadianPattern.data.peakEnergyHours[0]}:00` : 'Learning...'}
          subtitle="Your optimal time"
          icon={Clock}
          color="text-purple-600"
          bgColor="bg-purple-100 dark:bg-purple-900/20"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Charts and Patterns */}
        <div className="lg:col-span-2 space-y-6">
          {/* Energy Chart */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Energy Levels Over Time</h3>
              <p className="card-description">
                Track your energy patterns and identify trends
              </p>
            </div>
            <div className="card-content">
              {isEnergyLoading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="spinner w-6 h-6"></div>
                </div>
              ) : (
                <EnergyChart data={energyEntries} period={selectedPeriod} />
              )}
            </div>
          </div>

          {/* Circadian Pattern */}
          {circadianPattern?.data && (
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">Your Circadian Pattern</h3>
                <p className="card-description">
                  Discover your natural energy rhythm throughout the day
                </p>
              </div>
              <div className="card-content">
                <CircadianPattern pattern={circadianPattern.data} />
              </div>
            </div>
          )}

          {/* Energy History */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Recent Energy Logs</h3>
              <p className="card-description">
                Your latest energy entries with context
              </p>
            </div>
            <div className="card-content">
              <EnergyHistory entries={energyEntries.slice(0, 10)} />
            </div>
          </div>
        </div>

        {/* Right Column - Insights and Predictions */}
        <div className="space-y-6">
          {/* Energy Predictions */}
          {predictions?.data && predictions.data.length > 0 && (
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">Energy Forecast</h3>
                <p className="card-description">
                  Predicted energy levels for the next 24 hours
                </p>
              </div>
              <div className="card-content">
                <EnergyPredictions predictions={predictions.data} />
              </div>
            </div>
          )}

          {/* Energy Insights */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Insights & Tips</h3>
              <p className="card-description">
                Personalized recommendations based on your patterns
              </p>
            </div>
            <div className="card-content">
              <EnergyInsights 
                energyData={energyEntries} 
                circadianPattern={circadianPattern?.data} 
              />
            </div>
          </div>

          {/* Quick Log Widget */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Quick Energy Check</h3>
              <p className="card-description">
                How are you feeling right now?
              </p>
            </div>
            <div className="card-content">
              <div className="grid grid-cols-5 gap-2">
                {[1, 2, 3, 4, 5].map((level) => (
                  <button
                    key={level}
                    onClick={() => setShowLogForm(true)}
                    className={`p-3 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-yellow-400 dark:hover:border-yellow-500 transition-colors text-center`}
                  >
                    <div className="text-lg mb-1">
                      {level === 1 ? '😴' : level === 2 ? '😐' : level === 3 ? '🙂' : level === 4 ? '😊' : '⚡'}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      {level}
                    </div>
                  </button>
                ))}
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-3">
                Click any level to log your current energy
              </p>
            </div>
          </div>

          {/* Energy Tips */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Energy Optimization Tips</h3>
            </div>
            <div className="card-content">
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      Morning Sunlight
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Get 10-15 minutes of natural light within an hour of waking
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      Hydration Check
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Drink water regularly throughout the day
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      Power Naps
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      15-20 minute naps can boost afternoon energy
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      Movement Breaks
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Take short walks every 1-2 hours
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Energy Log Form Modal */}
      {showLogForm && (
        <EnergyLogForm
          onClose={() => setShowLogForm(false)}
          onSuccess={() => {
            setShowLogForm(false);
            // Data will be automatically refetched due to React Query
          }}
        />
      )}
    </div>
  );
}
