import { Router, Request, Response } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { FlowDetectionService, FlowIntensity } from '../services/flowDetectionService';
import { logger } from '../utils/logger';

const router = Router();
const flowService = FlowDetectionService.getInstance();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// POST /api/flow/session/start - Start a new flow session
router.post('/session/start',
  [
    body('taskType').isString().isLength({ min: 1, max: 100 }).withMessage('Task type is required and must be less than 100 characters'),
    body('environment').isObject().withMessage('Environment data is required'),
    body('environment.location').isString().withMessage('Location is required'),
    body('environment.noiseLevel').isInt({ min: 1, max: 10 }).withMessage('Noise level must be between 1 and 10'),
    body('environment.lighting').isString().withMessage('Lighting is required'),
    body('environment.temperature').optional().isNumeric().withMessage('Temperature must be a number'),
    body('environment.musicType').optional().isString().withMessage('Music type must be a string'),
    body('environment.distractions').optional().isArray().withMessage('Distractions must be an array'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { taskType, environment } = req.body;

      // Check if user already has an active session
      const activeSession = await flowService.getActiveFlowSession(userId);
      if (activeSession) {
        return res.status(400).json({
          error: 'Active session exists',
          message: 'Please end your current flow session before starting a new one',
          activeSession,
        });
      }

      const session = await flowService.startFlowSession(userId, taskType, environment);

      res.status(201).json({
        message: 'Flow session started successfully',
        data: session,
      });

      logger.info(`Flow session started for user ${userId}`);
    } catch (error) {
      logger.error('Error starting flow session:', error);
      res.status(500).json({
        error: 'Failed to start flow session',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// PUT /api/flow/session/:sessionId - Update flow session
router.put('/session/:sessionId',
  [
    param('sessionId').isString().withMessage('Session ID is required'),
    body('intensity').optional().isIn(['light', 'moderate', 'deep', 'peak']).withMessage('Invalid intensity level'),
    body('interruptions').optional().isInt({ min: 0 }).withMessage('Interruptions must be a non-negative integer'),
    body('productivity').optional().isInt({ min: 1, max: 10 }).withMessage('Productivity must be between 1 and 10'),
    body('satisfaction').optional().isInt({ min: 1, max: 10 }).withMessage('Satisfaction must be between 1 and 10'),
    body('triggers').optional().isArray().withMessage('Triggers must be an array'),
    body('notes').optional().isString().isLength({ max: 1000 }).withMessage('Notes must be less than 1000 characters'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { sessionId } = req.params;
      const updates = req.body;

      // Verify session belongs to user
      const activeSession = await flowService.getActiveFlowSession(userId);
      if (!activeSession || activeSession.id !== sessionId) {
        return res.status(404).json({
          error: 'Session not found',
          message: 'Active flow session not found or does not belong to user',
        });
      }

      const updatedSession = await flowService.updateFlowSession(sessionId, updates);

      res.json({
        message: 'Flow session updated successfully',
        data: updatedSession,
      });

      logger.info(`Flow session updated for user ${userId}: ${sessionId}`);
    } catch (error) {
      logger.error('Error updating flow session:', error);
      res.status(500).json({
        error: 'Failed to update flow session',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// POST /api/flow/session/:sessionId/end - End flow session
router.post('/session/:sessionId/end',
  [
    param('sessionId').isString().withMessage('Session ID is required'),
    body('productivity').isInt({ min: 1, max: 10 }).withMessage('Productivity rating is required (1-10)'),
    body('satisfaction').isInt({ min: 1, max: 10 }).withMessage('Satisfaction rating is required (1-10)'),
    body('notes').optional().isString().isLength({ max: 1000 }).withMessage('Notes must be less than 1000 characters'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { sessionId } = req.params;
      const { productivity, satisfaction, notes } = req.body;

      // Verify session belongs to user
      const activeSession = await flowService.getActiveFlowSession(userId);
      if (!activeSession || activeSession.id !== sessionId) {
        return res.status(404).json({
          error: 'Session not found',
          message: 'Active flow session not found or does not belong to user',
        });
      }

      const endedSession = await flowService.endFlowSession(sessionId, {
        productivity,
        satisfaction,
        notes,
      });

      res.json({
        message: 'Flow session ended successfully',
        data: endedSession,
      });

      logger.info(`Flow session ended for user ${userId}: ${sessionId}`);
    } catch (error) {
      logger.error('Error ending flow session:', error);
      res.status(500).json({
        error: 'Failed to end flow session',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/flow/session/active - Get active flow session
router.get('/session/active', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const activeSession = await flowService.getActiveFlowSession(userId);

    if (!activeSession) {
      return res.status(404).json({
        error: 'No active session',
        message: 'No active flow session found',
      });
    }

    res.json({
      data: activeSession,
    });
  } catch (error) {
    logger.error('Error getting active flow session:', error);
    res.status(500).json({
      error: 'Failed to get active flow session',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/flow/sessions - Get flow sessions history
router.get('/sessions',
  [
    query('startDate').isISO8601().withMessage('Start date must be a valid ISO 8601 date'),
    query('endDate').isISO8601().withMessage('End date must be a valid ISO 8601 date'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { startDate, endDate, limit } = req.query;

      const start = new Date(startDate as string);
      const end = new Date(endDate as string);
      const maxResults = parseInt(limit as string) || 50;

      // Validate date range
      if (start > end) {
        return res.status(400).json({
          error: 'Invalid date range',
          message: 'Start date must be before end date',
        });
      }

      const sessions = await flowService.getFlowSessions(userId, start, end, maxResults);

      res.json({
        data: sessions,
        count: sessions.length,
        dateRange: {
          startDate: start,
          endDate: end,
        },
      });
    } catch (error) {
      logger.error('Error getting flow sessions:', error);
      res.status(500).json({
        error: 'Failed to get flow sessions',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/flow/pattern - Get flow pattern analysis
router.get('/pattern', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const pattern = await flowService.getFlowPattern(userId);

    if (!pattern) {
      return res.status(404).json({
        error: 'Pattern not found',
        message: 'No flow pattern available. Please complete at least 3 flow sessions.',
      });
    }

    res.json({
      data: pattern,
    });
  } catch (error) {
    logger.error('Error getting flow pattern:', error);
    res.status(500).json({
      error: 'Failed to get flow pattern',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// POST /api/flow/pattern/update - Force update flow pattern
router.post('/pattern/update', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const pattern = await flowService.updateFlowPattern(userId);

    res.json({
      message: 'Flow pattern updated successfully',
      data: pattern,
    });

    logger.info(`Flow pattern updated for user ${userId}`);
  } catch (error) {
    logger.error('Error updating flow pattern:', error);
    res.status(500).json({
      error: 'Failed to update flow pattern',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/flow/predict - Predict optimal flow time
router.get('/predict',
  [
    query('timestamp').isISO8601().withMessage('Timestamp must be a valid ISO 8601 date'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { timestamp } = req.query;

      const targetTime = new Date(timestamp as string);

      // Don't predict too far in the future (max 7 days)
      const maxFutureDate = new Date();
      maxFutureDate.setDate(maxFutureDate.getDate() + 7);

      if (targetTime > maxFutureDate) {
        return res.status(400).json({
          error: 'Invalid prediction time',
          message: 'Cannot predict more than 7 days in the future',
        });
      }

      const prediction = await flowService.predictOptimalFlowTime(userId, targetTime);

      res.json({
        data: prediction,
      });
    } catch (error) {
      logger.error('Error predicting optimal flow time:', error);
      res.status(500).json({
        error: 'Failed to predict optimal flow time',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/flow/stats - Get flow statistics
router.get('/stats',
  [
    query('period').optional().isIn(['week', 'month', 'quarter']).withMessage('Period must be week, month, or quarter'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const period = (req.query.period as string) || 'month';

      const endDate = new Date();
      const startDate = new Date();

      switch (period) {
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(startDate.getMonth() - 3);
          break;
      }

      const sessions = await flowService.getFlowSessions(userId, startDate, endDate);

      if (sessions.length === 0) {
        return res.json({
          data: {
            period,
            sessionCount: 0,
            totalFlowTime: 0,
            averageProductivity: 0,
            averageSatisfaction: 0,
            averageDuration: 0,
            intensityDistribution: {},
          },
        });
      }

      // Calculate statistics
      const totalFlowTime = sessions.reduce((sum, s) => sum + (s.duration || 0), 0);
      const averageProductivity = sessions.reduce((sum, s) => sum + s.productivity, 0) / sessions.length;
      const averageSatisfaction = sessions.reduce((sum, s) => sum + s.satisfaction, 0) / sessions.length;
      const averageDuration = totalFlowTime / sessions.length;

      // Calculate intensity distribution
      const intensityDistribution: { [key: string]: number } = {};
      sessions.forEach(s => {
        intensityDistribution[s.intensity] = (intensityDistribution[s.intensity] || 0) + 1;
      });

      // Convert to percentages
      Object.keys(intensityDistribution).forEach(key => {
        intensityDistribution[key] = (intensityDistribution[key] / sessions.length * 100);
      });

      const stats = {
        period,
        sessionCount: sessions.length,
        totalFlowTime,
        averageProductivity: Math.round(averageProductivity * 10) / 10,
        averageSatisfaction: Math.round(averageSatisfaction * 10) / 10,
        averageDuration: Math.round(averageDuration),
        intensityDistribution,
        trends: calculateFlowTrends(sessions),
      };

      res.json({
        data: stats,
      });
    } catch (error) {
      logger.error('Error getting flow statistics:', error);
      res.status(500).json({
        error: 'Failed to get flow statistics',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/flow/recommendations - Get flow recommendations
router.get('/recommendations', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const pattern = await flowService.getFlowPattern(userId);
    
    if (!pattern) {
      return res.json({
        data: {
          recommendations: [
            'Complete at least 3 flow sessions to get personalized recommendations',
            'Try different environments and track what works best',
            'Experiment with different times of day for deep work',
          ],
        },
      });
    }

    const recommendations = generateFlowRecommendations(pattern);

    res.json({
      data: {
        recommendations,
        pattern: {
          optimalConditions: pattern.optimalConditions,
          successRate: pattern.successRate,
          averageIntensity: pattern.averageIntensity,
        },
      },
    });
  } catch (error) {
    logger.error('Error getting flow recommendations:', error);
    res.status(500).json({
      error: 'Failed to get flow recommendations',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Helper function to calculate flow trends
function calculateFlowTrends(sessions: any[]): any {
  if (sessions.length < 4) return {};

  const firstHalf = sessions.slice(0, Math.floor(sessions.length / 2));
  const secondHalf = sessions.slice(Math.floor(sessions.length / 2));

  const firstAvgProductivity = firstHalf.reduce((sum, s) => sum + s.productivity, 0) / firstHalf.length;
  const secondAvgProductivity = secondHalf.reduce((sum, s) => sum + s.productivity, 0) / secondHalf.length;

  const firstAvgDuration = firstHalf.reduce((sum, s) => sum + (s.duration || 0), 0) / firstHalf.length;
  const secondAvgDuration = secondHalf.reduce((sum, s) => sum + (s.duration || 0), 0) / secondHalf.length;

  return {
    productivityTrend: secondAvgProductivity > firstAvgProductivity ? 'improving' : 'declining',
    durationTrend: secondAvgDuration > firstAvgDuration ? 'increasing' : 'decreasing',
    productivityChange: ((secondAvgProductivity - firstAvgProductivity) / firstAvgProductivity * 100).toFixed(1),
    durationChange: ((secondAvgDuration - firstAvgDuration) / firstAvgDuration * 100).toFixed(1),
  };
}

// Helper function to generate flow recommendations
function generateFlowRecommendations(pattern: any): string[] {
  const recommendations: string[] = [];
  const conditions = pattern.optimalConditions;

  if (conditions.timeOfDay && conditions.timeOfDay.length > 0) {
    const optimalHours = conditions.timeOfDay.slice(0, 3).join(', ');
    recommendations.push(`Your optimal flow times are around ${optimalHours}:00. Schedule deep work during these hours.`);
  }

  if (conditions.duration) {
    recommendations.push(`Your optimal flow session duration is ${conditions.duration} minutes. Plan accordingly.`);
  }

  if (conditions.environment.location) {
    recommendations.push(`You perform best in: ${conditions.environment.location}. Try to use this location for important work.`);
  }

  if (conditions.environment.musicType) {
    recommendations.push(`${conditions.environment.musicType} music helps you focus. Consider using it during work sessions.`);
  }

  if (pattern.successRate < 0.6) {
    recommendations.push('Your flow success rate could be improved. Try reducing distractions and optimizing your environment.');
  } else if (pattern.successRate > 0.8) {
    recommendations.push('Excellent flow consistency! Consider extending your sessions or tackling more challenging tasks.');
  }

  return recommendations;
}

export default router;
