'use client';

import React from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts';
import { format, addHours } from 'date-fns';
import { Crystal, TrendingUp, Clock, Zap } from 'lucide-react';
import { EnergyPrediction } from '@/types/api';
import { getEnergyLevelLabel, cn } from '@/lib/utils';

interface EnergyPredictionsProps {
  predictions: EnergyPrediction[];
  height?: number;
}

export function EnergyPredictions({ predictions, height = 250 }: EnergyPredictionsProps) {
  // Transform predictions for chart
  const chartData = React.useMemo(() => {
    return predictions.map((prediction, index) => {
      const timestamp = new Date(prediction.timestamp);
      return {
        hour: format(timestamp, 'ha'),
        fullTime: format(timestamp, 'h:mm a'),
        energy: prediction.predictedEnergyLevel,
        confidence: prediction.confidence * 100,
        factors: prediction.factors,
        timestamp: prediction.timestamp,
        index,
      };
    });
  }, [predictions]);

  // Find next peak and low
  const nextPeak = chartData.reduce((max, current) => 
    current.energy > max.energy ? current : max
  );
  
  const nextLow = chartData.reduce((min, current) => 
    current.energy < min.energy ? current : min
  );

  const averagePredictedEnergy = chartData.reduce((sum, item) => sum + item.energy, 0) / chartData.length;

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            {data.fullTime}
          </p>
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 dark:text-gray-400">Predicted Energy:</span>
              <span className="text-xs font-medium text-blue-600 dark:text-blue-400">
                {data.energy.toFixed(1)}/5
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 dark:text-gray-400">Confidence:</span>
              <span className="text-xs font-medium text-gray-900 dark:text-white">
                {data.confidence.toFixed(0)}%
              </span>
            </div>
            <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">Contributing factors:</p>
              <div className="space-y-0.5">
                <div className="flex justify-between text-xs">
                  <span>Time of day:</span>
                  <span>{(data.factors.timeOfDay * 100).toFixed(0)}%</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span>Historical pattern:</span>
                  <span>{(data.factors.historicalPattern * 100).toFixed(0)}%</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span>Recent trend:</span>
                  <span>{(data.factors.recentTrend * 100).toFixed(0)}%</span>
                </div>
                {data.factors.sleepQuality && (
                  <div className="flex justify-between text-xs">
                    <span>Sleep quality:</span>
                    <span>{(data.factors.sleepQuality * 100).toFixed(0)}%</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  if (!predictions || predictions.length === 0) {
    return (
      <div className="text-center py-6 text-gray-500 dark:text-gray-400">
        <Crystal className="w-8 h-8 mx-auto mb-2 opacity-50" />
        <p className="text-sm">No predictions available</p>
        <p className="text-xs mt-1">More data needed for accurate forecasting</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Prediction Summary */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
          <div className="flex items-center space-x-2 mb-1">
            <TrendingUp className="w-4 h-4 text-green-600 dark:text-green-400" />
            <span className="text-xs font-medium text-green-800 dark:text-green-300">
              Next Peak
            </span>
          </div>
          <div className="text-sm font-bold text-green-800 dark:text-green-300">
            {nextPeak.fullTime}
          </div>
          <div className="text-xs text-green-600 dark:text-green-400">
            {nextPeak.energy.toFixed(1)}/5 energy
          </div>
        </div>

        <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3">
          <div className="flex items-center space-x-2 mb-1">
            <Clock className="w-4 h-4 text-orange-600 dark:text-orange-400" />
            <span className="text-xs font-medium text-orange-800 dark:text-orange-300">
              Next Low
            </span>
          </div>
          <div className="text-sm font-bold text-orange-800 dark:text-orange-300">
            {nextLow.fullTime}
          </div>
          <div className="text-xs text-orange-600 dark:text-orange-400">
            {nextLow.energy.toFixed(1)}/5 energy
          </div>
        </div>
      </div>

      {/* Prediction Chart */}
      <div className="w-full" style={{ height }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="hour"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 10, fill: 'currentColor' }}
              className="text-gray-600 dark:text-gray-400"
            />
            <YAxis 
              domain={[0, 5]}
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 10, fill: 'currentColor' }}
              className="text-gray-600 dark:text-gray-400"
            />
            <Tooltip content={<CustomTooltip />} />
            <ReferenceLine 
              y={averagePredictedEnergy} 
              stroke="#6b7280" 
              strokeDasharray="5 5" 
              label={{ value: "Avg", position: "insideTopRight", fontSize: 10 }}
            />
            <Line
              type="monotone"
              dataKey="energy"
              stroke="#8b5cf6"
              strokeWidth={2}
              dot={{ fill: '#8b5cf6', strokeWidth: 1, r: 3 }}
              activeDot={{ r: 5, stroke: '#8b5cf6', strokeWidth: 2 }}
              strokeDasharray="5 5"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* Recommendations */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-3">
          <Zap className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300">
            Energy Optimization Tips
          </h4>
        </div>
        
        <div className="space-y-2 text-sm text-blue-700 dark:text-blue-400">
          {nextPeak.energy > 4 && (
            <p>• Schedule your most important tasks around {nextPeak.fullTime} when your energy peaks.</p>
          )}
          
          {nextLow.energy < 2.5 && (
            <p>• Plan lighter activities or breaks around {nextLow.fullTime} when energy is predicted to be low.</p>
          )}
          
          {averagePredictedEnergy < 3 && (
            <p>• Your predicted energy is below average today. Consider getting more sunlight and staying hydrated.</p>
          )}
          
          {chartData.some(d => d.confidence < 60) && (
            <p>• Some predictions have lower confidence. Keep logging consistently to improve accuracy.</p>
          )}
          
          <p>• These predictions are based on your historical patterns and may vary based on sleep, stress, and other factors.</p>
        </div>
      </div>

      {/* Confidence Indicator */}
      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
        <span>Prediction accuracy:</span>
        <div className="flex items-center space-x-2">
          <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
            <div 
              className="bg-purple-500 h-1.5 rounded-full"
              style={{ 
                width: `${chartData.reduce((sum, d) => sum + d.confidence, 0) / chartData.length}%` 
              }}
            />
          </div>
          <span>
            {((chartData.reduce((sum, d) => sum + d.confidence, 0) / chartData.length)).toFixed(0)}%
          </span>
        </div>
      </div>
    </div>
  );
}
