'use client';

import React from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  Calendar, 
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  Target
} from 'lucide-react';
import { EnergyData, CircadianPattern } from '@/types/api';
import { format, parseISO, startOfWeek, endOfWeek, isWithinInterval } from 'date-fns';
import { cn } from '@/lib/utils';

interface EnergyInsightsProps {
  energyData: EnergyData[];
  circadianPattern?: CircadianPattern;
}

interface Insight {
  id: string;
  type: 'positive' | 'negative' | 'neutral' | 'tip';
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  action?: string;
  priority: 'high' | 'medium' | 'low';
}

export function EnergyInsights({ energyData, circadianPattern }: EnergyInsightsProps) {
  const insights = React.useMemo(() => {
    const insights: Insight[] = [];

    if (!energyData || energyData.length === 0) {
      insights.push({
        id: 'no-data',
        type: 'neutral',
        icon: Calendar,
        title: 'Start tracking consistently',
        description: 'Log your energy levels regularly to get personalized insights and recommendations.',
        action: 'Log your energy now',
        priority: 'high',
      });
      return insights;
    }

    // Calculate basic stats
    const recentEntries = energyData.slice(0, 7); // Last 7 entries
    const averageEnergy = recentEntries.reduce((sum, entry) => sum + entry.energyLevel, 0) / recentEntries.length;
    const averageMood = recentEntries.reduce((sum, entry) => sum + entry.mood, 0) / recentEntries.length;
    const averageStress = recentEntries.reduce((sum, entry) => sum + entry.stressLevel, 0) / recentEntries.length;

    // Energy trend analysis
    if (recentEntries.length >= 3) {
      const recent3 = recentEntries.slice(0, 3);
      const previous3 = recentEntries.slice(3, 6);
      
      if (previous3.length >= 3) {
        const recentAvg = recent3.reduce((sum, entry) => sum + entry.energyLevel, 0) / 3;
        const previousAvg = previous3.reduce((sum, entry) => sum + entry.energyLevel, 0) / 3;
        
        if (recentAvg > previousAvg + 0.5) {
          insights.push({
            id: 'energy-improving',
            type: 'positive',
            icon: TrendingUp,
            title: 'Energy levels are improving',
            description: `Your average energy has increased from ${previousAvg.toFixed(1)} to ${recentAvg.toFixed(1)} recently.`,
            priority: 'medium',
          });
        } else if (recentAvg < previousAvg - 0.5) {
          insights.push({
            id: 'energy-declining',
            type: 'negative',
            icon: TrendingDown,
            title: 'Energy levels are declining',
            description: `Your average energy has decreased from ${previousAvg.toFixed(1)} to ${recentAvg.toFixed(1)} recently.`,
            action: 'Review your sleep and stress levels',
            priority: 'high',
          });
        }
      }
    }

    // High stress correlation
    const highStressEntries = recentEntries.filter(entry => entry.stressLevel >= 7);
    if (highStressEntries.length >= 3) {
      const avgEnergyDuringStress = highStressEntries.reduce((sum, entry) => sum + entry.energyLevel, 0) / highStressEntries.length;
      
      if (avgEnergyDuringStress < averageEnergy - 0.5) {
        insights.push({
          id: 'stress-energy-correlation',
          type: 'negative',
          icon: AlertTriangle,
          title: 'High stress is affecting your energy',
          description: `Your energy drops to ${avgEnergyDuringStress.toFixed(1)} when stress is high (7+).`,
          action: 'Try stress management techniques',
          priority: 'high',
        });
      }
    }

    // Sleep quality impact
    const entriesWithSleep = recentEntries.filter(entry => entry.sleepQuality);
    if (entriesWithSleep.length >= 3) {
      const goodSleepEntries = entriesWithSleep.filter(entry => entry.sleepQuality! >= 7);
      const poorSleepEntries = entriesWithSleep.filter(entry => entry.sleepQuality! <= 4);
      
      if (goodSleepEntries.length >= 2 && poorSleepEntries.length >= 2) {
        const goodSleepEnergy = goodSleepEntries.reduce((sum, entry) => sum + entry.energyLevel, 0) / goodSleepEntries.length;
        const poorSleepEnergy = poorSleepEntries.reduce((sum, entry) => sum + entry.energyLevel, 0) / poorSleepEntries.length;
        
        if (goodSleepEnergy > poorSleepEnergy + 0.5) {
          insights.push({
            id: 'sleep-energy-correlation',
            type: 'positive',
            icon: CheckCircle,
            title: 'Good sleep boosts your energy',
            description: `Your energy is ${goodSleepEnergy.toFixed(1)} after good sleep vs ${poorSleepEnergy.toFixed(1)} after poor sleep.`,
            action: 'Prioritize consistent sleep schedule',
            priority: 'medium',
          });
        }
      }
    }

    // Caffeine patterns
    const caffeineEntries = recentEntries.filter(entry => entry.caffeine && entry.caffeine > 0);
    if (caffeineEntries.length >= 3) {
      const avgCaffeineEnergy = caffeineEntries.reduce((sum, entry) => sum + entry.energyLevel, 0) / caffeineEntries.length;
      const noCaffeineEntries = recentEntries.filter(entry => !entry.caffeine || entry.caffeine === 0);
      
      if (noCaffeineEntries.length >= 2) {
        const avgNoCaffeineEnergy = noCaffeineEntries.reduce((sum, entry) => sum + entry.energyLevel, 0) / noCaffeineEntries.length;
        
        if (avgCaffeineEnergy < avgNoCaffeineEnergy - 0.3) {
          insights.push({
            id: 'caffeine-dependency',
            type: 'negative',
            icon: AlertTriangle,
            title: 'Potential caffeine dependency',
            description: `Your energy is lower (${avgCaffeineEnergy.toFixed(1)}) on caffeine days vs non-caffeine days (${avgNoCaffeineEnergy.toFixed(1)}).`,
            action: 'Consider reducing caffeine gradually',
            priority: 'medium',
          });
        }
      }
    }

    // Circadian pattern insights
    if (circadianPattern) {
      if (circadianPattern.confidence > 80) {
        const peakHour = circadianPattern.peakEnergyHours[0];
        if (peakHour !== undefined) {
          insights.push({
            id: 'circadian-pattern',
            type: 'positive',
            icon: Clock,
            title: 'Strong circadian rhythm detected',
            description: `Your energy consistently peaks around ${peakHour === 0 ? '12 AM' : peakHour < 12 ? `${peakHour} AM` : peakHour === 12 ? '12 PM' : `${peakHour - 12} PM`}.`,
            action: 'Schedule important tasks during peak hours',
            priority: 'medium',
          });
        }
      } else if (circadianPattern.confidence < 50) {
        insights.push({
          id: 'inconsistent-pattern',
          type: 'neutral',
          icon: Target,
          title: 'Inconsistent energy patterns',
          description: 'Your energy levels vary significantly throughout the day.',
          action: 'Track more consistently to identify patterns',
          priority: 'low',
        });
      }
    }

    // General tips based on average levels
    if (averageEnergy < 2.5) {
      insights.push({
        id: 'low-energy-tips',
        type: 'tip',
        icon: Lightbulb,
        title: 'Boost your energy naturally',
        description: 'Your average energy is below optimal. Try morning sunlight, regular meals, and short walks.',
        priority: 'high',
      });
    } else if (averageEnergy > 4) {
      insights.push({
        id: 'high-energy-tips',
        type: 'tip',
        icon: Lightbulb,
        title: 'Maintain your high energy',
        description: 'Great energy levels! Keep up your current routine and consider sharing what works.',
        priority: 'low',
      });
    }

    if (averageStress > 7) {
      insights.push({
        id: 'high-stress-tips',
        type: 'tip',
        icon: Lightbulb,
        title: 'Manage stress levels',
        description: 'Your stress levels are consistently high. Try meditation, deep breathing, or regular exercise.',
        action: 'Explore stress management techniques',
        priority: 'high',
      });
    }

    // Sort by priority
    return insights.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }, [energyData, circadianPattern]);

  const getInsightStyle = (type: Insight['type']) => {
    switch (type) {
      case 'positive':
        return 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800';
      case 'negative':
        return 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800';
      case 'tip':
        return 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800';
      default:
        return 'bg-gray-50 border-gray-200 dark:bg-gray-800 dark:border-gray-700';
    }
  };

  const getIconColor = (type: Insight['type']) => {
    switch (type) {
      case 'positive':
        return 'text-green-600 dark:text-green-400';
      case 'negative':
        return 'text-red-600 dark:text-red-400';
      case 'tip':
        return 'text-blue-600 dark:text-blue-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getTextColor = (type: Insight['type']) => {
    switch (type) {
      case 'positive':
        return 'text-green-800 dark:text-green-300';
      case 'negative':
        return 'text-red-800 dark:text-red-300';
      case 'tip':
        return 'text-blue-800 dark:text-blue-300';
      default:
        return 'text-gray-800 dark:text-gray-300';
    }
  };

  if (insights.length === 0) {
    return (
      <div className="text-center py-6 text-gray-500 dark:text-gray-400">
        <Lightbulb className="w-8 h-8 mx-auto mb-2 opacity-50" />
        <p className="text-sm">No insights available yet</p>
        <p className="text-xs mt-1">Keep logging your energy to get personalized insights</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {insights.slice(0, 5).map((insight) => {
        const Icon = insight.icon;
        return (
          <div
            key={insight.id}
            className={cn(
              'p-4 rounded-lg border',
              getInsightStyle(insight.type)
            )}
          >
            <div className="flex items-start space-x-3">
              <Icon className={cn('w-5 h-5 mt-0.5 flex-shrink-0', getIconColor(insight.type))} />
              <div className="flex-1 min-w-0">
                <h4 className={cn('text-sm font-medium', getTextColor(insight.type))}>
                  {insight.title}
                </h4>
                <p className={cn('text-sm mt-1 opacity-90', getTextColor(insight.type))}>
                  {insight.description}
                </p>
                {insight.action && (
                  <button className={cn(
                    'text-xs font-medium mt-2 hover:underline',
                    getTextColor(insight.type)
                  )}>
                    {insight.action} →
                  </button>
                )}
              </div>
              {insight.priority === 'high' && (
                <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0 mt-2"></div>
              )}
            </div>
          </div>
        );
      })}

      {insights.length > 5 && (
        <button className="w-full text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300 py-2">
          View all insights ({insights.length - 5} more)
        </button>
      )}
    </div>
  );
}
