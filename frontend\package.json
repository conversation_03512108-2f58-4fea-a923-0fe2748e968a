{"name": "<PERSON><PERSON>-frontend", "version": "1.0.0", "description": "RhythmAI - AI-Powered Productivity and Wellness Platform Frontend", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "clean": "rimraf .next out dist", "prepare": "husky install"}, "dependencies": {"next": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.0", "@types/node": "^20.11.0", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "tailwindcss": "^3.4.1", "autoprefixer": "^10.4.17", "postcss": "^8.4.33", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@headlessui/react": "^1.7.18", "@heroicons/react": "^2.0.18", "clsx": "^2.1.0", "tailwind-merge": "^2.2.1", "axios": "^1.6.7", "react-query": "^3.39.3", "react-hook-form": "^7.49.3", "@hookform/resolvers": "^3.3.4", "zod": "^3.22.4", "date-fns": "^3.3.1", "recharts": "^2.10.4", "react-hot-toast": "^2.4.1", "framer-motion": "^11.0.3", "react-intersection-observer": "^9.5.3", "js-cookie": "^3.0.5", "@types/js-cookie": "^3.0.6", "react-calendar": "^4.8.0", "@types/react-calendar": "^4.1.0", "react-select": "^5.8.0", "react-datepicker": "^4.25.0", "@types/react-datepicker": "^4.19.5", "react-dropzone": "^14.2.3", "lucide-react": "^0.323.0", "sonner": "^1.4.0", "vaul": "^0.9.0", "cmdk": "^0.2.1"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^14.1.0", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.2.4", "prettier-plugin-tailwindcss": "^0.5.11", "husky": "^8.0.3", "lint-staged": "^15.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.4.1", "@testing-library/user-event": "^14.5.2", "cross-env": "^7.0.3", "rimraf": "^5.0.5", "@next/bundle-analyzer": "^14.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "keywords": ["productivity", "wellness", "ai", "habit-tracking", "energy-management", "flow-state", "next.js", "react", "typescript"], "author": "RhythmAI Team", "license": "MIT"}