import axios from 'axios';
import { prisma } from '../../config/database';
import { logger } from '../../utils/logger';
import { config } from '../../config/database';

// Fitbit data interfaces
export interface FitbitProfile {
  userId: string;
  displayName: string;
  avatar: string;
  timezone: string;
  dateOfBirth: string;
  gender: string;
  height: number;
  weight: number;
}

export interface FitbitActivityData {
  date: Date;
  steps: number;
  distance: number; // in km
  calories: number;
  activeMinutes: number;
  sedentaryMinutes: number;
  floors: number;
  elevation: number;
}

export interface FitbitSleepData {
  date: Date;
  duration: number; // in minutes
  efficiency: number; // percentage
  startTime: Date;
  endTime: Date;
  timeInBed: number; // in minutes
  timeAsleep: number; // in minutes
  awakeDuration: number; // in minutes
  restlessDuration: number; // in minutes
  stages: {
    deep: number;
    light: number;
    rem: number;
    wake: number;
  };
}

export interface FitbitHeartRateData {
  date: Date;
  restingHeartRate: number;
  heartRateZones: {
    fatBurn: { min: number; max: number; minutes: number };
    cardio: { min: number; max: number; minutes: number };
    peak: { min: number; max: number; minutes: number };
  };
  averageHeartRate?: number;
}

export class FitbitService {
  private baseUrl = 'https://api.fitbit.com/1';
  private authUrl = 'https://www.fitbit.com/oauth2/authorize';
  private tokenUrl = 'https://api.fitbit.com/oauth2/token';

  // Get authorization URL for OAuth flow
  getAuthUrl(state: string): string {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: config.integrations.fitbit.clientId!,
      redirect_uri: config.integrations.fitbit.redirectUri || 'http://localhost:3000/integrations/fitbit/callback',
      scope: 'activity heartrate location nutrition profile settings sleep social weight',
      state,
    });

    return `${this.authUrl}?${params.toString()}`;
  }

  // Exchange authorization code for tokens
  async exchangeCodeForTokens(code: string): Promise<any> {
    try {
      const response = await axios.post(this.tokenUrl, {
        client_id: config.integrations.fitbit.clientId,
        client_secret: config.integrations.fitbit.clientSecret,
        grant_type: 'authorization_code',
        code,
        redirect_uri: config.integrations.fitbit.redirectUri || 'http://localhost:3000/integrations/fitbit/callback',
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      return response.data;
    } catch (error) {
      logger.error('Error exchanging Fitbit code for tokens:', error);
      throw new Error('Failed to exchange authorization code');
    }
  }

  // Store integration credentials
  async storeIntegration(userId: string, tokens: any): Promise<void> {
    try {
      const expiresAt = new Date(Date.now() + tokens.expires_in * 1000);

      await prisma.integration.upsert({
        where: {
          userId_provider: {
            userId,
            provider: 'FITBIT',
          },
        },
        update: {
          accessToken: this.encryptToken(tokens.access_token),
          refreshToken: this.encryptToken(tokens.refresh_token),
          expiresAt,
          providerUserId: tokens.user_id,
          isActive: true,
          lastSyncAt: new Date(),
        },
        create: {
          userId,
          provider: 'FITBIT',
          accessToken: this.encryptToken(tokens.access_token),
          refreshToken: this.encryptToken(tokens.refresh_token),
          expiresAt,
          providerUserId: tokens.user_id,
          isActive: true,
          lastSyncAt: new Date(),
        },
      });

      logger.info(`Fitbit integration stored for user ${userId}`);
    } catch (error) {
      logger.error('Error storing Fitbit integration:', error);
      throw error;
    }
  }

  // Get user's Fitbit integration
  async getIntegration(userId: string): Promise<any> {
    try {
      const integration = await prisma.integration.findUnique({
        where: {
          userId_provider: {
            userId,
            provider: 'FITBIT',
          },
        },
      });

      if (!integration || !integration.isActive) {
        return null;
      }

      return {
        userId: integration.userId,
        accessToken: this.decryptToken(integration.accessToken!),
        refreshToken: this.decryptToken(integration.refreshToken!),
        expiresAt: integration.expiresAt!,
        providerUserId: integration.providerUserId,
      };
    } catch (error) {
      logger.error('Error getting Fitbit integration:', error);
      return null;
    }
  }

  // Refresh access token if needed
  async refreshTokenIfNeeded(integration: any): Promise<string> {
    try {
      if (new Date() < integration.expiresAt) {
        return integration.accessToken;
      }

      const response = await axios.post(this.tokenUrl, {
        grant_type: 'refresh_token',
        refresh_token: integration.refreshToken,
      }, {
        headers: {
          'Authorization': `Basic ${Buffer.from(`${config.integrations.fitbit.clientId}:${config.integrations.fitbit.clientSecret}`).toString('base64')}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      // Update stored tokens
      await this.storeIntegration(integration.userId, response.data);

      return response.data.access_token;
    } catch (error) {
      logger.error('Error refreshing Fitbit token:', error);
      throw new Error('Failed to refresh access token');
    }
  }

  // Get user profile
  async getProfile(userId: string): Promise<FitbitProfile> {
    try {
      const integration = await this.getIntegration(userId);
      if (!integration) {
        throw new Error('Fitbit integration not found');
      }

      const accessToken = await this.refreshTokenIfNeeded(integration);

      const response = await axios.get(`${this.baseUrl}/user/-/profile.json`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      const profile = response.data.user;

      return {
        userId: profile.encodedId,
        displayName: profile.displayName,
        avatar: profile.avatar,
        timezone: profile.timezone,
        dateOfBirth: profile.dateOfBirth,
        gender: profile.gender,
        height: profile.height,
        weight: profile.weight,
      };
    } catch (error) {
      logger.error('Error getting Fitbit profile:', error);
      throw error;
    }
  }

  // Get activity data for a specific date
  async getActivityData(userId: string, date: Date): Promise<FitbitActivityData> {
    try {
      const integration = await this.getIntegration(userId);
      if (!integration) {
        throw new Error('Fitbit integration not found');
      }

      const accessToken = await this.refreshTokenIfNeeded(integration);
      const dateStr = date.toISOString().split('T')[0];

      const response = await axios.get(`${this.baseUrl}/user/-/activities/date/${dateStr}.json`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      const data = response.data.summary;

      return {
        date,
        steps: data.steps,
        distance: data.distances.find((d: any) => d.activity === 'total')?.distance || 0,
        calories: data.caloriesOut,
        activeMinutes: data.veryActiveMinutes + data.fairlyActiveMinutes,
        sedentaryMinutes: data.sedentaryMinutes,
        floors: data.floors,
        elevation: data.elevation,
      };
    } catch (error) {
      logger.error('Error getting Fitbit activity data:', error);
      throw error;
    }
  }

  // Get sleep data for a specific date
  async getSleepData(userId: string, date: Date): Promise<FitbitSleepData | null> {
    try {
      const integration = await this.getIntegration(userId);
      if (!integration) {
        throw new Error('Fitbit integration not found');
      }

      const accessToken = await this.refreshTokenIfNeeded(integration);
      const dateStr = date.toISOString().split('T')[0];

      const response = await axios.get(`${this.baseUrl}/user/-/sleep/date/${dateStr}.json`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      const sleepData = response.data.sleep[0];
      if (!sleepData) {
        return null;
      }

      return {
        date,
        duration: sleepData.duration,
        efficiency: sleepData.efficiency,
        startTime: new Date(sleepData.startTime),
        endTime: new Date(sleepData.endTime),
        timeInBed: sleepData.timeInBed,
        timeAsleep: sleepData.minutesAsleep,
        awakeDuration: sleepData.minutesAwake,
        restlessDuration: sleepData.restlessCount,
        stages: {
          deep: sleepData.levels?.summary?.deep?.minutes || 0,
          light: sleepData.levels?.summary?.light?.minutes || 0,
          rem: sleepData.levels?.summary?.rem?.minutes || 0,
          wake: sleepData.levels?.summary?.wake?.minutes || 0,
        },
      };
    } catch (error) {
      logger.error('Error getting Fitbit sleep data:', error);
      throw error;
    }
  }

  // Get heart rate data for a specific date
  async getHeartRateData(userId: string, date: Date): Promise<FitbitHeartRateData | null> {
    try {
      const integration = await this.getIntegration(userId);
      if (!integration) {
        throw new Error('Fitbit integration not found');
      }

      const accessToken = await this.refreshTokenIfNeeded(integration);
      const dateStr = date.toISOString().split('T')[0];

      const response = await axios.get(`${this.baseUrl}/user/-/activities/heart/date/${dateStr}/1d.json`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      const heartData = response.data['activities-heart'][0];
      if (!heartData || !heartData.value) {
        return null;
      }

      const zones = heartData.value.heartRateZones;

      return {
        date,
        restingHeartRate: heartData.value.restingHeartRate,
        heartRateZones: {
          fatBurn: {
            min: zones[1]?.min || 0,
            max: zones[1]?.max || 0,
            minutes: zones[1]?.minutes || 0,
          },
          cardio: {
            min: zones[2]?.min || 0,
            max: zones[2]?.max || 0,
            minutes: zones[2]?.minutes || 0,
          },
          peak: {
            min: zones[3]?.min || 0,
            max: zones[3]?.max || 0,
            minutes: zones[3]?.minutes || 0,
          },
        },
      };
    } catch (error) {
      logger.error('Error getting Fitbit heart rate data:', error);
      throw error;
    }
  }

  // Sync all data for a date range
  async syncData(userId: string, startDate: Date, endDate: Date): Promise<void> {
    try {
      const integration = await this.getIntegration(userId);
      if (!integration) {
        return;
      }

      const currentDate = new Date(startDate);
      
      while (currentDate <= endDate) {
        try {
          // Sync activity data
          const activityData = await this.getActivityData(userId, currentDate);
          await this.storeActivityData(userId, activityData);

          // Sync sleep data
          const sleepData = await this.getSleepData(userId, currentDate);
          if (sleepData) {
            await this.storeSleepData(userId, sleepData);
          }

          // Sync heart rate data
          const heartRateData = await this.getHeartRateData(userId, currentDate);
          if (heartRateData) {
            await this.storeHeartRateData(userId, heartRateData);
          }

          // Move to next day
          currentDate.setDate(currentDate.getDate() + 1);

          // Add delay to respect rate limits
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
          logger.error(`Error syncing Fitbit data for ${currentDate.toISOString()}:`, error);
          // Continue with next date
          currentDate.setDate(currentDate.getDate() + 1);
        }
      }

      // Update last sync time
      await prisma.integration.update({
        where: {
          userId_provider: {
            userId,
            provider: 'FITBIT',
          },
        },
        data: {
          lastSyncAt: new Date(),
        },
      });

      logger.info(`Fitbit data synced for user ${userId}`);
    } catch (error) {
      logger.error('Error syncing Fitbit data:', error);
      throw error;
    }
  }

  // Store activity data in database
  private async storeActivityData(userId: string, data: FitbitActivityData): Promise<void> {
    try {
      // Store as biometric data that can be used by energy analysis
      await prisma.energyData.upsert({
        where: {
          userId_timestamp: {
            userId,
            timestamp: data.date,
          },
        },
        update: {
          physicalActivity: data.activeMinutes,
          context: `Steps: ${data.steps}, Calories: ${data.calories}`,
        },
        create: {
          userId,
          timestamp: data.date,
          energyLevel: 3, // Default, will be updated by user input
          mood: 5, // Default
          stressLevel: 5, // Default
          physicalActivity: data.activeMinutes,
          context: `Steps: ${data.steps}, Calories: ${data.calories}`,
        },
      });
    } catch (error) {
      logger.error('Error storing Fitbit activity data:', error);
    }
  }

  // Store sleep data in database
  private async storeSleepData(userId: string, data: FitbitSleepData): Promise<void> {
    try {
      // Calculate sleep quality score (0-10)
      const sleepQuality = Math.min(10, Math.max(1, 
        (data.efficiency / 10) + (data.timeAsleep / 60) // Simplified calculation
      ));

      await prisma.energyData.upsert({
        where: {
          userId_timestamp: {
            userId,
            timestamp: data.date,
          },
        },
        update: {
          sleepQuality: Math.round(sleepQuality),
        },
        create: {
          userId,
          timestamp: data.date,
          energyLevel: 3, // Default
          mood: 5, // Default
          stressLevel: 5, // Default
          sleepQuality: Math.round(sleepQuality),
        },
      });
    } catch (error) {
      logger.error('Error storing Fitbit sleep data:', error);
    }
  }

  // Store heart rate data in database
  private async storeHeartRateData(userId: string, data: FitbitHeartRateData): Promise<void> {
    try {
      // Heart rate data could be used for stress level calculation
      // This is a simplified implementation
      logger.debug(`Storing heart rate data for user ${userId} on ${data.date.toISOString()}`);
    } catch (error) {
      logger.error('Error storing Fitbit heart rate data:', error);
    }
  }

  // Encrypt token (placeholder - use proper encryption in production)
  private encryptToken(token: string): string {
    return Buffer.from(token).toString('base64');
  }

  // Decrypt token (placeholder - use proper decryption in production)
  private decryptToken(encryptedToken: string): string {
    return Buffer.from(encryptedToken, 'base64').toString();
  }

  // Disconnect integration
  async disconnectIntegration(userId: string): Promise<void> {
    try {
      await prisma.integration.update({
        where: {
          userId_provider: {
            userId,
            provider: 'FITBIT',
          },
        },
        data: {
          isActive: false,
        },
      });

      logger.info(`Fitbit integration disconnected for user ${userId}`);
    } catch (error) {
      logger.error('Error disconnecting Fitbit integration:', error);
      throw error;
    }
  }

  // Check if user has active integration
  async hasActiveIntegration(userId: string): Promise<boolean> {
    try {
      const integration = await this.getIntegration(userId);
      return integration !== null;
    } catch (error) {
      logger.error('Error checking Fitbit integration status:', error);
      return false;
    }
  }
}
