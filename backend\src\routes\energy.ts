import { Router, Request, Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { EnergyAnalysisService, EnergyLevel } from '../services/energyAnalysisService';
import { logger } from '../utils/logger';

const router = Router();
const energyService = EnergyAnalysisService.getInstance();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// POST /api/energy/record - Record energy data point
router.post('/record',
  [
    body('energyLevel').isInt({ min: 1, max: 5 }).withMessage('Energy level must be between 1 and 5'),
    body('mood').isInt({ min: 1, max: 10 }).withMessage('Mood must be between 1 and 10'),
    body('stressLevel').isInt({ min: 1, max: 10 }).withMessage('Stress level must be between 1 and 10'),
    body('sleepQuality').optional().isInt({ min: 1, max: 10 }).withMessage('Sleep quality must be between 1 and 10'),
    body('physicalActivity').optional().isInt({ min: 0 }).withMessage('Physical activity must be a positive number'),
    body('caffeine').optional().isInt({ min: 0 }).withMessage('Caffeine must be a positive number'),
    body('context').optional().isString().isLength({ max: 500 }).withMessage('Context must be less than 500 characters'),
    body('timestamp').optional().isISO8601().withMessage('Timestamp must be a valid ISO 8601 date'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const {
        energyLevel,
        mood,
        stressLevel,
        sleepQuality,
        physicalActivity,
        caffeine,
        context,
        timestamp,
      } = req.body;

      const energyData = {
        timestamp: timestamp ? new Date(timestamp) : new Date(),
        energyLevel: energyLevel as EnergyLevel,
        mood,
        stressLevel,
        sleepQuality,
        physicalActivity,
        caffeine,
        context,
      };

      await energyService.recordEnergyData(userId, energyData);

      res.status(201).json({
        message: 'Energy data recorded successfully',
        data: energyData,
      });

      logger.info(`Energy data recorded for user ${userId}`);
    } catch (error) {
      logger.error('Error recording energy data:', error);
      res.status(500).json({
        error: 'Failed to record energy data',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/energy/data - Get energy data for date range
router.get('/data',
  [
    query('startDate').isISO8601().withMessage('Start date must be a valid ISO 8601 date'),
    query('endDate').isISO8601().withMessage('End date must be a valid ISO 8601 date'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { startDate, endDate } = req.query;

      const start = new Date(startDate as string);
      const end = new Date(endDate as string);

      // Validate date range
      if (start > end) {
        return res.status(400).json({
          error: 'Invalid date range',
          message: 'Start date must be before end date',
        });
      }

      // Limit to 90 days
      const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
      if (daysDiff > 90) {
        return res.status(400).json({
          error: 'Date range too large',
          message: 'Maximum date range is 90 days',
        });
      }

      const energyData = await energyService.getEnergyData(userId, start, end);

      res.json({
        data: energyData,
        count: energyData.length,
        dateRange: {
          startDate: start,
          endDate: end,
        },
      });
    } catch (error) {
      logger.error('Error getting energy data:', error);
      res.status(500).json({
        error: 'Failed to get energy data',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/energy/pattern - Get circadian rhythm pattern
router.get('/pattern', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const pattern = await energyService.getCircadianPattern(userId);

    if (!pattern) {
      return res.status(404).json({
        error: 'Pattern not found',
        message: 'No circadian pattern available. Please log energy levels for at least 2 weeks.',
      });
    }

    res.json({
      data: pattern,
    });
  } catch (error) {
    logger.error('Error getting circadian pattern:', error);
    res.status(500).json({
      error: 'Failed to get circadian pattern',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// POST /api/energy/pattern/update - Force update circadian pattern
router.post('/pattern/update', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const pattern = await energyService.updateCircadianPattern(userId);

    res.json({
      message: 'Circadian pattern updated successfully',
      data: pattern,
    });

    logger.info(`Circadian pattern updated for user ${userId}`);
  } catch (error) {
    logger.error('Error updating circadian pattern:', error);
    res.status(500).json({
      error: 'Failed to update circadian pattern',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/energy/predict - Predict energy level for specific time
router.get('/predict',
  [
    query('timestamp').isISO8601().withMessage('Timestamp must be a valid ISO 8601 date'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { timestamp } = req.query;

      const targetTime = new Date(timestamp as string);

      // Don't predict too far in the future (max 7 days)
      const maxFutureDate = new Date();
      maxFutureDate.setDate(maxFutureDate.getDate() + 7);

      if (targetTime > maxFutureDate) {
        return res.status(400).json({
          error: 'Invalid prediction time',
          message: 'Cannot predict more than 7 days in the future',
        });
      }

      const prediction = await energyService.predictEnergyLevel(userId, targetTime);

      res.json({
        data: prediction,
      });
    } catch (error) {
      logger.error('Error predicting energy level:', error);
      res.status(500).json({
        error: 'Failed to predict energy level',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/energy/insights - Get personalized energy insights
router.get('/insights', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const insights = await energyService.getEnergyInsights(userId);

    res.json({
      data: insights,
    });
  } catch (error) {
    logger.error('Error getting energy insights:', error);
    res.status(500).json({
      error: 'Failed to get energy insights',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/energy/today - Get today's energy prediction and recommendations
router.get('/today', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const today = new Date();

    // Get today's prediction
    const prediction = await energyService.predictEnergyLevel(userId, today);

    // Get circadian pattern for context
    const pattern = await energyService.getCircadianPattern(userId);

    // Get recent energy data
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const recentData = await energyService.getEnergyData(userId, yesterday, today);

    res.json({
      data: {
        prediction,
        pattern: pattern ? {
          peakEnergyHours: pattern.peakEnergyHours,
          lowEnergyHours: pattern.lowEnergyHours,
          confidence: pattern.confidence,
        } : null,
        recentData: recentData.slice(-5), // Last 5 data points
        recommendations: generateTodayRecommendations(prediction, pattern),
      },
    });
  } catch (error) {
    logger.error('Error getting today\'s energy data:', error);
    res.status(500).json({
      error: 'Failed to get today\'s energy data',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/energy/stats - Get energy statistics
router.get('/stats',
  [
    query('period').optional().isIn(['week', 'month', 'quarter']).withMessage('Period must be week, month, or quarter'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const period = (req.query.period as string) || 'month';

      const endDate = new Date();
      const startDate = new Date();

      switch (period) {
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(startDate.getMonth() - 3);
          break;
      }

      const energyData = await energyService.getEnergyData(userId, startDate, endDate);

      if (energyData.length === 0) {
        return res.json({
          data: {
            period,
            dataPoints: 0,
            averageEnergy: 0,
            averageMood: 0,
            averageStress: 0,
            trends: {},
          },
        });
      }

      // Calculate statistics
      const stats = {
        period,
        dataPoints: energyData.length,
        averageEnergy: energyData.reduce((sum, d) => sum + d.energyLevel, 0) / energyData.length,
        averageMood: energyData.reduce((sum, d) => sum + d.mood, 0) / energyData.length,
        averageStress: energyData.reduce((sum, d) => sum + d.stressLevel, 0) / energyData.length,
        trends: calculateTrends(energyData),
        distribution: calculateDistribution(energyData),
      };

      res.json({
        data: stats,
      });
    } catch (error) {
      logger.error('Error getting energy statistics:', error);
      res.status(500).json({
        error: 'Failed to get energy statistics',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// Helper function to generate today's recommendations
function generateTodayRecommendations(prediction: any, pattern: any): string[] {
  const recommendations: string[] = [];
  const currentHour = new Date().getHours();

  if (prediction.predictedEnergyLevel < 3) {
    recommendations.push('Your energy is predicted to be low today. Consider lighter tasks and more breaks.');
    recommendations.push('Ensure you stay hydrated and get some natural light.');
  } else if (prediction.predictedEnergyLevel > 4) {
    recommendations.push('High energy predicted! This is a great day for challenging tasks.');
    recommendations.push('Take advantage of your peak energy for important work.');
  }

  if (pattern && pattern.peakEnergyHours.includes(currentHour)) {
    recommendations.push('You\'re currently in a peak energy period. Focus on your most important tasks.');
  } else if (pattern && pattern.lowEnergyHours.includes(currentHour)) {
    recommendations.push('This is typically a low-energy time for you. Consider administrative tasks or breaks.');
  }

  return recommendations;
}

// Helper function to calculate trends
function calculateTrends(data: any[]): any {
  if (data.length < 2) return {};

  const firstHalf = data.slice(0, Math.floor(data.length / 2));
  const secondHalf = data.slice(Math.floor(data.length / 2));

  const firstAvgEnergy = firstHalf.reduce((sum, d) => sum + d.energyLevel, 0) / firstHalf.length;
  const secondAvgEnergy = secondHalf.reduce((sum, d) => sum + d.energyLevel, 0) / secondHalf.length;

  const firstAvgMood = firstHalf.reduce((sum, d) => sum + d.mood, 0) / firstHalf.length;
  const secondAvgMood = secondHalf.reduce((sum, d) => sum + d.mood, 0) / secondHalf.length;

  return {
    energyTrend: secondAvgEnergy > firstAvgEnergy ? 'improving' : 'declining',
    moodTrend: secondAvgMood > firstAvgMood ? 'improving' : 'declining',
    energyChange: ((secondAvgEnergy - firstAvgEnergy) / firstAvgEnergy * 100).toFixed(1),
    moodChange: ((secondAvgMood - firstAvgMood) / firstAvgMood * 100).toFixed(1),
  };
}

// Helper function to calculate distribution
function calculateDistribution(data: any[]): any {
  const energyDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
  
  data.forEach(d => {
    energyDistribution[d.energyLevel as keyof typeof energyDistribution]++;
  });

  // Convert to percentages
  Object.keys(energyDistribution).forEach(key => {
    energyDistribution[key as keyof typeof energyDistribution] = 
      (energyDistribution[key as keyof typeof energyDistribution] / data.length * 100);
  });

  return energyDistribution;
}

export default router;
