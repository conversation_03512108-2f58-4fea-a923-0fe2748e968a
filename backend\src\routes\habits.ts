import { Router, Request, Response } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { HabitFormationService, HabitCategory, HabitFrequency } from '../services/habitFormationService';
import { logger } from '../utils/logger';

const router = Router();
const habitService = HabitFormationService.getInstance();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// POST /api/habits - Create a new habit
router.post('/',
  [
    body('name').isString().trim().isLength({ min: 1, max: 100 }).withMessage('Habit name is required and must be less than 100 characters'),
    body('description').optional().isString().trim().isLength({ max: 500 }).withMessage('Description must be less than 500 characters'),
    body('category').isIn(['PRODUCTIVITY', 'HEALTH', 'LEARNING', 'MINDFULNESS', 'SOCIAL', 'CREATIVE', 'FINANCIAL']).withMessage('Invalid habit category'),
    body('frequency').isIn(['DAILY', 'WEEKLY', 'MONTHLY', 'CUSTOM']).withMessage('Invalid habit frequency'),
    body('targetValue').isInt({ min: 1 }).withMessage('Target value must be a positive integer'),
    body('unit').isString().trim().isLength({ min: 1, max: 20 }).withMessage('Unit is required and must be less than 20 characters'),
    body('preferredTime').optional().matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('Preferred time must be in HH:MM format'),
    body('reminderEnabled').isBoolean().withMessage('Reminder enabled must be a boolean'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const habitData = req.body;

      const habit = await habitService.createHabit(userId, habitData);

      res.status(201).json({
        message: 'Habit created successfully',
        data: habit,
      });

      logger.info(`Habit created for user ${userId}: ${habit.name}`);
    } catch (error) {
      logger.error('Error creating habit:', error);
      res.status(500).json({
        error: 'Failed to create habit',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/habits - Get user's habits
router.get('/',
  [
    query('activeOnly').optional().isBoolean().withMessage('activeOnly must be a boolean'),
    query('category').optional().isIn(['PRODUCTIVITY', 'HEALTH', 'LEARNING', 'MINDFULNESS', 'SOCIAL', 'CREATIVE', 'FINANCIAL']).withMessage('Invalid category'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { activeOnly = 'true', category } = req.query;

      let habits = await habitService.getUserHabits(userId, activeOnly === 'true');

      // Filter by category if specified
      if (category) {
        habits = habits.filter(habit => habit.category === category);
      }

      res.json({
        data: habits,
        count: habits.length,
      });
    } catch (error) {
      logger.error('Error getting user habits:', error);
      res.status(500).json({
        error: 'Failed to get habits',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/habits/:habitId - Get specific habit details
router.get('/:habitId',
  [
    param('habitId').isString().isLength({ min: 1 }).withMessage('Habit ID is required'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { habitId } = req.params;

      const habits = await habitService.getUserHabits(userId, false);
      const habit = habits.find(h => h.id === habitId);

      if (!habit) {
        return res.status(404).json({
          error: 'Habit not found',
          message: 'Habit not found or does not belong to user',
        });
      }

      // Get habit statistics
      const stats = await habitService.getHabitStats(userId, habitId);

      res.json({
        data: {
          ...habit,
          stats,
        },
      });
    } catch (error) {
      logger.error('Error getting habit details:', error);
      res.status(500).json({
        error: 'Failed to get habit details',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// PUT /api/habits/:habitId - Update habit
router.put('/:habitId',
  [
    param('habitId').isString().isLength({ min: 1 }).withMessage('Habit ID is required'),
    body('name').optional().isString().trim().isLength({ min: 1, max: 100 }).withMessage('Habit name must be less than 100 characters'),
    body('description').optional().isString().trim().isLength({ max: 500 }).withMessage('Description must be less than 500 characters'),
    body('category').optional().isIn(['PRODUCTIVITY', 'HEALTH', 'LEARNING', 'MINDFULNESS', 'SOCIAL', 'CREATIVE', 'FINANCIAL']).withMessage('Invalid habit category'),
    body('frequency').optional().isIn(['DAILY', 'WEEKLY', 'MONTHLY', 'CUSTOM']).withMessage('Invalid habit frequency'),
    body('targetValue').optional().isInt({ min: 1 }).withMessage('Target value must be a positive integer'),
    body('unit').optional().isString().trim().isLength({ min: 1, max: 20 }).withMessage('Unit must be less than 20 characters'),
    body('preferredTime').optional().matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('Preferred time must be in HH:MM format'),
    body('reminderEnabled').optional().isBoolean().withMessage('Reminder enabled must be a boolean'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { habitId } = req.params;
      const updates = req.body;

      const updatedHabit = await habitService.updateHabit(userId, habitId, updates);

      res.json({
        message: 'Habit updated successfully',
        data: updatedHabit,
      });

      logger.info(`Habit updated for user ${userId}: ${habitId}`);
    } catch (error) {
      logger.error('Error updating habit:', error);
      res.status(500).json({
        error: 'Failed to update habit',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// DELETE /api/habits/:habitId - Delete (deactivate) habit
router.delete('/:habitId',
  [
    param('habitId').isString().isLength({ min: 1 }).withMessage('Habit ID is required'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { habitId } = req.params;

      await habitService.deleteHabit(userId, habitId);

      res.json({
        message: 'Habit deleted successfully',
      });

      logger.info(`Habit deleted for user ${userId}: ${habitId}`);
    } catch (error) {
      logger.error('Error deleting habit:', error);
      res.status(500).json({
        error: 'Failed to delete habit',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// POST /api/habits/:habitId/complete - Record habit completion
router.post('/:habitId/complete',
  [
    param('habitId').isString().isLength({ min: 1 }).withMessage('Habit ID is required'),
    body('completedAt').optional().isISO8601().withMessage('Completed at must be a valid ISO 8601 date'),
    body('value').optional().isInt({ min: 1 }).withMessage('Value must be a positive integer'),
    body('notes').optional().isString().isLength({ max: 500 }).withMessage('Notes must be less than 500 characters'),
    body('mood').optional().isInt({ min: 1, max: 10 }).withMessage('Mood must be between 1 and 10'),
    body('difficulty').optional().isInt({ min: 1, max: 10 }).withMessage('Difficulty must be between 1 and 10'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { habitId } = req.params;
      const { completedAt, value = 1, notes, mood, difficulty } = req.body;

      const completion = {
        habitId,
        completedAt: completedAt ? new Date(completedAt) : new Date(),
        value,
        notes,
        mood,
        difficulty,
      };

      const habitCompletion = await habitService.recordHabitCompletion(userId, completion);

      res.status(201).json({
        message: 'Habit completion recorded successfully',
        data: habitCompletion,
      });

      logger.info(`Habit completion recorded for user ${userId}, habit ${habitId}`);
    } catch (error) {
      logger.error('Error recording habit completion:', error);
      res.status(500).json({
        error: 'Failed to record habit completion',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/habits/:habitId/stats - Get habit statistics
router.get('/:habitId/stats',
  [
    param('habitId').isString().isLength({ min: 1 }).withMessage('Habit ID is required'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { habitId } = req.params;

      const stats = await habitService.getHabitStats(userId, habitId);

      res.json({
        data: stats,
      });
    } catch (error) {
      logger.error('Error getting habit statistics:', error);
      res.status(500).json({
        error: 'Failed to get habit statistics',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/habits/insights - Get habit insights and recommendations
router.get('/insights/all', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const insights = await habitService.generateHabitInsights(userId);

    res.json({
      data: insights,
      count: insights.length,
    });
  } catch (error) {
    logger.error('Error getting habit insights:', error);
    res.status(500).json({
      error: 'Failed to get habit insights',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/habits/summary - Get habits summary
router.get('/summary/overview', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const habits = await habitService.getUserHabits(userId, true);
    const allStats = await Promise.all(
      habits.map(habit => habitService.getHabitStats(userId, habit.id))
    );

    // Calculate summary statistics
    const totalHabits = habits.length;
    const averageSuccessRate = allStats.length > 0 
      ? allStats.reduce((sum, stat) => sum + stat.successRate, 0) / allStats.length 
      : 0;
    const totalStreaks = allStats.reduce((sum, stat) => sum + stat.currentStreak, 0);
    const longestStreak = Math.max(...allStats.map(stat => stat.longestStreak), 0);
    const totalCompletions = allStats.reduce((sum, stat) => sum + stat.totalCompletions, 0);

    // Category breakdown
    const categoryBreakdown: { [key: string]: number } = {};
    habits.forEach(habit => {
      categoryBreakdown[habit.category] = (categoryBreakdown[habit.category] || 0) + 1;
    });

    // Recent activity (habits completed in last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentActivity = allStats.filter(stat => 
      stat.lastCompletion && new Date(stat.lastCompletion) >= sevenDaysAgo
    ).length;

    const summary = {
      totalHabits,
      averageSuccessRate: Math.round(averageSuccessRate * 100) / 100,
      totalStreaks,
      longestStreak,
      totalCompletions,
      categoryBreakdown,
      recentActivity,
      habitsNeedingAttention: allStats.filter(stat => stat.successRate < 0.5).length,
    };

    res.json({
      data: summary,
    });
  } catch (error) {
    logger.error('Error getting habits summary:', error);
    res.status(500).json({
      error: 'Failed to get habits summary',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/habits/:habitId/completions - Get habit completion history
router.get('/:habitId/completions',
  [
    param('habitId').isString().isLength({ min: 1 }).withMessage('Habit ID is required'),
    query('startDate').optional().isISO8601().withMessage('Start date must be a valid ISO 8601 date'),
    query('endDate').optional().isISO8601().withMessage('End date must be a valid ISO 8601 date'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const { habitId } = req.params;
      const { startDate, endDate, limit = '30' } = req.query;

      // Verify habit belongs to user
      const habits = await habitService.getUserHabits(userId, false);
      const habit = habits.find(h => h.id === habitId);

      if (!habit) {
        return res.status(404).json({
          error: 'Habit not found',
          message: 'Habit not found or does not belong to user',
        });
      }

      // Get completions (this would need to be implemented in the service)
      // For now, return the completions from the habit object
      let completions = habit.completions || [];

      // Filter by date range if provided
      if (startDate || endDate) {
        const start = startDate ? new Date(startDate as string) : new Date(0);
        const end = endDate ? new Date(endDate as string) : new Date();
        
        completions = completions.filter(completion => {
          const completionDate = new Date(completion.completedAt);
          return completionDate >= start && completionDate <= end;
        });
      }

      // Apply limit
      completions = completions.slice(0, parseInt(limit as string));

      res.json({
        data: completions,
        count: completions.length,
        habitId,
      });
    } catch (error) {
      logger.error('Error getting habit completions:', error);
      res.status(500).json({
        error: 'Failed to get habit completions',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

export default router;
