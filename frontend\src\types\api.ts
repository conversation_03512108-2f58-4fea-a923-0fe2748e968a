// Base API types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  error?: string;
  count?: number;
  totalCount?: number;
  hasMore?: boolean;
}

export interface ApiError {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
  path: string;
  method: string;
  details?: any[];
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface DateRangeParams {
  startDate: string;
  endDate: string;
}

// User types
export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  displayName: string;
  avatar?: string;
  timezone: string;
  role: 'USER' | 'ADMIN';
  isActive: boolean;
  isEmailVerified: boolean;
  isPremium: boolean;
  onboardingCompleted: boolean;
  createdAt: string;
  lastLoginAt?: string;
}

export interface UserPreferences {
  id: string;
  userId: string;
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  dataSharing: boolean;
  analyticsOptIn: boolean;
  theme: 'light' | 'dark' | 'auto';
  language: string;
  workingHours: {
    start: string;
    end: string;
  };
  workingDays: string[];
  energyReminderFreq: number;
  energyReminderEnabled: boolean;
  createdAt: string;
  updatedAt: string;
}

// Authentication types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  timezone?: string;
}

export interface AuthResponse {
  user: User;
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
  sessionId: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

// Energy types
export type EnergyLevel = 1 | 2 | 3 | 4 | 5;

export interface EnergyData {
  id: string;
  userId: string;
  timestamp: string;
  energyLevel: EnergyLevel;
  mood: number;
  stressLevel: number;
  sleepQuality?: number;
  physicalActivity?: number;
  caffeine?: number;
  context?: string;
  createdAt: string;
}

export interface EnergyDataRequest {
  energyLevel: EnergyLevel;
  mood: number;
  stressLevel: number;
  sleepQuality?: number;
  physicalActivity?: number;
  caffeine?: number;
  context?: string;
  timestamp?: string;
}

export interface CircadianPattern {
  userId: string;
  peakEnergyHours: number[];
  lowEnergyHours: number[];
  averageEnergyByHour: { [hour: number]: number };
  confidence: number;
  dataPoints: number;
  lastUpdated: string;
}

export interface EnergyPrediction {
  timestamp: string;
  predictedEnergyLevel: number;
  confidence: number;
  factors: {
    timeOfDay: number;
    historicalPattern: number;
    recentTrend: number;
    sleepQuality?: number;
  };
}

// Flow types
export type FlowIntensity = 'light' | 'moderate' | 'deep' | 'peak';

export interface FlowSession {
  id: string;
  userId: string;
  taskType: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  intensity: FlowIntensity;
  productivity: number;
  satisfaction: number;
  interruptions: number;
  triggers: string[];
  environment: FlowEnvironment;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface FlowEnvironment {
  location: string;
  noiseLevel: number;
  lighting: string;
  temperature?: number;
  musicType?: string;
  distractions: string[];
}

export interface FlowSessionRequest {
  taskType: string;
  environment: FlowEnvironment;
}

export interface FlowSessionUpdate {
  intensity?: FlowIntensity;
  interruptions?: number;
  productivity?: number;
  satisfaction?: number;
  triggers?: string[];
  notes?: string;
}

export interface FlowPattern {
  userId: string;
  optimalConditions: {
    timeOfDay: number[];
    duration: number;
    environment: Partial<FlowEnvironment>;
  };
  successRate: number;
  averageIntensity: FlowIntensity;
  commonTriggers: string[];
  lastUpdated: string;
}

// Habit types
export type HabitCategory = 'PRODUCTIVITY' | 'HEALTH' | 'LEARNING' | 'MINDFULNESS' | 'SOCIAL' | 'CREATIVE' | 'FINANCIAL';
export type HabitFrequency = 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'CUSTOM';

export interface Habit {
  id: string;
  userId: string;
  name: string;
  description?: string;
  category: HabitCategory;
  frequency: HabitFrequency;
  targetValue: number;
  unit: string;
  preferredTime?: string;
  reminderEnabled: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  completions?: HabitCompletion[];
}

export interface HabitCompletion {
  id: string;
  habitId: string;
  completedAt: string;
  value: number;
  notes?: string;
  mood?: number;
  difficulty?: number;
  createdAt: string;
}

export interface HabitRequest {
  name: string;
  description?: string;
  category: HabitCategory;
  frequency: HabitFrequency;
  targetValue: number;
  unit: string;
  preferredTime?: string;
  reminderEnabled: boolean;
}

export interface HabitCompletionRequest {
  habitId: string;
  completedAt?: string;
  value?: number;
  notes?: string;
  mood?: number;
  difficulty?: number;
}

export interface HabitStats {
  habitId: string;
  currentStreak: number;
  longestStreak: number;
  totalCompletions: number;
  successRate: number;
  averageValue: number;
  lastCompletion?: string;
  weeklyProgress: number;
  monthlyProgress: number;
}

// Task types
export type TaskType = 'WORK' | 'PERSONAL' | 'HEALTH' | 'LEARNING' | 'CREATIVE' | 'ADMINISTRATIVE' | 'SOCIAL';
export type TaskPriority = 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
export type TaskStatus = 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'RESCHEDULED';

export interface Task {
  id: string;
  userId: string;
  title: string;
  description?: string;
  taskType: TaskType;
  priority: TaskPriority;
  status: TaskStatus;
  estimatedDuration?: number;
  actualDuration?: number;
  energyRequired: number;
  focusRequired: boolean;
  dueDate?: string;
  scheduledAt?: string;
  completedAt?: string;
  productivity?: number;
  satisfaction?: number;
  createdAt: string;
  updatedAt: string;
}

export interface TaskRequest {
  title: string;
  description?: string;
  taskType?: TaskType;
  priority?: TaskPriority;
  estimatedDuration?: number;
  energyRequired?: number;
  focusRequired?: boolean;
  dueDate?: string;
  scheduledAt?: string;
}

// Coaching types
export type InsightType = 'energy_pattern' | 'flow_optimization' | 'habit_progress' | 'schedule_efficiency' | 'wellness_alert' | 'achievement' | 'trend_analysis';
export type CoachingType = 'DAILY_CHECKIN' | 'WEEKLY_REVIEW' | 'MONTHLY_ANALYSIS' | 'GOAL_SETTING' | 'HABIT_COACHING' | 'ENERGY_OPTIMIZATION';

export interface CoachingInsight {
  id: string;
  type: InsightType;
  title: string;
  message: string;
  data: any;
  priority: 'low' | 'medium' | 'high';
  actionable: boolean;
  recommendations: string[];
  createdAt: string;
}

export interface Recommendation {
  id: string;
  category: 'energy' | 'flow' | 'habits' | 'scheduling' | 'wellness';
  title: string;
  description: string;
  actionSteps: string[];
  expectedImpact: 'low' | 'medium' | 'high';
  timeframe: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface CoachingSession {
  id: string;
  userId: string;
  sessionType: CoachingType;
  insights: CoachingInsight[];
  recommendations: Recommendation[];
  goals: any[];
  metrics: {
    productivityScore: number;
    wellnessScore: number;
    progressScore: number;
    energyOptimization: number;
    habitConsistency: number;
    flowStateUtilization: number;
  };
  createdAt: string;
}

// Integration types
export type IntegrationProvider = 'GOOGLE_CALENDAR' | 'FITBIT' | 'APPLE_HEALTH' | 'SPOTIFY' | 'SLACK';

export interface Integration {
  id: string;
  provider: IntegrationProvider;
  isActive: boolean;
  lastSyncAt?: string;
  createdAt: string;
  updatedAt: string;
  status: 'connected' | 'disconnected';
  lastSyncStatus: 'recent' | 'stale' | 'never';
}

export interface AvailableIntegration {
  provider: IntegrationProvider;
  name: string;
  description: string;
  category: string;
  features: string[];
  isConnected: boolean;
  authUrl?: string;
}

// Dashboard types
export interface DashboardData {
  user: Pick<User, 'id' | 'displayName' | 'onboardingCompleted' | 'isPremium'>;
  stats: {
    recentEnergyLogs: number;
    recentFlowSessions: number;
    activeHabits: number;
    pendingTasks: number;
    completedTasksThisWeek: number;
    recentCoachingSessions: number;
    activeIntegrations: number;
  };
  needsAttention: {
    onboardingIncomplete: boolean;
    noRecentEnergyData: boolean;
    noActiveHabits: boolean;
    manyPendingTasks: boolean;
  };
}
