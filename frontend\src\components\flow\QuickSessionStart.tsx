'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Target, Play, Clock } from 'lucide-react';
import { useFlowSessionManager } from '@/hooks/useFlowSession';
import { SessionTypeSelector } from './SessionTypeSelector';
import { SessionType, SESSION_TYPES } from '@/types/flow-session';
import { cn } from '@/lib/utils';

interface QuickSessionStartProps {
  className?: string;
  onSessionStarted?: () => void;
}

export function QuickSessionStart({ className, onSessionStarted }: QuickSessionStartProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedType, setSelectedType] = useState<SessionType>(SESSION_TYPES[0]);
  const router = useRouter();
  
  const { startSession, isStarting, sessionState } = useFlowSessionManager();

  const handleQuickStart = async () => {
    try {
      const environment = {
        location: 'home-office',
        noiseLevel: 3,
        lighting: 'natural',
        temperature: 22,
        musicType: 'none',
        distractions: []
      };

      await startSession(selectedType, environment);
      onSessionStarted?.();
      router.push('/app/flow');
    } catch (error) {
      console.error('Failed to start session:', error);
    }
  };

  const handleGoToFlow = () => {
    router.push('/app/flow');
  };

  // If there's already an active session, show go to flow button
  if (sessionState !== 'idle') {
    return (
      <button
        onClick={handleGoToFlow}
        className={cn(
          'flex items-center space-x-2 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors',
          className
        )}
      >
        <Target className="w-4 h-4" />
        <span>Go to Active Session</span>
      </button>
    );
  }

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className={cn(
          'flex items-center space-x-2 px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors',
          className
        )}
      >
        <Target className="w-4 h-4" />
        <span>Start Flow Session</span>
      </button>
    );
  }

  return (
    <div className={cn('bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6', className)}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Quick Start Session
        </h3>
        <button
          onClick={() => setIsOpen(false)}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          ×
        </button>
      </div>

      <div className="space-y-4">
        <SessionTypeSelector
          selectedType={selectedType}
          onSelect={setSelectedType}
          compact
          showDuration
        />

        <div className="flex space-x-3">
          <button
            onClick={handleQuickStart}
            disabled={isStarting}
            className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Play className="w-4 h-4" />
            <span>{isStarting ? 'Starting...' : 'Quick Start'}</span>
          </button>

          <button
            onClick={handleGoToFlow}
            className="flex items-center justify-center space-x-2 px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            <Clock className="w-4 h-4" />
            <span>Full Setup</span>
          </button>
        </div>

        <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
          Quick start uses default environment settings
        </div>
      </div>
    </div>
  );
}
