'use client';

import React from 'react';
import { Play, Pause, Square, RotateCcw, Clock, Target } from 'lucide-react';
import { useFlowSessionManager } from '@/hooks/useFlowSession';
import { cn, formatDuration } from '@/lib/utils';
import { LoadingWrapper, ErrorMessage } from '@/components/common/ErrorBoundary';

interface SessionTimerProps {
  className?: string;
  showControls?: boolean;
  compact?: boolean;
}

export function SessionTimer({ className, showControls = true, compact = false }: SessionTimerProps) {
  const {
    sessionState,
    timerState,
    progress,
    currentSession,
    pauseSession,
    resumeSession,
    completeSession,
    cancelSession,
    isEnding,
    error,
    isInitializing,
    clearError
  } = useFlowSessionManager();

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimerColor = () => {
    switch (sessionState) {
      case 'active':
        return 'text-green-600 dark:text-green-400';
      case 'paused':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'completed':
        return 'text-blue-600 dark:text-blue-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getProgressColor = () => {
    if (progress.percentage >= 90) return 'bg-green-500';
    if (progress.percentage >= 75) return 'bg-blue-500';
    if (progress.percentage >= 50) return 'bg-yellow-500';
    return 'bg-purple-500';
  };

  return (
    <LoadingWrapper
      isLoading={isInitializing}
      error={error}
      onRetry={clearError}
      loadingText="Loading session..."
      className={className}
    >
      {error && (
        <div className="mb-4">
          <ErrorMessage error={error} onRetry={clearError} />
        </div>
      )}

      {(sessionState === 'idle' || !currentSession) && !error ? (
        <div className={cn('flex items-center justify-center p-8 text-center', className)}>
          <div className="space-y-4">
            <div className="w-16 h-16 mx-auto rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
              <Clock className="w-8 h-8 text-gray-400" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">No Active Session</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">Start a flow session to begin tracking your time</p>
            </div>
          </div>
        </div>
      ) : currentSession ? (
        <SessionTimerContent
          sessionState={sessionState}
          timerState={timerState}
          progress={progress}
          currentSession={currentSession}
          pauseSession={pauseSession}
          resumeSession={resumeSession}
          completeSession={completeSession}
          cancelSession={cancelSession}
          isEnding={isEnding}
          showControls={showControls}
          compact={compact}
          className={className}
        />
      ) : null}
    </LoadingWrapper>
  );
}

function SessionTimerContent({
  sessionState,
  timerState,
  progress,
  currentSession,
  pauseSession,
  resumeSession,
  completeSession,
  cancelSession,
  isEnding,
  showControls,
  compact,
  className
}: any) {

  if (compact) {
    return (
      <div className={cn('flex items-center space-x-3', className)}>
        <div className="flex items-center space-x-2">
          <div className={cn('w-2 h-2 rounded-full', {
            'bg-green-500 animate-pulse': sessionState === 'active',
            'bg-yellow-500': sessionState === 'paused',
            'bg-blue-500': sessionState === 'completed'
          })} />
          <span className={cn('text-sm font-mono', getTimerColor())}>
            {formatTime(progress.elapsedTime)}
          </span>
        </div>
        
        {showControls && (
          <div className="flex items-center space-x-1">
            {timerState === 'running' ? (
              <button
                onClick={pauseSession}
                className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                title="Pause session"
              >
                <Pause className="w-4 h-4" />
              </button>
            ) : (
              <button
                onClick={resumeSession}
                className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                title="Resume session"
              >
                <Play className="w-4 h-4" />
              </button>
            )}
            
            <button
              onClick={completeSession}
              disabled={isEnding}
              className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors disabled:opacity-50"
              title="Complete session"
            >
              <Square className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn('bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6', className)}>
      {/* Session Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className={cn('w-3 h-3 rounded-full', {
            'bg-green-500 animate-pulse': sessionState === 'active',
            'bg-yellow-500': sessionState === 'paused',
            'bg-blue-500': sessionState === 'completed'
          })} />
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {currentSession.sessionType.name}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {sessionState === 'active' && 'In Progress'}
              {sessionState === 'paused' && 'Paused'}
              {sessionState === 'completed' && 'Completed'}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
          <Target className="w-4 h-4" />
          <span>{currentSession.sessionType.description}</span>
        </div>
      </div>

      {/* Timer Display */}
      <div className="text-center mb-6">
        <div className={cn('text-4xl font-mono font-bold mb-2', getTimerColor())}>
          {formatTime(progress.elapsedTime)}
        </div>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {progress.remainingTime > 0 ? (
            <>Remaining: {formatTime(progress.remainingTime)}</>
          ) : (
            <>Session completed!</>
          )}
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-2">
          <span>Progress</span>
          <span>{Math.round(progress.percentage)}%</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className={cn('h-2 rounded-full transition-all duration-300', getProgressColor())}
            style={{ width: `${progress.percentage}%` }}
          />
        </div>
      </div>

      {/* Milestones */}
      {progress.milestones.length > 0 && (
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Milestones</span>
          </div>
          <div className="grid grid-cols-2 gap-2">
            {progress.milestones.map((milestone) => (
              <div
                key={milestone.id}
                className={cn('flex items-center space-x-2 text-xs p-2 rounded', {
                  'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300': milestone.reached,
                  'bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400': !milestone.reached
                })}
              >
                <div className={cn('w-2 h-2 rounded-full', {
                  'bg-green-500': milestone.reached,
                  'bg-gray-300 dark:bg-gray-600': !milestone.reached
                })} />
                <span>{milestone.name}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Controls */}
      {showControls && (
        <div className="flex items-center justify-center space-x-3">
          {timerState === 'running' ? (
            <button
              onClick={pauseSession}
              className="flex items-center space-x-2 px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg transition-colors"
            >
              <Pause className="w-4 h-4" />
              <span>Pause</span>
            </button>
          ) : (
            <button
              onClick={resumeSession}
              className="flex items-center space-x-2 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
            >
              <Play className="w-4 h-4" />
              <span>Resume</span>
            </button>
          )}
          
          <button
            onClick={completeSession}
            disabled={isEnding}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Square className="w-4 h-4" />
            <span>{isEnding ? 'Completing...' : 'Complete'}</span>
          </button>
          
          <button
            onClick={cancelSession}
            disabled={isEnding}
            className="flex items-center space-x-2 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RotateCcw className="w-4 h-4" />
            <span>Cancel</span>
          </button>
        </div>
      )}
    </div>
  );
}
