import { FlowSession, FlowEnvironment, FlowIntensity } from './api';

// Session Management States
export type SessionState = 'idle' | 'active' | 'paused' | 'completed' | 'break';
export type TimerState = 'stopped' | 'running' | 'paused';
export type BreakType = 'short' | 'long' | 'custom';

// Session Types and Configurations
export interface SessionType {
  id: string;
  name: string;
  description: string;
  icon: string;
  defaultDuration: number; // in minutes
  suggestedBreakInterval: number; // in minutes
  color: string;
  category: SessionCategory;
}

export type SessionCategory = 'focus' | 'creative' | 'learning' | 'planning' | 'communication' | 'maintenance';

// Predefined session types
export const SESSION_TYPES: SessionType[] = [
  {
    id: 'deep-focus',
    name: 'Deep Focus',
    description: 'Intensive focused work requiring high concentration',
    icon: 'Target',
    defaultDuration: 90,
    suggestedBreakInterval: 25,
    color: 'purple',
    category: 'focus'
  },
  {
    id: 'creative-work',
    name: 'Creative Work',
    description: 'Creative tasks like design, writing, or brainstorming',
    icon: 'Palette',
    defaultDuration: 60,
    suggestedBreakInterval: 30,
    color: 'pink',
    category: 'creative'
  },
  {
    id: 'learning',
    name: 'Learning',
    description: 'Study sessions, reading, or skill development',
    icon: 'BookOpen',
    defaultDuration: 45,
    suggestedBreakInterval: 25,
    color: 'blue',
    category: 'learning'
  },
  {
    id: 'planning',
    name: 'Planning',
    description: 'Strategic planning, goal setting, or organizing',
    icon: 'Calendar',
    defaultDuration: 30,
    suggestedBreakInterval: 15,
    color: 'green',
    category: 'planning'
  },
  {
    id: 'communication',
    name: 'Communication',
    description: 'Emails, meetings, or collaborative work',
    icon: 'MessageCircle',
    defaultDuration: 25,
    suggestedBreakInterval: 10,
    color: 'orange',
    category: 'communication'
  },
  {
    id: 'maintenance',
    name: 'Maintenance',
    description: 'Routine tasks, admin work, or cleanup',
    icon: 'Settings',
    defaultDuration: 20,
    suggestedBreakInterval: 10,
    color: 'gray',
    category: 'maintenance'
  }
];

// Timer Configuration
export interface TimerConfig {
  duration: number; // in minutes
  breakInterval?: number; // in minutes
  shortBreakDuration: number; // in minutes
  longBreakDuration: number; // in minutes
  longBreakInterval: number; // number of sessions before long break
  autoStartBreaks: boolean;
  autoStartSessions: boolean;
  soundEnabled: boolean;
  notificationsEnabled: boolean;
}

// Break Timer Configuration
export interface BreakTimer {
  type: BreakType;
  duration: number; // in minutes
  startTime?: Date;
  endTime?: Date;
  isActive: boolean;
  suggestions: string[];
}

// Ambient Sound Configuration
export interface AmbientSound {
  id: string;
  name: string;
  category: SoundCategory;
  url: string;
  volume: number; // 0-100
  isLooping: boolean;
  description?: string;
}

export type SoundCategory = 'nature' | 'white-noise' | 'instrumental' | 'binaural' | 'ambient';

export const AMBIENT_SOUNDS: AmbientSound[] = [
  {
    id: 'rain',
    name: 'Rain',
    category: 'nature',
    url: '/sounds/rain.mp3',
    volume: 50,
    isLooping: true,
    description: 'Gentle rain sounds for focus'
  },
  {
    id: 'forest',
    name: 'Forest',
    category: 'nature',
    url: '/sounds/forest.mp3',
    volume: 50,
    isLooping: true,
    description: 'Peaceful forest ambience'
  },
  {
    id: 'white-noise',
    name: 'White Noise',
    category: 'white-noise',
    url: '/sounds/white-noise.mp3',
    volume: 40,
    isLooping: true,
    description: 'Classic white noise for concentration'
  },
  {
    id: 'focus-music',
    name: 'Focus Music',
    category: 'instrumental',
    url: '/sounds/focus-music.mp3',
    volume: 30,
    isLooping: true,
    description: 'Instrumental music for deep work'
  },
  {
    id: 'binaural-focus',
    name: 'Binaural Focus',
    category: 'binaural',
    url: '/sounds/binaural-focus.mp3',
    volume: 35,
    isLooping: true,
    description: 'Binaural beats for enhanced focus'
  }
];

// Session Progress Tracking
export interface SessionProgress {
  elapsedTime: number; // in seconds
  remainingTime: number; // in seconds
  totalTime: number; // in seconds
  percentage: number; // 0-100
  milestones: SessionMilestone[];
  currentMilestone?: SessionMilestone;
}

export interface SessionMilestone {
  id: string;
  name: string;
  percentage: number; // 0-100
  reached: boolean;
  timestamp?: Date;
  message?: string;
}

// Extended Flow Session for UI Management
export interface FlowSessionUI extends FlowSession {
  state: SessionState;
  timerState: TimerState;
  progress: SessionProgress;
  breakTimer?: BreakTimer;
  ambientSound?: AmbientSound;
  sessionType: SessionType;
  linkedTaskId?: string;
}

// Session Preferences
export interface SessionPreferences {
  defaultSessionType: string;
  timerConfig: TimerConfig;
  ambientSoundEnabled: boolean;
  defaultAmbientSound?: string;
  breakRemindersEnabled: boolean;
  focusModeEnabled: boolean; // blocks distractions
  sessionGoals: SessionGoal[];
}

export interface SessionGoal {
  id: string;
  name: string;
  target: number; // in minutes per day/week
  period: 'daily' | 'weekly' | 'monthly';
  sessionTypes: string[]; // session type IDs
  isActive: boolean;
}

// Session Analytics
export interface SessionAnalytics {
  totalSessions: number;
  totalTime: number; // in minutes
  averageSessionLength: number; // in minutes
  completionRate: number; // 0-100
  mostProductiveTime: number[]; // hours of day
  favoriteSessionTypes: { type: string; count: number; totalTime: number }[];
  weeklyProgress: { date: string; sessions: number; totalTime: number }[];
  streakDays: number;
  longestSession: number; // in minutes
}

// Session Events for tracking
export interface SessionEvent {
  id: string;
  sessionId: string;
  type: SessionEventType;
  timestamp: Date;
  data?: any;
}

export type SessionEventType = 
  | 'session_started'
  | 'session_paused'
  | 'session_resumed'
  | 'session_completed'
  | 'session_cancelled'
  | 'break_started'
  | 'break_completed'
  | 'break_skipped'
  | 'interruption_logged'
  | 'milestone_reached'
  | 'ambient_sound_changed'
  | 'intensity_updated';

// Local Storage Keys
export const STORAGE_KEYS = {
  SESSION_PREFERENCES: 'rhythmai_session_preferences',
  ACTIVE_SESSION: 'rhythmai_active_session',
  SESSION_HISTORY: 'rhythmai_session_history',
  AMBIENT_SOUND_SETTINGS: 'rhythmai_ambient_sound_settings',
} as const;
