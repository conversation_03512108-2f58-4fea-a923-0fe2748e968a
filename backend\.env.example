# Environment Configuration
NODE_ENV=development
PORT=8000

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/rhythmai_dev"
DATABASE_POOL_SIZE=10

# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=""
REDIS_DB=0

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-change-this-in-production"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Frontend Configuration
FRONTEND_URL="http://localhost:3000"
CORS_ORIGINS="http://localhost:3000,http://localhost:3001"

# Email Configuration (for notifications and password reset)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"
FROM_NAME="RhythmAI"

# Google Calendar Integration
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_REDIRECT_URI="http://localhost:8000/api/integrations/google-calendar/callback"

# Fitbit Integration
FITBIT_CLIENT_ID="your-fitbit-client-id"
FITBIT_CLIENT_SECRET="your-fitbit-client-secret"
FITBIT_REDIRECT_URI="http://localhost:8000/api/integrations/fitbit/callback"

# Apple Health Integration (for future implementation)
APPLE_HEALTH_CLIENT_ID=""
APPLE_HEALTH_CLIENT_SECRET=""

# Spotify Integration (for future implementation)
SPOTIFY_CLIENT_ID=""
SPOTIFY_CLIENT_SECRET=""
SPOTIFY_REDIRECT_URI=""

# Slack Integration (for future implementation)
SLACK_CLIENT_ID=""
SLACK_CLIENT_SECRET=""
SLACK_REDIRECT_URI=""

# Notion Integration (for future implementation)
NOTION_CLIENT_ID=""
NOTION_CLIENT_SECRET=""
NOTION_REDIRECT_URI=""

# OpenAI Configuration (for AI coaching features)
OPENAI_API_KEY="your-openai-api-key"
OPENAI_MODEL="gpt-4"
OPENAI_MAX_TOKENS=1000

# Logging Configuration
LOG_LEVEL="debug"
LOG_FILE_PATH="./logs"
LOG_MAX_SIZE="10m"
LOG_MAX_FILES="5"

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_AUTH_MAX=10

# Session Configuration
SESSION_SECRET="your-session-secret-change-this"
SESSION_TIMEOUT=3600000

# File Upload Configuration
MAX_FILE_SIZE="10mb"
UPLOAD_PATH="./uploads"
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif"

# Webhook Configuration
WEBHOOK_SECRET="your-webhook-secret"

# Analytics Configuration
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=365

# Feature Flags
FEATURE_FLOW_DETECTION=true
FEATURE_AI_COACHING=true
FEATURE_INTEGRATIONS=true
FEATURE_PREMIUM_FEATURES=true
FEATURE_ANALYTICS=true

# Monitoring Configuration
SENTRY_DSN=""
HEALTH_CHECK_INTERVAL=30000

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Machine Learning Configuration
ML_MODEL_PATH="./models"
ML_PREDICTION_CONFIDENCE_THRESHOLD=0.7

# Notification Configuration
PUSH_NOTIFICATION_KEY=""
PUSH_NOTIFICATION_CERT=""

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=""
BACKUP_S3_REGION=""
BACKUP_S3_ACCESS_KEY=""
BACKUP_S3_SECRET_KEY=""

# Development Configuration
DEBUG_SQL=false
DEBUG_REDIS=false
DEBUG_AUTH=false

# Testing Configuration
TEST_DATABASE_URL="postgresql://username:password@localhost:5432/rhythmai_test"
TEST_REDIS_URL="redis://localhost:6379/1"

# Production Configuration (only set in production)
# SSL_CERT_PATH=""
# SSL_KEY_PATH=""
# CLUSTER_WORKERS=4

# Third-party Service URLs
GOOGLE_APIS_BASE_URL="https://www.googleapis.com"
FITBIT_API_BASE_URL="https://api.fitbit.com"
SPOTIFY_API_BASE_URL="https://api.spotify.com"

# Timezone Configuration
DEFAULT_TIMEZONE="UTC"
SUPPORTED_TIMEZONES="UTC,America/New_York,America/Los_Angeles,Europe/London,Europe/Paris,Asia/Tokyo"

# Localization Configuration
DEFAULT_LANGUAGE="en"
SUPPORTED_LANGUAGES="en,es,fr,de,ja"

# Performance Configuration
REQUEST_TIMEOUT=30000
DATABASE_TIMEOUT=10000
REDIS_TIMEOUT=5000

# Security Headers Configuration
HSTS_MAX_AGE=31536000
CSP_REPORT_URI=""

# API Versioning
API_VERSION="v1"
API_DEPRECATION_NOTICE=""

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="RhythmAI is currently undergoing maintenance. Please try again later."

# Documentation
API_DOCS_ENABLED=true
API_DOCS_PATH="/api/docs"
