import { useEffect, useCallback, useRef } from 'react';

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  description: string;
  action: () => void;
  preventDefault?: boolean;
  enabled?: boolean;
}

export interface ShortcutGroup {
  name: string;
  shortcuts: KeyboardShortcut[];
}

export function useKeyboardShortcuts(shortcuts: KeyboardShortcut[], enabled: boolean = true) {
  const shortcutsRef = useRef<KeyboardShortcut[]>([]);
  
  // Update shortcuts ref when shortcuts change
  useEffect(() => {
    shortcutsRef.current = shortcuts;
  }, [shortcuts]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    // Don't trigger shortcuts when user is typing in input fields
    const target = event.target as HTMLElement;
    if (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.contentEditable === 'true' ||
      target.isContentEditable
    ) {
      return;
    }

    const activeShortcuts = shortcutsRef.current.filter(shortcut => 
      shortcut.enabled !== false
    );

    for (const shortcut of activeShortcuts) {
      const keyMatches = event.key.toLowerCase() === shortcut.key.toLowerCase() ||
                        event.code.toLowerCase() === shortcut.key.toLowerCase();
      
      const ctrlMatches = !!shortcut.ctrlKey === event.ctrlKey;
      const shiftMatches = !!shortcut.shiftKey === event.shiftKey;
      const altMatches = !!shortcut.altKey === event.altKey;
      const metaMatches = !!shortcut.metaKey === event.metaKey;

      if (keyMatches && ctrlMatches && shiftMatches && altMatches && metaMatches) {
        if (shortcut.preventDefault !== false) {
          event.preventDefault();
        }
        shortcut.action();
        break; // Only execute the first matching shortcut
      }
    }
  }, [enabled]);

  useEffect(() => {
    if (enabled) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [handleKeyDown, enabled]);
}

// Hook for flow session keyboard shortcuts
export function useFlowSessionShortcuts(
  sessionActions: {
    startSession?: () => void;
    pauseSession?: () => void;
    resumeSession?: () => void;
    completeSession?: () => void;
    cancelSession?: () => void;
    startBreak?: () => void;
    extendSession?: () => void;
  },
  navigationActions: {
    goToStart?: () => void;
    goToActive?: () => void;
    goToSettings?: () => void;
    goToAnalytics?: () => void;
  },
  sessionState: string = 'idle',
  enabled: boolean = true
) {
  const shortcuts: KeyboardShortcut[] = [
    // Session Control
    {
      key: ' ', // Space
      description: 'Start/Pause session',
      action: () => {
        if (sessionState === 'idle' && sessionActions.startSession) {
          sessionActions.startSession();
        } else if (sessionState === 'active' && sessionActions.pauseSession) {
          sessionActions.pauseSession();
        } else if (sessionState === 'paused' && sessionActions.resumeSession) {
          sessionActions.resumeSession();
        }
      },
      enabled: !!(sessionActions.startSession || sessionActions.pauseSession || sessionActions.resumeSession)
    },
    {
      key: ' ', // Ctrl+Space (alternative)
      ctrlKey: true,
      description: 'Start/Pause session',
      action: () => {
        if (sessionState === 'idle' && sessionActions.startSession) {
          sessionActions.startSession();
        } else if (sessionState === 'active' && sessionActions.pauseSession) {
          sessionActions.pauseSession();
        } else if (sessionState === 'paused' && sessionActions.resumeSession) {
          sessionActions.resumeSession();
        }
      },
      enabled: !!(sessionActions.startSession || sessionActions.pauseSession || sessionActions.resumeSession)
    },
    {
      key: 's',
      ctrlKey: true,
      shiftKey: true,
      description: 'Stop/Complete session',
      action: () => sessionActions.completeSession?.(),
      enabled: !!sessionActions.completeSession && sessionState !== 'idle'
    },
    {
      key: 'r',
      ctrlKey: true,
      description: 'Restart session',
      action: () => {
        if (sessionActions.cancelSession && sessionActions.startSession) {
          sessionActions.cancelSession();
          setTimeout(() => sessionActions.startSession?.(), 100);
        }
      },
      enabled: !!sessionActions.cancelSession && !!sessionActions.startSession && sessionState !== 'idle'
    },
    {
      key: 'b',
      ctrlKey: true,
      description: 'Start break',
      action: () => sessionActions.startBreak?.(),
      enabled: !!sessionActions.startBreak && sessionState === 'active'
    },
    {
      key: 'e',
      ctrlKey: true,
      description: 'Extend session',
      action: () => sessionActions.extendSession?.(),
      enabled: !!sessionActions.extendSession && sessionState === 'active'
    },
    {
      key: 'Escape',
      description: 'Cancel current action',
      action: () => sessionActions.cancelSession?.(),
      enabled: !!sessionActions.cancelSession && sessionState !== 'idle'
    },

    // Navigation
    {
      key: '1',
      ctrlKey: true,
      description: 'Go to Start Session',
      action: () => navigationActions.goToStart?.(),
      enabled: !!navigationActions.goToStart
    },
    {
      key: '2',
      ctrlKey: true,
      description: 'Go to Active Session',
      action: () => navigationActions.goToActive?.(),
      enabled: !!navigationActions.goToActive
    },
    {
      key: '3',
      ctrlKey: true,
      description: 'Go to Settings',
      action: () => navigationActions.goToSettings?.(),
      enabled: !!navigationActions.goToSettings
    },
    {
      key: '4',
      ctrlKey: true,
      description: 'Go to Analytics',
      action: () => navigationActions.goToAnalytics?.(),
      enabled: !!navigationActions.goToAnalytics
    }
  ];

  useKeyboardShortcuts(shortcuts, enabled);

  return shortcuts;
}

// Hook for managing global keyboard shortcuts
export function useGlobalShortcuts(enabled: boolean = true) {
  const shortcuts: KeyboardShortcut[] = [
    {
      key: '/',
      ctrlKey: true,
      description: 'Show keyboard shortcuts',
      action: () => {
        // This will be handled by the ShortcutsModal component
        const event = new CustomEvent('show-shortcuts-modal');
        window.dispatchEvent(event);
      }
    },
    {
      key: 'k',
      ctrlKey: true,
      description: 'Quick search',
      action: () => {
        // Focus search input if available
        const searchInput = document.querySelector('input[type="text"]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      }
    }
  ];

  useKeyboardShortcuts(shortcuts, enabled);

  return shortcuts;
}

// Utility function to format shortcut display
export function formatShortcut(shortcut: KeyboardShortcut): string {
  const parts: string[] = [];
  
  if (shortcut.ctrlKey) parts.push('Ctrl');
  if (shortcut.shiftKey) parts.push('Shift');
  if (shortcut.altKey) parts.push('Alt');
  if (shortcut.metaKey) parts.push('Cmd');
  
  // Format key display
  let keyDisplay = shortcut.key;
  if (keyDisplay === ' ') keyDisplay = 'Space';
  if (keyDisplay === 'Escape') keyDisplay = 'Esc';
  if (keyDisplay.length === 1) keyDisplay = keyDisplay.toUpperCase();
  
  parts.push(keyDisplay);
  
  return parts.join(' + ');
}

// Get shortcuts organized by groups
export function getShortcutGroups(
  sessionShortcuts: KeyboardShortcut[],
  globalShortcuts: KeyboardShortcut[]
): ShortcutGroup[] {
  return [
    {
      name: 'Session Control',
      shortcuts: sessionShortcuts.filter(s => 
        ['Start/Pause session', 'Stop/Complete session', 'Restart session', 'Start break', 'Extend session', 'Cancel current action'].includes(s.description)
      )
    },
    {
      name: 'Navigation',
      shortcuts: sessionShortcuts.filter(s => 
        s.description.startsWith('Go to')
      )
    },
    {
      name: 'Global',
      shortcuts: globalShortcuts
    }
  ].filter(group => group.shortcuts.length > 0);
}
