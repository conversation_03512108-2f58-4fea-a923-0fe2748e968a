{"name": "@rhythmai/backend", "version": "1.0.0", "description": "RhythmAI Backend API - Rhythm-aware productivity system", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "migrate": "prisma migrate dev", "migrate:prod": "prisma migrate deploy", "seed": "ts-node prisma/seed.ts", "generate": "prisma generate", "studio": "prisma studio", "docker:build": "docker build -t rhythmai-backend .", "docker:run": "docker run -p 8000:8000 rhythmai-backend"}, "keywords": ["productivity", "circadian-rhythm", "flow-state", "ai", "wellness", "scheduling", "biometrics"], "author": "HectorTa1989", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "@tensorflow/tfjs-node": "^4.15.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "joi": "^17.11.0", "zod": "^3.22.4", "redis": "^4.6.11", "ioredis": "^5.3.2", "bull": "^4.12.2", "node-cron": "^3.0.3", "axios": "^1.6.2", "googleapis": "^129.0.0", "node-fetch": "^3.3.2", "ws": "^8.14.2", "socket.io": "^4.7.4", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "csv-parser": "^3.0.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "lodash": "^4.17.21", "uuid": "^9.0.1", "crypto-js": "^4.2.0", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "stripe": "^14.9.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.10.4", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/passport": "^1.0.16", "@types/passport-google-oauth20": "^2.0.14", "@types/passport-jwt": "^3.0.13", "@types/joi": "^17.2.3", "@types/ws": "^8.5.10", "@types/multer": "^1.4.11", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@types/crypto-js": "^4.2.1", "@types/nodemailer": "^6.4.14", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0", "prisma": "^5.7.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/HectorTa1989/RhythmAI.git", "directory": "backend"}, "bugs": {"url": "https://github.com/HectorTa1989/RhythmAI/issues"}, "homepage": "https://github.com/HectorTa1989/RhythmAI#readme"}