'use client';

import React, { useState } from 'react';
import { Target, Settings, Bar<PERSON>hart3, Clock, HelpCircle } from 'lucide-react';
import { useFlowSessionManager, useSessionPreferences } from '@/hooks/useFlowSession';
import { SessionTimer } from '@/components/flow/SessionTimer';
import { SessionTypeSelector } from '@/components/flow/SessionTypeSelector';
import { SessionStatusIndicator } from '@/components/flow/SessionStatusIndicator';
import { ProgressVisualization } from '@/components/flow/ProgressVisualization';
import { BreakTimer } from '@/components/flow/BreakTimer';
import { AmbientSounds } from '@/components/flow/AmbientSounds';
import { SessionCompletion } from '@/components/flow/SessionCompletion';
import { TaskLinker } from '@/components/flow/TaskLinker';
import { SessionAnalytics } from '@/components/flow/SessionAnalytics';
import { NotificationSettings } from '@/components/flow/NotificationSettings';
import { FocusMode } from '@/components/flow/FocusMode';
import { SessionType, SESSION_TYPES } from '@/types/flow-session';
import { useFlowSessionShortcuts, useGlobalShortcuts, getShortcutGroups } from '@/hooks/useKeyboardShortcuts';
import { GlobalShortcutsModal, ButtonWithShortcut } from '@/components/common/ShortcutsModal';
import { cn } from '@/lib/utils';

type ViewMode = 'start' | 'active' | 'settings' | 'analytics';

export default function FlowSessionPage() {
  const [viewMode, setViewMode] = useState<ViewMode>('start');
  const [selectedSessionType, setSelectedSessionType] = useState<SessionType>(SESSION_TYPES[0]);
  const [linkedTaskId, setLinkedTaskId] = useState<string | null>(null);
  const [environment, setEnvironment] = useState({
    location: 'home-office',
    noiseLevel: 3,
    lighting: 'natural',
    temperature: 22,
    musicType: 'none',
    distractions: []
  });

  const {
    sessionState,
    currentSession,
    startSession,
    pauseSession,
    resumeSession,
    completeSession,
    cancelSession,
    isStarting
  } = useFlowSessionManager();

  const { preferences } = useSessionPreferences();

  // Auto-switch view mode based on session state
  React.useEffect(() => {
    if (sessionState === 'idle') {
      setViewMode('start');
    } else if (sessionState === 'active' || sessionState === 'paused') {
      setViewMode('active');
    }
  }, [sessionState]);

  const handleStartSession = async () => {
    try {
      await startSession(selectedSessionType, environment, linkedTaskId || undefined);
      setViewMode('active');
    } catch (error) {
      console.error('Failed to start session:', error);
    }
  };

  const handleRestartSession = () => {
    setViewMode('start');
  };

  const handleNewSession = () => {
    setViewMode('start');
  };

  const handleExtendSession = (minutes: number) => {
    // Implementation for extending session
    console.log(`Extending session by ${minutes} minutes`);
  };

  // Keyboard shortcuts
  const sessionShortcuts = useFlowSessionShortcuts(
    {
      startSession: viewMode === 'start' ? handleStartSession : undefined,
      pauseSession: sessionState === 'active' ? pauseSession : undefined,
      resumeSession: sessionState === 'paused' ? resumeSession : undefined,
      completeSession: sessionState !== 'idle' ? completeSession : undefined,
      cancelSession: sessionState !== 'idle' ? cancelSession : undefined,
      extendSession: sessionState === 'active' ? () => handleExtendSession(15) : undefined,
    },
    {
      goToStart: () => setViewMode('start'),
      goToActive: sessionState !== 'idle' ? () => setViewMode('active') : undefined,
      goToSettings: () => setViewMode('settings'),
      goToAnalytics: () => setViewMode('analytics'),
    },
    sessionState,
    true
  );

  const globalShortcuts = useGlobalShortcuts(true);
  const shortcutGroups = getShortcutGroups(sessionShortcuts, globalShortcuts);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <Target className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Flow Sessions
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Manage your focused work sessions
                </p>
              </div>
            </div>

            {/* Session Status in Header */}
            <div className="flex items-center space-x-4">
              <SessionStatusIndicator size="sm" showDetails={false} />
              
              {/* Navigation */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setViewMode('start')}
                  className={cn(
                    'p-2 rounded-lg transition-colors',
                    viewMode === 'start'
                      ? 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                  )}
                  title="Start Session"
                >
                  <Target className="w-4 h-4" />
                </button>
                
                <button
                  onClick={() => setViewMode('active')}
                  disabled={sessionState === 'idle'}
                  className={cn(
                    'p-2 rounded-lg transition-colors',
                    viewMode === 'active'
                      ? 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700',
                    sessionState === 'idle' && 'opacity-50 cursor-not-allowed'
                  )}
                  title="Active Session"
                >
                  <Clock className="w-4 h-4" />
                </button>
                
                <button
                  onClick={() => setViewMode('settings')}
                  className={cn(
                    'p-2 rounded-lg transition-colors',
                    viewMode === 'settings'
                      ? 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                  )}
                  title="Settings"
                >
                  <Settings className="w-4 h-4" />
                </button>
                
                <button
                  onClick={() => setViewMode('analytics')}
                  className={cn(
                    'p-2 rounded-lg transition-colors',
                    viewMode === 'analytics'
                      ? 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                  )}
                  title="Analytics"
                >
                  <BarChart3 className="w-4 h-4" />
                </button>

                <button
                  onClick={() => {
                    const event = new CustomEvent('show-shortcuts-modal');
                    window.dispatchEvent(event);
                  }}
                  className="p-2 rounded-lg transition-colors text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                  title="Keyboard Shortcuts (Ctrl + /)"
                >
                  <HelpCircle className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Start Session View */}
        {viewMode === 'start' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Session Setup */}
            <div className="lg:col-span-2 space-y-6">
              <SessionTypeSelector
                selectedType={selectedSessionType}
                onSelect={setSelectedSessionType}
              />

              {/* Environment Setup */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Environment Setup
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Location
                    </label>
                    <select
                      value={environment.location}
                      onChange={(e) => setEnvironment(prev => ({ ...prev, location: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    >
                      <option value="home-office">Home Office</option>
                      <option value="coffee-shop">Coffee Shop</option>
                      <option value="library">Library</option>
                      <option value="coworking">Coworking Space</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Noise Level (1-10)
                    </label>
                    <input
                      type="range"
                      min="1"
                      max="10"
                      value={environment.noiseLevel}
                      onChange={(e) => setEnvironment(prev => ({ ...prev, noiseLevel: parseInt(e.target.value) }))}
                      className="w-full"
                    />
                    <div className="text-sm text-gray-500 dark:text-gray-400 text-center">
                      {environment.noiseLevel}
                    </div>
                  </div>
                </div>

                <ButtonWithShortcut
                  onClick={handleStartSession}
                  disabled={isStarting}
                  shortcut="Space"
                  className="w-full mt-6"
                >
                  <div className="flex items-center space-x-2">
                    <Target className="w-5 h-5" />
                    <span>{isStarting ? 'Starting Session...' : 'Start Flow Session'}</span>
                  </div>
                </ButtonWithShortcut>
              </div>
            </div>

            {/* Right Column - Task Linking, Focus Mode, and Ambient Sounds */}
            <div className="space-y-6">
              <TaskLinker
                selectedTaskId={linkedTaskId}
                onTaskSelect={setLinkedTaskId}
                compact
              />
              <FocusMode />
              <AmbientSounds compact={false} />
            </div>
          </div>
        )}

        {/* Active Session View */}
        {viewMode === 'active' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Timer and Progress */}
            <div className="lg:col-span-2 space-y-6">
              <SessionTimer />
              <ProgressVisualization />
              
              {sessionState === 'completed' && (
                <SessionCompletion
                  onRestart={handleRestartSession}
                  onExtend={handleExtendSession}
                  onNewSession={handleNewSession}
                />
              )}
            </div>

            {/* Right Column - Break Timer and Ambient Sounds */}
            <div className="space-y-6">
              <BreakTimer />
              <AmbientSounds compact />
            </div>
          </div>
        )}

        {/* Settings View */}
        {viewMode === 'settings' && (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                Session Settings
              </h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Timer Configuration
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Default Session Duration (minutes)
                      </label>
                      <input
                        type="number"
                        min="5"
                        max="180"
                        value={preferences.timerConfig.duration}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Short Break Duration (minutes)
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="30"
                        value={preferences.timerConfig.shortBreakDuration}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    Preferences
                  </h3>
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={preferences.timerConfig.autoStartBreaks}
                        className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Auto-start breaks
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={preferences.timerConfig.soundEnabled}
                        className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Enable sound notifications
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={preferences.ambientSoundEnabled}
                        className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Enable ambient sounds
                      </span>
                    </label>
                  </div>
                </div>

                {/* Notification Settings */}
                <NotificationSettings />
              </div>
            </div>
          </div>
        )}

        {/* Analytics View */}
        {viewMode === 'analytics' && (
          <div className="max-w-6xl mx-auto">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Session Analytics
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Track your focus sessions and productivity patterns
              </p>
            </div>
            <SessionAnalytics />
          </div>
        )}
      </div>

      {/* Global Shortcuts Modal */}
      <GlobalShortcutsModal shortcutGroups={shortcutGroups} />
    </div>
  );
}
