import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from 'react-query';
import { toast } from 'sonner';
import {
  userApi,
  energyApi,
  flowApi,
  habitsApi,
  tasksApi,
  coachingApi,
  integrationsApi,
} from '@/services/api';
import {
  User,
  UserPreferences,
  EnergyData,
  EnergyDataRequest,
  FlowSession,
  FlowSessionRequest,
  FlowSessionUpdate,
  Habit,
  HabitRequest,
  HabitCompletionRequest,
  Task,
  TaskRequest,
  DashboardData,
  ApiResponse,
} from '@/types/api';

// Query Keys
export const queryKeys = {
  // User
  user: ['user'] as const,
  userProfile: ['user', 'profile'] as const,
  userPreferences: ['user', 'preferences'] as const,
  userDashboard: ['user', 'dashboard'] as const,
  userStats: (period?: string) => ['user', 'stats', period] as const,

  // Energy
  energy: ['energy'] as const,
  energyData: (params?: any) => ['energy', 'data', params] as const,
  energyPattern: ['energy', 'pattern'] as const,
  energyPredictions: (hours?: number) => ['energy', 'predictions', hours] as const,
  energyInsights: ['energy', 'insights'] as const,
  energyStats: (period?: string) => ['energy', 'stats', period] as const,

  // Flow
  flow: ['flow'] as const,
  flowSessions: (params?: any) => ['flow', 'sessions', params] as const,
  flowActiveSession: ['flow', 'active'] as const,
  flowPattern: ['flow', 'pattern'] as const,
  flowInsights: ['flow', 'insights'] as const,
  flowStats: (period?: string) => ['flow', 'stats', period] as const,

  // Habits
  habits: ['habits'] as const,
  habitsList: (params?: any) => ['habits', 'list', params] as const,
  habitById: (id: string) => ['habits', id] as const,
  habitCompletions: (id: string, params?: any) => ['habits', id, 'completions', params] as const,
  habitStats: (id: string) => ['habits', id, 'stats'] as const,
  habitsInsights: ['habits', 'insights'] as const,
  habitsOverview: ['habits', 'overview'] as const,

  // Tasks
  tasks: ['tasks'] as const,
  tasksList: (params?: any) => ['tasks', 'list', params] as const,
  taskById: (id: string) => ['tasks', id] as const,
  tasksSchedule: ['tasks', 'schedule'] as const,
  tasksScheduleRange: (params?: any) => ['tasks', 'schedule', 'range', params] as const,
  tasksWorkload: (date: string) => ['tasks', 'workload', date] as const,
  tasksStats: (period?: string) => ['tasks', 'stats', period] as const,

  // Coaching
  coaching: ['coaching'] as const,
  coachingDaily: ['coaching', 'daily'] as const,
  coachingWeekly: ['coaching', 'weekly'] as const,
  coachingSessions: (params?: any) => ['coaching', 'sessions', params] as const,
  coachingInsights: ['coaching', 'insights'] as const,
  coachingRecommendations: (params?: any) => ['coaching', 'recommendations', params] as const,
  coachingMetrics: (period?: string) => ['coaching', 'metrics', period] as const,
  coachingSummary: ['coaching', 'summary'] as const,
  coachingProgress: (params?: any) => ['coaching', 'progress', params] as const,

  // Integrations
  integrations: ['integrations'] as const,
  integrationsAvailable: ['integrations', 'available'] as const,
  integrationsStatus: ['integrations', 'status'] as const,
  googleCalendarEvents: (params?: any) => ['integrations', 'google-calendar', 'events', params] as const,
  fitbitActivity: (date: string) => ['integrations', 'fitbit', 'activity', date] as const,
  fitbitSleep: (date: string) => ['integrations', 'fitbit', 'sleep', date] as const,
};

// User Hooks
export const useUserProfile = (options?: UseQueryOptions<ApiResponse<User>>) => {
  return useQuery(queryKeys.userProfile, userApi.getProfile, {
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

export const useUserPreferences = (options?: UseQueryOptions<ApiResponse<UserPreferences>>) => {
  return useQuery(queryKeys.userPreferences, userApi.getPreferences, {
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

export const useDashboard = (options?: UseQueryOptions<ApiResponse<DashboardData>>) => {
  return useQuery(queryKeys.userDashboard, userApi.getDashboard, {
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    ...options,
  });
};

export const useUpdateProfile = (options?: UseMutationOptions<ApiResponse<User>, Error, Partial<User>>) => {
  const queryClient = useQueryClient();
  
  return useMutation(userApi.updateProfile, {
    onSuccess: (data) => {
      queryClient.setQueryData(queryKeys.userProfile, data);
      queryClient.invalidateQueries(queryKeys.userDashboard);
      toast.success('Profile updated successfully');
    },
    ...options,
  });
};

export const useUpdatePreferences = (options?: UseMutationOptions<ApiResponse<UserPreferences>, Error, Partial<UserPreferences>>) => {
  const queryClient = useQueryClient();
  
  return useMutation(userApi.updatePreferences, {
    onSuccess: (data) => {
      queryClient.setQueryData(queryKeys.userPreferences, data);
      toast.success('Preferences updated successfully');
    },
    ...options,
  });
};

// Energy Hooks
export const useEnergyData = (params?: any, options?: UseQueryOptions<ApiResponse<EnergyData[]>>) => {
  return useQuery(queryKeys.energyData(params), () => energyApi.getEnergyData(params), {
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  });
};

export const useCircadianPattern = (options?: UseQueryOptions<ApiResponse<any>>) => {
  return useQuery(queryKeys.energyPattern, energyApi.getCircadianPattern, {
    staleTime: 30 * 60 * 1000, // 30 minutes
    ...options,
  });
};

export const useEnergyPredictions = (hours?: number, options?: UseQueryOptions<ApiResponse<any[]>>) => {
  return useQuery(queryKeys.energyPredictions(hours), () => energyApi.getPredictions(hours), {
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

export const useRecordEnergy = (options?: UseMutationOptions<ApiResponse<EnergyData>, Error, EnergyDataRequest>) => {
  const queryClient = useQueryClient();
  
  return useMutation(energyApi.recordEnergy, {
    onSuccess: () => {
      queryClient.invalidateQueries(queryKeys.energy);
      queryClient.invalidateQueries(queryKeys.userDashboard);
      toast.success('Energy data recorded successfully');
    },
    ...options,
  });
};

// Flow Hooks
export const useFlowSessions = (params?: any, options?: UseQueryOptions<ApiResponse<FlowSession[]>>) => {
  return useQuery(queryKeys.flowSessions(params), () => flowApi.getSessions(params), {
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  });
};

export const useActiveFlowSession = (options?: UseQueryOptions<ApiResponse<FlowSession | null>>) => {
  return useQuery(queryKeys.flowActiveSession, flowApi.getActiveSession, {
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute
    ...options,
  });
};

export const useStartFlowSession = (options?: UseMutationOptions<ApiResponse<FlowSession>, Error, FlowSessionRequest>) => {
  const queryClient = useQueryClient();
  
  return useMutation(flowApi.startSession, {
    onSuccess: () => {
      queryClient.invalidateQueries(queryKeys.flow);
      queryClient.invalidateQueries(queryKeys.userDashboard);
      toast.success('Flow session started');
    },
    ...options,
  });
};

export const useEndFlowSession = (options?: UseMutationOptions<ApiResponse<FlowSession>, Error, { id: string; data: FlowSessionUpdate }>) => {
  const queryClient = useQueryClient();
  
  return useMutation(({ id, data }) => flowApi.endSession(id, data), {
    onSuccess: () => {
      queryClient.invalidateQueries(queryKeys.flow);
      queryClient.invalidateQueries(queryKeys.userDashboard);
      toast.success('Flow session completed');
    },
    ...options,
  });
};

// Habits Hooks
export const useHabits = (params?: any, options?: UseQueryOptions<ApiResponse<Habit[]>>) => {
  return useQuery(queryKeys.habitsList(params), () => habitsApi.getHabits(params), {
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

export const useHabitById = (id: string, options?: UseQueryOptions<ApiResponse<Habit>>) => {
  return useQuery(queryKeys.habitById(id), () => habitsApi.getHabitById(id), {
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

export const useCreateHabit = (options?: UseMutationOptions<ApiResponse<Habit>, Error, HabitRequest>) => {
  const queryClient = useQueryClient();
  
  return useMutation(habitsApi.createHabit, {
    onSuccess: () => {
      queryClient.invalidateQueries(queryKeys.habits);
      queryClient.invalidateQueries(queryKeys.userDashboard);
      toast.success('Habit created successfully');
    },
    ...options,
  });
};

export const useCompleteHabit = (options?: UseMutationOptions<ApiResponse<any>, Error, HabitCompletionRequest>) => {
  const queryClient = useQueryClient();
  
  return useMutation(habitsApi.completeHabit, {
    onSuccess: () => {
      queryClient.invalidateQueries(queryKeys.habits);
      queryClient.invalidateQueries(queryKeys.userDashboard);
      toast.success('Habit completed! 🎉');
    },
    ...options,
  });
};

// Tasks Hooks
export const useTasks = (params?: any, options?: UseQueryOptions<ApiResponse<Task[]>>) => {
  return useQuery(queryKeys.tasksList(params), () => tasksApi.getTasks(params), {
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  });
};

export const useTodaySchedule = (options?: UseQueryOptions<ApiResponse<any>>) => {
  return useQuery(queryKeys.tasksSchedule, tasksApi.getTodaySchedule, {
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
    ...options,
  });
};

export const useCreateTask = (options?: UseMutationOptions<ApiResponse<Task>, Error, TaskRequest>) => {
  const queryClient = useQueryClient();
  
  return useMutation(tasksApi.createTask, {
    onSuccess: () => {
      queryClient.invalidateQueries(queryKeys.tasks);
      queryClient.invalidateQueries(queryKeys.userDashboard);
      toast.success('Task created successfully');
    },
    ...options,
  });
};

export const useUpdateTask = (options?: UseMutationOptions<ApiResponse<Task>, Error, { id: string; data: Partial<TaskRequest> }>) => {
  const queryClient = useQueryClient();
  
  return useMutation(({ id, data }) => tasksApi.updateTask(id, data), {
    onSuccess: () => {
      queryClient.invalidateQueries(queryKeys.tasks);
      queryClient.invalidateQueries(queryKeys.userDashboard);
      toast.success('Task updated successfully');
    },
    ...options,
  });
};

// Coaching Hooks
export const useDailyCoaching = (options?: UseQueryOptions<ApiResponse<any>>) => {
  return useQuery(queryKeys.coachingDaily, coachingApi.getDailyCoaching, {
    staleTime: 60 * 60 * 1000, // 1 hour
    ...options,
  });
};

export const useCoachingInsights = (options?: UseQueryOptions<ApiResponse<any[]>>) => {
  return useQuery(queryKeys.coachingInsights, coachingApi.getInsights, {
    staleTime: 30 * 60 * 1000, // 30 minutes
    ...options,
  });
};

export const useCoachingSummary = (options?: UseQueryOptions<ApiResponse<any>>) => {
  return useQuery(queryKeys.coachingSummary, coachingApi.getSummary, {
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

// Integrations Hooks
export const useIntegrations = (options?: UseQueryOptions<ApiResponse<any[]>>) => {
  return useQuery(queryKeys.integrations, integrationsApi.getIntegrations, {
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

export const useAvailableIntegrations = (options?: UseQueryOptions<ApiResponse<any[]>>) => {
  return useQuery(queryKeys.integrationsAvailable, integrationsApi.getAvailableIntegrations, {
    staleTime: 30 * 60 * 1000, // 30 minutes
    ...options,
  });
};

export const useConnectIntegration = (options?: UseMutationOptions<ApiResponse<void>, Error, { provider: string; code: string }>) => {
  const queryClient = useQueryClient();
  
  return useMutation(
    ({ provider, code }) => {
      if (provider === 'GOOGLE_CALENDAR') {
        return integrationsApi.connectGoogleCalendar(code);
      } else if (provider === 'FITBIT') {
        return integrationsApi.connectFitbit(code);
      }
      throw new Error('Unsupported provider');
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(queryKeys.integrations);
        queryClient.invalidateQueries(queryKeys.integrationsAvailable);
        toast.success('Integration connected successfully');
      },
      ...options,
    }
  );
};
